package com.accolm.licenseManager.DAO;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.Query;
import javax.persistence.TypedQuery;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.core.userdetails.jdbc.JdbcDaoImpl;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;

import com.accolm.licenseManager.Entities.Company;
import com.accolm.licenseManager.Entities.User;
import com.accolm.licenseManager.Utils.LocalEntityManagerFactory;

public class UserDaoImpl implements UserDao {

	Logger logger = LogManager.getLogger(UserDaoImpl.class);
	
	

	public UserDaoImpl() {
	}

	@Override
	public User createUser(User user) {

		EntityManager em = LocalEntityManagerFactory.createEntityManager();

		logger.info("Creating a user...");
		User tempUser = null;
		// save
		try {
			// managed transactions by RESOURCE_LOCAL
			EntityTransaction tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.
		
			
			tempUser =	em.merge(user);

			tx.commit(); // commit the transaction
			logger.info("user created successfully.");
		} catch (Exception e) {
			logger.error("Error creating user: ", e);
		} finally {
			em.close(); // close persistence context.
		}

		return tempUser;

	}

	@Override
	public long getUsersCount() {
		logger.info("Get count of users");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();

		long numberOfUsers = 0;
		EntityTransaction tx = null;

		try {
			tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.

			// Corrected JPQL query with alias
			Query query = em.createQuery("SELECT COUNT(users) FROM User users");

			numberOfUsers = (long) query.getSingleResult(); // Direct cast to long

			tx.commit(); // commit the transaction
			logger.info("Count of users successfully returned");
		} catch (Exception e) {
			if (tx != null && tx.isActive()) {
				tx.rollback(); // rollback in case of error
			}
			logger.error("Error getting count of users ", e);
		} finally {
			if (em.isOpen()) {
				em.close(); // close persistence context
			}
		}

		return numberOfUsers;
	}

}
