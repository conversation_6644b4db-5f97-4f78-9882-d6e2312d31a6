// Maintenance & Support Vue Application
var PaginationStrip = Vue.component("pagination-strip", {
  props: ["currentPage", "pages", "maxPerPage", "itemCount"],
  methods: {
    async onChange(event) {
      const numberToGet = parseInt(event.target.value);
      this.$emit("update:maxPerPage", numberToGet);
      this.$emit("refresh");
    },
    async onNext() {
      if (this.currentPage < this.pages) {
        this.$emit("update:currentPage", this.currentPage + 1);
        this.$emit("refresh");
      }
    },
    async onPrev() {
      if (this.currentPage > 1) {
        this.$emit("update:currentPage", this.currentPage - 1);
        this.$emit("refresh");
      }
    },
    async onFirst() {
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onLast() {
      this.$emit("update:currentPage", this.pages);
      this.$emit("refresh");
    },
  },
  template: `
     <th class="tableHeader" colspan="10" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
      <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
      <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
        <span>Page {{currentPage}} of {{pages}}</span>
        <ul class="pagination pagination-sm" style="margin: 0; border: none;">
          <li :class="{disabled: currentPage === 1}" style="border: none;">
            <span @click="onFirst()" style="border: none;">
              <i class="glyphicon glyphicon-step-backward"></i>
            </span>
          </li>
          <li :class="{disabled: currentPage === 1}" style="border: none;">
            <span @click="onPrev()" style="border: none;">
              <i class="glyphicon glyphicon-chevron-left"></i>
            </span>
          </li>
          <li :class="{disabled: currentPage === pages}" style="border: none;">
            <span @click="onNext()" style="border: none;">
              <i class="glyphicon glyphicon-chevron-right"></i>
            </span>
          </li>
          <li :class="{disabled: currentPage === pages}" style="border: none;">
            <span @click="onLast()" style="border: none;">
              <i class="glyphicon glyphicon-step-forward"></i>
            </span>
          </li>
        </ul>
        <span style="white-space: nowrap;">Results per page : </span>
        <select class="pages-form-control" @change="onChange($event)" style="margin-left: 5px;">
          <option value="10" :selected="maxPerPage === 10">10</option>
          <option value="25" :selected="maxPerPage === 25">25</option>
          <option value="50" :selected="maxPerPage === 50">50</option>
          <option value="100" :selected="maxPerPage === 100">100</option>
        </select>
      </div>
    </th>
  `,
});

// Company autocomplete component
Vue.component("company-autocomplete", {
  props: ["companies", "value"],
  data() {
    return {
      maintenanceItems: [],
      loading: true,
      error: null,
      currentPage: 1,
      pages: 0,
      itemCount: 0,
      maxPerPage: 10,
      filters: {
        company: "",
        licenseKey: "",
        product: "",
        expiryDate: "",
        expiryDateFormatted: "",
      },
      isFiltering: false,
      filteredItems: [],
      selectedCompany: "",
      selectedProduct: "",
      productVersion: "",
      licenseType: "",
      startDate: "",
      endDate: "",
      extendedSupport: false,
      deleteId: 0,
      expiryFilter: "30",
      searchText: "",
      filteredCompanies: [],
      showDropdown: false,
      highlightedIndex: -1,
    };
  },
  mounted() {
    if (this.value) {
      const company = this.companies.find((c) => c.id === parseInt(this.value));
      if (company) {
        this.selectedCompany = company;
        this.searchText = company.name;
      }
    }
  },
  methods: {
    filterCompanies() {
      if (!this.searchText) {
        this.filteredCompanies = [];
        this.showDropdown = false;
        return;
      }

      const searchTerm = this.searchText.toLowerCase();
      this.filteredCompanies = this.companies.filter((company) =>
        company.name.toLowerCase().includes(searchTerm)
      );

      this.showDropdown = this.filteredCompanies.length > 0;
      this.highlightedIndex = -1;
    },
    selectCompany(company) {
      this.selectedCompany = company;
      this.searchText = company.name;
      this.$emit("input", company.id);
      this.$emit("company-selected", company.id);
      this.showDropdown = false;
    },
    onFocus() {
      if (this.searchText) {
        this.filterCompanies();
      }
    },
    onBlur() {
      setTimeout(() => {
        this.showDropdown = false;

        if (this.searchText && !this.selectedCompany) {
          const matchingCompany = this.companies.find(
            (c) => c.name.toLowerCase() === this.searchText.toLowerCase()
          );

          if (matchingCompany) {
            this.selectCompany(matchingCompany);
          } else {
            this.searchText = this.selectedCompany
              ? this.selectedCompany.name
              : "";
          }
        }
      }, 200);
    },
    onKeyDown(e) {
      if (!this.showDropdown) return;

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          this.highlightedIndex = Math.min(
            this.highlightedIndex + 1,
            this.filteredCompanies.length - 1
          );
          this.scrollToHighlighted();
          break;
        case "ArrowUp":
          e.preventDefault();
          this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
          this.scrollToHighlighted();
          break;
        case "Enter":
          e.preventDefault();
          if (this.highlightedIndex >= 0) {
            this.selectCompany(this.filteredCompanies[this.highlightedIndex]);
          }
          break;
        case "Escape":
          e.preventDefault();
          this.showDropdown = false;
          break;
      }
    },
    scrollToHighlighted() {
      this.$nextTick(() => {
        const highlighted = this.$el.querySelector(".highlighted");
        if (highlighted) {
          highlighted.scrollIntoView({
            block: "nearest",
            behavior: "smooth",
          });
        }
      });
    },
    clearSelection() {
      this.searchText = "";
      this.selectedCompany = null;
      this.$emit("input", "");
    },
  },
  template: `
    <div class="company-autocomplete-container" style="position: relative; width: 100%;">
      <div class="input-group" style="width: 56%;">
        <input
          type="text"
          class="form-control"
          v-model="searchText"
          @input="filterCompanies"
          @focus="onFocus"
          @blur="onBlur"
          @keydown="onKeyDown"
          placeholder="Search for a company..."
          style="width: calc(100% - 34px); border-radius: 4px 0 0 4px;"
        >
        <div class="input-group-btn" style="width: 34px; position: absolute; right: 0; top: 0; height: 100%;">
          <button 
            v-if="searchText" 
            class="btn btn-default" 
            type="button" 
            @click="clearSelection"
            style="height: 100%; border-radius: 0 4px 4px 0;"
          >
            <i class="glyphicon glyphicon-remove"></i>
          </button>
          <button 
            v-else 
            class="btn btn-default" 
            type="button"
            style="height: 100%; border-radius: 0 4px 4px 0; cursor: default;"
            disabled
          >
            <i class="glyphicon glyphicon-search"></i>
          </button>
        </div>
      </div>
      <div 
        v-show="showDropdown" 
        class="dropdown-menu" 
        style="display: block; width: 56%; max-height: 300px; overflow-y: auto; position: absolute; z-index: 1000;"
      >
        <a 
          v-for="(company, index) in filteredCompanies" 
          :key="company.id"
          href="#" 
          class="dropdown-item" 
          :class="{ 'highlighted': index === highlightedIndex }"
          @mousedown.prevent="selectCompany(company)"
          @mouseover="highlightedIndex = index"
          style="display: block; padding: 8px 15px; text-decoration: none; color: #333;"
        >
          {{ company.name }}
        </a>
      </div>
    </div>
  `,
});

// Main maintenance component
var MaintenanceComponent = Vue.component("maintenance-component", {
  components: {
    "pagination-strip": PaginationStrip,
    "company-autocomplete": Vue.component("company-autocomplete"),
  },
  data() {
    return {
      maintenanceItems: [],
      companies: [],
      products: [],
      loading: true,
      error: null,
      companyLicenses: [],
      selectedLicense: "",
      currentPage: 1,
      pages: 0,
      itemCount: 0,
      maxPerPage: 10,
      filters: {
        company: "",
        product: "",
        licenseKey: "",
        expiryDate: "",
        expiryDateFormatted: "",
      },
      isFiltering: false,
      filteredItems: [],
      selectedCompany: "",
      selectedProduct: "",
      selectedProductVersion: "",
      productVersion: "",
      selectedLicenseType: "",
      licenseType: "",
      startDate: "",
      endDate: "",
      extendedSupport: false,
      deleteId: 0,
      expiryFilter: "30",
    };
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },

    displayedItems() {
      return this.isFiltering ? this.filteredItems : this.maintenanceItems;
    },
  },
  mounted() {
    this.fetchMaintenanceData();
    this.fetchCompanies();
    this.fetchProducts();
  },
  methods: {
    fetchCompanyLicenses(companyId) {
      if (!companyId) {
        this.companyLicenses = [];
        return;
      }

      axios
        .get(`${this.apiBaseUrl}/licenses/company/${companyId}`)
        .then((response) => {
          this.companyLicenses = response.data || [];
          // Reset selected license when company changes
          this.selectedLicense = "";
          // Reset other form fields that depend on license
          this.selectedProduct = "";
          this.productVersion = "";
          this.selectedLicenseType = "";
        })
        .catch((error) => {
          console.error("Error fetching company licenses:", error);
          this.companyLicenses = [];
        });
    },

    // Handle license selection
    onLicenseSelected() {
      if (!this.selectedLicense) return;

      // Find the selected license in the array
      const license = this.companyLicenses.find(
        (lic) => lic.id === parseInt(this.selectedLicense)
      );

      if (license) {
        // Auto-populate form fields based on the license
        this.selectedProduct = license.product || "";
        this.productVersion = license.productVersion || "";
        this.selectedLicenseType = license.licenseType || "";

        // Set default dates if needed
        const today = new Date().toISOString().split("T")[0];
        if (!this.startDate) this.startDate = today;

        // Set default end date to 1 year from today if not set
        if (!this.endDate) {
          const nextYear = new Date();
          nextYear.setFullYear(nextYear.getFullYear() + 1);
          this.endDate = nextYear.toISOString().split("T")[0];
        }
      }
    },

    fetchMaintenanceData() {
      this.loading = true;

      // First, get the total count of maintenance records
      axios
        .get(`${this.apiBaseUrl}/maintenance-supports/count`)
        .then((countResponse) => {
          // Set the total count from the count API
          this.itemCount = countResponse.data || 0;
          this.pages = Math.ceil(this.itemCount / this.maxPerPage);

          // Then fetch the paginated data
          return axios.get(`${this.apiBaseUrl}/maintenance-supports`, {
            params: {
              page: this.currentPage,
              limit: this.maxPerPage,
            },
          });
        })
        .then((response) => {
          console.log("API Response:", response.data);
          if (response.data && response.data.items) {
            // Map the database fields to our display fields with consistent naming
            const transformedItems = (response.data.items || []).map(
              (item) => ({
                // Use consistent property naming
                id: item.ms_id,
                companyId: item.companyId,
                companyName: item.companyName || `Company #${item.companyId}`, // Use companyName if provided by API
                licenseId: item.license_id || item.licenseId,
                licenseKey: item.licenseKey || "N/A",
                product: item.product || "N/A",
                productVersion: item.productVersion || "N/A",
                licenseType: item.licenseType || "N/A",
                startDate: item.startDate || "",
                endDate: item.endDate || "",
                status: item.status || this.calculateStatus(item.endDate), // Use API status or calculate
                // Ensure consistent boolean representation
                extendedSupport:
                  item.extendedSupport === 1 ||
                  item.extendedSupport === true ||
                  item.extendedSupport === "1",
              })
            );

            this.maintenanceItems = transformedItems;

            // If the count API failed, use the count from the data response
            if (!this.itemCount && response.data.total) {
              this.itemCount = response.data.total;
              this.pages =
                response.data.pages ||
                Math.ceil(this.itemCount / this.maxPerPage);
            }

            // If company names aren't included in the API response, fetch them separately
            if (transformedItems.some((item) => !item.companyName)) {
              this.fetchCompanyNames();
            }
          } else {
            this.maintenanceItems = [];
            if (!this.itemCount) {
              this.itemCount = 0;
              this.pages = 0;
            }
          }
          this.loading = false;
        })
        .catch((error) => {
          console.error("Error fetching maintenance data:", error);
          this.error = "Failed to load maintenance data";
          this.maintenanceItems = [];
          this.itemCount = 0;
          this.pages = 0;
          this.loading = false;
        });
    },

    calculateStatus(endDateStr) {
      if (!endDateStr || endDateStr === "Never") {
        return "Active";
      }

      const endDate = new Date(endDateStr);
      const today = new Date();

      if (endDate < today) {
        return "Expired";
      } else {
        const timeDiff = endDate - today;
        const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));

        if (daysRemaining <= 30) {
          return "Expiring Soon";
        } else {
          return "Active";
        }
      }
    },

    fetchCompanyNames() {
      // Create a Set of unique company IDs to fetch
      const companyIds = new Set(
        this.maintenanceItems.map((item) => item.companyId).filter((id) => id) // Filter out null/undefined
      );

      // If no company IDs, return early
      if (companyIds.size === 0) return;

      // Create a map to store company names by ID
      const companyMap = {};

      // Fetch company details for each ID
      const promises = Array.from(companyIds).map((id) =>
        axios
          .get(`${this.apiBaseUrl}/companies/${id}`)
          .then((response) => {
            if (response.data && response.data.name) {
              companyMap[id] = response.data.name;
            } else {
              companyMap[id] = `Company #${id}`;
            }
          })
          .catch((error) => {
            console.error(`Error fetching company #${id}:`, error);
            companyMap[id] = `Company #${id}`;
          })
      );

      // When all promises resolve, update the maintenance items with company names
      Promise.all(promises)
        .then(() => {
          this.maintenanceItems = this.maintenanceItems.map((item) => ({
            ...item,
            companyName:
              companyMap[item.companyId] || `Company #${item.companyId}`,
          }));
        })
        .catch((error) => {
          console.error("Error fetching company names:", error);
        });
    },

    fetchCompanies() {
      axios
        .get(`${this.apiBaseUrl}/companies`)
        .then((response) => {
          this.companies = response.data;
        })
        .catch((error) => {
          console.error("Error fetching companies:", error);
        });
    },

    fetchProducts() {
      // This would be replaced with actual API endpoint in a real implementation
      setTimeout(() => {
        this.products = [];
      }, 500);
    },

    formatDate(dateString) {
      if (
        !dateString ||
        dateString === "" ||
        dateString === "Never" ||
        dateString === "null"
      ) {
        return "Never";
      }
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return "Never";
        }
        return date.toISOString().split("T")[0];
      } catch (e) {
        return "Never";
      }
    },

    applyFilters() {
      this.isFiltering = true;

      // Start with all maintenance items
      let filtered = [...this.maintenanceItems];

      // Filter by company
      if (this.filters.company) {
        const companySearch = this.filters.company.toLowerCase();
        filtered = filtered.filter(
          (item) =>
            item.company && item.company.toLowerCase().includes(companySearch)
        );
      }

      // Filter by product
      if (this.filters.product) {
        const productSearch = this.filters.product.toLowerCase();
        filtered = filtered.filter(
          (item) =>
            item.product && item.product.toLowerCase().includes(productSearch)
        );
      }

      // Filter by expiry date if provided
      if (this.filters.expiryDate) {
        const filterDate = new Date(this.filters.expiryDate);
        // Set time to end of day for proper comparison
        filterDate.setHours(23, 59, 59, 999);

        filtered = filtered.filter((item) => {
          // Skip items with "Never" expiry
          if (item.endDate === "Never" || !item.endDate) {
            return false;
          }

          const expiryDate = new Date(item.endDate);
          // Item is expiring on or before the filter date
          return expiryDate <= filterDate;
        });
      }

      this.filteredItems = filtered;
    },

    clearFilters() {
      this.filters = {
        company: "",
        product: "",
        expiryDate: "",
        expiryDateFormatted: "",
      };

      this.isFiltering = false;
      this.fetchMaintenanceData(); // Refresh the data
    },

    addMaintenanceSupport() {
      const selectedLicenseObj = this.companyLicenses.find(
        (lic) => lic.id === parseInt(this.selectedLicense)
      );

      // Format the data according to the database schema with consistent naming
      const maintenanceData = {
        companyId: parseInt(this.selectedCompany),
        licenseId: parseInt(this.selectedLicense), // Use camelCase for consistency
        product: this.selectedProduct,
        licenseKey: selectedLicenseObj
          ? selectedLicenseObj.licenseKey || ""
          : "",
        productVersion: this.productVersion,
        licenseType: this.selectedLicenseType,
        startDate: this.startDate,
        endDate: this.endDate,
        status: "ACTIVE", // Default status for new records
        extendedSupport: this.extendedSupport ? 1 : 0, // Convert boolean to integer as expected by backend
      };

      console.log("Sending maintenance data:", maintenanceData);

      // First, ensure all modals are hidden
      $(".modal").modal("hide");

      axios
        .post(`${this.apiBaseUrl}/maintenance-supports`, maintenanceData)
        .then((response) => {
          // API call succeeded
          console.log("Success response:", response);

          // Reset form after submission
          this.resetForm();

          // Show only the success modal
          $("#createSuccessModal").modal({
            backdrop: "static",
            keyboard: false,
          });

          // After a short delay, switch to the Maintenance & Support tab and refresh the data
          setTimeout(() => {
            // Hide the success modal
            $("#createSuccessModal").modal("hide");

            // Switch to the Maintenance & Support tab
            $('a[href="#list"]').tab("show");

            // Reset to first page to ensure new record is visible
            this.currentPage = 1;

            // Refresh the data to include the new record and update count
            this.fetchMaintenanceData();
          }, 1500); // 1.5 second delay
        })
        .catch((error) => {
          // API call failed
          console.error("Error adding maintenance record:", error);
          console.error(
            "Error details:",
            error.response ? error.response.data : "No response data"
          );

          // Show only the error modal
          $("#createErrorModal").modal({
            backdrop: "static",
            keyboard: false,
          });
        });
    },
    resetForm() {
      this.selectedCompany = "";
      this.selectedProduct = "";
      this.productVersion = "";
      this.licenseType = "";
      this.startDate = "";
      this.endDate = "";
      this.extendedSupport = false;
    },

    editItem(item) {
      // This would be implemented with actual edit functionality in a real implementation
      alert("Edit functionality would be implemented here");
    },

    deleteModal(id) {
      this.deleteId = id;
      $("#deleteMaintenanceModal").modal("show");
    },

    deleteMaintenance() {
      // This would be implemented with actual delete API call in a real implementation
      alert("Delete functionality would be implemented here");
      $("#deleteMaintenanceModal").modal("hide");
    },

    renewItem(item) {
      // This would be implemented with actual renewal functionality in a real implementation
      alert("Renewal functionality would be implemented here");
    },

    notifyClient(item) {
      // This would be implemented with actual notification functionality in a real implementation
      alert("Notification functionality would be implemented here");
    },

    getStatusClass(item) {
      if (!item.endDate || item.endDate === "Never") {
        return "label label-success";
      }

      const endDate = new Date(item.endDate);
      const today = new Date();

      if (endDate < today) {
        return "label label-danger";
      } else {
        const timeDiff = endDate - today;
        const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));

        if (daysRemaining <= 30) {
          return "label label-warning";
        } else {
          return "label label-success";
        }
      }
    },

    getStatusText(item) {
      if (!item.endDate || item.endDate === "Never") {
        return "Active";
      }

      const endDate = new Date(item.endDate);
      const today = new Date();

      if (endDate < today) {
        return "Expired";
      } else {
        const timeDiff = endDate - today;
        const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));

        if (daysRemaining <= 30) {
          return "Expiring Soon";
        } else {
          return "Active";
        }
      }
    },
  },

  template: `
<div class="main-section" style="font-family: 'Open Sans', sans-serif;">
    <div class="container">
        <div class="row">
            <div class="col-sm-12" style="text-align: center">
                <h1>Maintenance & Support</h1>
            </div>
            <div class="col-sm-12">
                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs" role="tablist">
                    <li class="active"><a href="#list" role="tab" data-toggle="tab">Maintenance & Support</a></li>
                    <li><a href="#expiry" role="tab" data-toggle="tab">M & S Expiry</a></li>
                    <li><a href="#create" role="tab" data-toggle="tab">Add M & S</a></li>
                </ul>

                <!-- Tabs Content -->
                <div class="tab-content">
                    <!-- List Tab -->
                    <div class="tab-pane active" id="list">
                        <!-- Filter Section -->
                        <div class="panel panel-default" style="margin-bottom: 10px; border: none;">
                            <div class="panel-heading" style="background-color: #f5f5f5; border: none;">
                                <h3 class="panel-title">
                                    <a data-toggle="collapse" href="#filterCollapse">
                                        <i class="glyphicon glyphicon-filter"></i> Filters
                                    </a>
                                </h3>
                            </div>
                            <div id="filterCollapse" class="panel-collapse collapse" style="border: none;">
                                <div class="panel-body" style="padding: 10px; border: none;">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group" style="margin-bottom: 10px;">
                                                <label for="companyFilter" style="font-weight: 600;">Company</label>
                                                <input type="text" id="companyFilter" class="form-control" v-model="filters.company" placeholder="Filter by company">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group" style="margin-bottom: 10px;">
                                                <label for="productFilter" style="font-weight: 600;">Product</label>
                                                <input type="text" id="productFilter" class="form-control" v-model="filters.product" placeholder="Filter by product">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group" style="margin-bottom: 10px;">
                                                <label for="expiryDateFilter" style="font-weight: 600;">Expiring On or Before</label>
                                                <input 
                                                    type="date" 
                                                    id="expiryDateFilter" 
                                                    class="form-control" 
                                                    v-model="filters.expiryDate"
                                                    min="1900-01-01"
                                                    max="2100-12-31">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12" style="text-align: right;">
                                            <button type="button" class="btn btn-default" @click="clearFilters">
                                                <i class="glyphicon glyphicon-refresh"></i> Reset Filters
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                          <table class="table table-hover">
  <thead>
      <tr>
          <pagination-strip
              :current-page.sync="currentPage"
              :pages="pages"
              :max-per-page.sync="maxPerPage"
              :item-count="itemCount"
              @refresh="fetchMaintenanceData"
          ></pagination-strip>
      </tr>
      <tr>
          <th>Company</th>
          <th>License Key</th>
          <th>Product</th>
          <th>Product Version</th>
          <th>License Type</th>
          <th>Start Date</th>
          <th>End Date</th>
          <th>M&S Status</th>
          <th>Extended Support</th>
          <th>Actions</th>
      </tr>
  </thead>
<tbody v-show='!loading'>
  <tr v-for="item in displayedItems" :key="'maintenance-' + item.id">
    <td>{{ item.companyName || 'Loading...' }}</td>
    <td>{{ item.licenseKey }}</td>
    <td>{{ item.product }}</td>
    <td>{{ item.productVersion }}</td>
    <td>{{ item.licenseType }}</td>
    <td>{{ formatDate(item.startDate) }}</td>
    <td>{{ formatDate(item.endDate) }}</td>
    <td>
      <span :class="getStatusClass(item)">{{ item.status }}</span>
    </td>
    <td>
      <input type="checkbox" v-model="item.extendedSupport" disabled>
    </td>
    <td>
      <div class="btn-group">
        <button @click="editItem(item)" class="btn btn-light">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
          </svg>
        </button>
      </div>
    </td>
  </tr>
</tbody>

  <tbody v-show="loading">
      <tr>
          <td colspan="10" class="text-center">
              <p>Loading maintenance data...</p>
          </td>
      </tr>
  </tbody>
  <tfoot>
      <tr>
          <pagination-strip
              :current-page.sync="currentPage"
              :pages="pages"
              :max-per-page.sync="maxPerPage"
              :item-count="itemCount"
              @refresh="fetchMaintenanceData"
          ></pagination-strip>
      </tr>
  </tfoot>
</table>  
                        </div>
                    </div>

                    <!-- Expiry Tab -->
<div class="tab-pane" id="expiry">
  <div class="row">
      
  </div>
  <div class="table-responsive">
      <table class="table table-hover">
          <thead>
              <tr>
                  <pagination-strip
                      :current-page.sync="currentPage"
                      :pages="pages"
                      :max-per-page.sync="maxPerPage"
                      :item-count="itemCount"
                      @refresh="fetchMaintenanceData"
                  ></pagination-strip>
              </tr>
              <tr>
                  <th>Company</th>
                  <th>License Key</th>
                  <th>Product</th>
                  <th>Product Version</th>
                  <th>License Type</th>
                  <th>Start Date</th>
                  <th>End Date</th>
                  <th>M&S Status</th>
                  <th>Extended Support</th>
                  <th>Actions</th>
              </tr>
          </thead>
          <tbody v-show='!loading'>
  <tr v-for="item in displayedItems" :key="'maintenance-' + item.ms_id">
    <td>{{ item.company }}</td>
    <td>{{ item.licenseKey }}</td>
    <td>{{ item.product }}</td>
    <td>{{ item.productVersion }}</td>
    <td>{{ item.licenseType }}</td>
    <td>{{ formatDate(item.startDate) }}</td>
    <td>{{ formatDate(item.endDate) }}</td>
    <td>
      <span :class="getStatusClass(item)">{{ getStatusText(item) }}</span>
    </td>
    <td>
      <input type="checkbox" v-model="item.extendedSupport" disabled>
    </td>
                  <td>
                      <div class="btn-group">
                          <button @click="editItem(item)" class="btn btn-light">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                  <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                              </svg>
                          </button>
                          <button class="btn btn-light" @click="renewItem(item)">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                  <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                                  <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                              </svg>
                          </button>
                          <button class="btn btn-light" @click="notifyClient(item)">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                  <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2zm13 2.383-4.758 2.855L15 11.114v-5.73zm-.034 6.878L9.271 8.82 8 9.583 6.728 8.82l-5.694 3.44A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.739zM1 11.114l4.758-2.876L1 5.383v5.73z"/>
                              </svg>
                          </button>
                      </div>
                  </td>
              </tr>
          </tbody>
          <tbody v-show="loading">
              <tr>
                  <td colspan="10" class="text-center">
                      <p>Loading expiry data...</p>
                  </td>
              </tr>
          </tbody>
          <tfoot>
              <tr>
                  <pagination-strip
                      :current-page.sync="currentPage"
                      :pages="pages"
                      :max-per-page.sync="maxPerPage"
                      :item-count="itemCount"
                      @refresh="fetchMaintenanceData"
                  ></pagination-strip>
              </tr>
          </tfoot>
      </table>
  </div>
</div>


                   <!-- Add Tab -->
<div class="tab-pane" id="create">
  <div class="row">
      <div class="col-sm-2"></div>
      <div class="col-sm-8" style="margin-top: 29px;">
          <form class="form-horizontal" @submit.prevent="addMaintenanceSupport">
              <div class="form-group">
                  <div class="col-sm-3">
                      <label class="control-label">Company</label>
                  </div>
                  <div class="col-sm-6">
                      <company-autocomplete 
                        :companies="companies" 
                        v-model="selectedCompany"
                        @company-selected="fetchCompanyLicenses"
                      ></company-autocomplete>
                  </div>
              </div>

              <div class="form-group">
  <div class="col-sm-3">
    <label class="control-label">License</label>
  </div>
  <div class="col-sm-6">
    <select v-model="selectedLicense" class="form-control" @change="onLicenseSelected" required>
      <option value="">Select License</option>
      <option v-for="license in companyLicenses" :key="license.id" :value="license.id">
        {{ license.licenseKey || 'No Key' }} - {{ license.product }} {{ license.productVersion }}
      </option>
    </select>
  </div>
</div>

              <div class="form-group">
                  <div class="col-sm-3">
                      <label class="control-label">Product</label>
                  </div>
                  <div class="col-sm-6">
                      <select v-model="selectedProduct" class="form-control" required>
                          <option value="">Select Product</option>
                          <option value="Accolm BAM">Accolm BAM</option>
                          <option value="Accolm SSP">Accolm SSP</option>
                      </select>
                  </div>
              </div>

               <div class="form-group">
    <div class="col-sm-3">
        <label class="control-label">Product Version</label>
    </div>
    <div class="col-sm-6">
        <select v-model="productVersion" class="form-control" required>
            <option value="">Select Version</option>
            <option value="7">7</option>
            <option value="6">6</option>
            <option value="5">5</option>
            <option value="4">4</option>
            <option value="3">3</option>
            <option value="2">2</option>
            <option value="1">1</option>
        </select>
    </div>
</div>
                <div class="form-group">
                    <div class="col-sm-3">
                        <label class="control-label">License Type</label>
                    </div>
                    <div class="col-sm-6">
                        <select v-model="selectedLicenseType" class="form-control" required>
                            <option value="">Select License Type</option>
                            <option value="Production">Production</option>
                            <option value="Test">Test</option>
                            <option value="Standby">Standby</option>
                            <option value="Evaluation">Evaluation</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
    <div class="col-sm-3">
        <label class="control-label">Start Date</label>
    </div>
    <div class="col-sm-6">
        <input type="date" class="form-control" v-model="startDate" required>
    </div>
</div>

<div class="form-group">
    <div class="col-sm-3">
        <label class="control-label">End Date</label>
    </div>
    <div class="col-sm-6">
        <input type="date" class="form-control" v-model="endDate" required>
    </div>
</div>

                <div class="form-group">
                    <div class="col-sm-3">
                        <label class="control-label">Extended Support</label>
                    </div>
                    <div class="col-sm-6">
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" v-model="extendedSupport"> Enable Extended Support
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-sm-3"></div>
                    <div class="col-sm-6">
                        <button type="submit" class="request-button form-control" :disabled="!selectedCompany || !selectedProduct">
                            Add M & S
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-sm-2"></div>
    </div>
</div>
                  </div> <!-- End of tab-content -->
              </div> <!-- End of col-sm-12 -->
          </div> <!-- End of row -->
      </div> <!-- End of container -->

      <!-- Error Modal for create licenses page -->
<div class="modal fade" id="createErrorModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p>Failed to add Maintenance & Support record. Please try again.</p>
            </div>
        </div>
    </div>
</div>

      <!-- Delete Confirmation Modal -->
      <div class="modal fade" id="deleteMaintenanceModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Confirm Delete</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <p>Are you sure you want to delete this maintenance record?</p>
                      <div class="mt-3">
                          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                          <button type="button" class="btn btn-danger" @click="deleteMaintenance">Delete</button>
                      </div>
                  </div>
              </div>
          </div>
      </div>

      <!-- Success Modal for create licenses page -->
<div class="modal fade" id="createSuccessModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>Operation completed successfully!</p>
            </div>
        </div>
    </div>
</div>

      <!-- Success Modal -->
      <div class="modal fade" id="successModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Success</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                      <p>Operation completed successfully!</p>
                  </div>
              </div>
          </div>
      </div>

      <!-- Error Modal -->
      <div class="modal fade" id="errorModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Error</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                      <p>An error occurred. Please try again.</p>
                  </div>
              </div>
          </div>
      </div>
  </div>
  `,
});

// Initialize the Vue application
new Vue({
  el: "#maintenance",
  components: {
    "maintenance-component": MaintenanceComponent,
  },
  template: `
    <div class="wrapper">
      <maintenance-component></maintenance-component>
    </div>
  `,
});
