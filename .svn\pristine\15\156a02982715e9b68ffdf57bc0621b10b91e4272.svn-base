
/* @font-face { font-family: "Open Sans Regular"; src: url('OpenSans/OpenSans-Regular.ttf'); }
@font-face { font-family: "Open Sans Bold"; src: url('OpenSans/OpenSans-Bold.ttf'); }
@font-face { font-family: "Open Sans BoldItalic"; src: url('OpenSans/OpenSans-BoldItalic.ttf'); }
@font-face { font-family: "Open Sans ExtraBold"; src: url('OpenSans/OpenSans-ExtraBold.ttf'); }
@font-face { font-family: "Open Sans ExtraBoldItalic"; src: url('OpenSans/OpenSans-ExtraBoldItalic.ttf'); }
@font-face { font-family: "Open Sans Italic"; src: url('OpenSans/OpenSans-Italic.ttf'); }
@font-face { font-family: "Open Sans Light"; src: url('OpenSans/OpenSans-Light.ttf'); }
@font-face { font-family: "Open Sans LightItalic"; src: url('OpenSans/OpenSans-LightItalic.ttf'); }
@font-face { font-family: "Open Sans Semibold"; src: url('OpenSans/OpenSans-Semibold.ttf'); }
@font-face { font-family: "Open Sans SemiboldItalic"; src: url('OpenSans/OpenSans-SemiboldItalic.ttf'); }


@font-face { font-family: "Nimbus Sans Regular"; src: url('nimbus-sans-l/NimbusSanL-Reg.html'); }


@font-face { font-family: "Roboto Condensed Bold"; src: url('RobotoCondensed/RobotoCondensed-Bold.html'); }
@font-face { font-family: "Roboto Condensed BoldItalic"; src: url('RobotoCondensed/RobotoCondensed-BoldItalic.html'); }
@font-face { font-family: "Roboto Condensed Italic"; src: url('RobotoCondensed/RobotoCondensed-Italic.html'); }
@font-face { font-family: "Roboto Condensed Light"; src: url('RobotoCondensed/RobotoCondensed-Light.html'); }
@font-face { font-family: "Roboto Condensed LightItalic"; src: url('RobotoCondensed/RobotoCondensed-LightItalic.html'); }
@font-face { font-family: "Roboto Condensed Regular"; src: url('RobotoCondensed/RobotoCondensed-Regular.html'); }


@font-face { font-family: "Roboto Black"; src: url('Roboto/Roboto-Black.html'); }
@font-face { font-family: "Roboto Black Italic"; src: url('Roboto/Roboto-BlackItalic.html'); }
@font-face { font-family: "Roboto Bold"; src: url('Roboto/Roboto-Bold.html'); }
@font-face { font-family: "Roboto Bold Italic"; src: url('Roboto/Roboto-BoldItalic.html'); }
@font-face { font-family: "Roboto Italic"; src: url('Roboto/Roboto-Italic.html'); }
@font-face { font-family: "Roboto Light"; src: url('Roboto/Roboto-Light.html'); }
@font-face { font-family: "Roboto Light Italic"; src: url('Roboto/Roboto-LightItalic.html'); }
@font-face { font-family: "Roboto Medium"; src: url('Roboto/Roboto-Medium.html'); }
@font-face { font-family: "Roboto Medium Italic"; src: url('Roboto/Roboto-MediumItalic.html'); }
@font-face { font-family: "Roboto Regular"; src: url('Roboto/Roboto-Regular.html'); }
@font-face { font-family: "Roboto Thin"; src: url('Roboto/Roboto-Thin.html'); }
@font-face { font-family: "Roboto Thin Italic"; src: url('Roboto/Roboto-ThinItalic.html'); }
 
 */
body{
	margin: 0px;
	padding: 0px;
	font-family: "Open Sans Regular";
	font-size: 14px;
	font-weight:400px;
	word-spacing:2px;
	line-height:30px;
	color: #000;
	width:100%;
}
#index_body{
	overflow-x: hidden;
}
h1, h2, h3, h4, h5, h6{
	font-family: "Open Sans Semibold";
    /*font-size: 32px;
    font-weight: bold;*/
    line-height: 1.2;
    /*
	font-family:'Open Sans Semibold';
	font-weight: normal;
	text-transform: none;*/
	color:#656565;
	letter-spacing: 0px;
}

h1{
	line-height:60px;
	font-size: 32px;
	/*margin: 40px,0px,40px, 0px;*/
}
	
h2{
	font-size:18px;
	line-height:25px;
	margin-top:25px;
	margin-bottom:15px;
}
h3{
	font-size: 14px;
	line-height:25px;
	margin-top:35px;
}
h4{
	font-size: 14px;
	line-height:25px;
	margin-top:0px;
}

label{
	font-family: "Open Sans";
	color: #656565
}

/********* dropdown menu **********/

.outer-wrapper{
	width:100%;
	margin:0px auto;
}
.inner-wrapper{
	width:100%;
	margin:20px auto;
}

.menu-outlook{
	margin-bottom: 15px;
}

hr.thin {
	height: 1px;
	border: 0;
	color: #333;
	background-color: #BDBDBD;
}

#logo_image{
	clear:none;
	float:left;
	outline: none;
	border : 0;
}

#clear{
	clear:both;
}

.centerblock {
	width: 100% ;
	margin-left: auto;
	margin-right: auto;
	text-align: center;
}

/*--------------------Sliding down sub menu---------------------------*/
.slide-menu{
	width: 100%;
	color: #5a4d4d;
	position: absolute;
	z-index: 10;
}

.submenu-wrapper{	
	background-color: #fbfbfb;
	color: #5a4d4d;
	border-radius: 5px;
	border: 1px solid #dedede;
	border-top: 2px solid #d82229;
	border-bottom: 2px solid #eaeaea;
	margin-top: -2px;
	padding-left: 20px;
	padding-right: 20px;
	padding-bottom: 30px;
}

.menu-drop-panel{
	/*
	background-color: #fff;
	color: #5a4d4d;
	border-radius: 5px;
	border: 1px solid #dedede;
	border-top: 2px solid #d82229;
	margin-top: -2px;
	padding-left: 20px;
	padding-right: 20px;
	padding-bottom: 30px;*/
}

.menu-drop-panel h2{
	font-size: 18px;
	font-family: "Open Sans Semibold";
	color: #323232;
}

.menu-drop-panel h4{
	font-size: 14px;
	font-family: "Open Sans Light";
	font-weight: bold;
	color: #545454;
	letter-spacing: 1px;
}

.sub_menu_list li{
	margin-bottom: 0px;
	margin-top: 2px;
}

.sub_menu_list li:hover{

}


ul.sub_menu_list {
	font-size:13px;
	font-weight:normal;
	word-spacing:1px;
	margin-bottom:5px;
	margin-top:12px;
	margin-left:-25px;
	list-style:none;
	margin-top:3px;	
}
ul.sub_menu_list li {
}
ul.sub_menu_list li a {
	color:#585656;
	text-decoration:none;
}
ul.sub_menu_list li a:hover {
	color:#585656;
	text-decoration:underline;
}


/*--------------------------Start scrolling logo caurosel--------------------------*/
#carousel_container{
	width:1230px;  /*rcc*/
	overflow:hidden;
}
#carousel_inner {
	float:left; /* important for inline positioning */
	width:85%; /* important (this width = width of list item(including margin) * items shown */  /*rcc*/
	overflow: hidden;
	margin-right:4px;  /* important (hide the items outside the div) */ /*rcc*/
	/* non-important styling bellow 
	background: #F0F0F0;*/
}
#carousel_ul {
	position:relative;
	left:-80px; /* important (this should be negative number of list items width(including margin) */
	list-style-type: none; /* removing the default styling for unordered list items */
	margin: 0px;
	padding: 0px;
	margin-left:-50px;
	width:9999px; /* important */
	/* non-important styling bellow */
	padding-bottom:10px;
}
#carousel_ul li{
	float: left; /* important for inline positioning of the list items */                                    
	width:120px;  /* fixed width, important */
	/* just styling bellow*/
	padding:0px;
	height:67px;
	margin-top:10px;
	margin-bottom:10px; 
	margin-left:5px; 
	margin-right:5px; 
}
#carousel_ul li img {
	.margin-bottom:-4px; /* IE is making a 4px gap bellow an image inside of an anchor (<a href...>) so this is to fix that*/
	/* styling */
	cursor:pointer;
	cursor: hand; 
	border:0px; 
	width:120px;
}
#left_scroll, #right_scroll{
	float:left; 
	height:67px; 
	width:30px; 
}
#left_scroll img, #right_scroll img{
	/*styling*/
	cursor: pointer;
	cursor: hand;
}
/*--------------------------End scrolling logo caurosel--------------------------*/

.main_section_left_img{
	width:500px;
	height:200px;
	overflow:hidden;
	/*margin-right:30px;*/
	/*float:left;*/
	/*padding:10px 0px;*/
	display: block;
    margin: 0 auto;
	text-align:center;
}

.product_bam_section_img{
	width: 100%;
	height:250px;
	overflow:hidden;
	display: block;
    margin: 0 auto;
	text-align:center;
}

.tab_content{
	padding-top:15px;
}

.topic_section{
	margin-top: 20px;
	margin-bottom: 10px;
}

.sub_topic_section{
	margin-top: 5px;
	margin-bottom: 5px;
}

.header_section{
	margin-top: 30px;
}

.first_strip_section{
	margin-top: 20px;
}

.footer{
	padding:0px;
	background-color:#4d4d4d;
	clear:both;
	padding-top:20px;
	margin-top:100px;
	font-size:13px;
	color:#fff;
	display:block;
}

#footer a{
	font-size:13px;
	color:#fff;

}

#footer-contents{
	padding:0px;
	background-color:#4d4d4d;
	clear:both;
	padding-top:20px;
	margin-top:0px;
	font-size:13px;
	color:#fff;
	display:block;
	width:100%;  /*rcc*/
}

.tab_section{
}

.tab_content_section{
}

.section_division{

}

.odd_row_section{
	background-color: #FFFFFF;
	padding-top: 15px;
	padding-bottom: 15px;
}

.even_row_section{
	background-color: #FAFAF0;
	padding-top: 15px;
	padding-bottom: 15px;
}

.main_row_section{
	margin-bottom: 0px;
}

.request_box{
	border: 2px solid #EEEEEE;
	border-radius:5px;
	height: 100%;
	background-color: #fff;
	border-top: 4px solid #EEAA00;
	padding: 10px;
	text-align: center;
	margin-top: 5px;
	margin-left: auto;
	margin-right: auto;
	max-width: 500px;
}

.request_box > h1,
.request_box > h2,
.request_box > h3,
.request_box > h4,
.request_box > h5,
.request_box > h6 {
	margin-top: 5px;
	margin-bottom: 10px;
}

.btn-outline-danger.focus, .btn-outline-danger:focus {
    color: #fff;
    background-color: #d9534f;
    border-color: #d9534f;
}

.btn-outline-danger {
    color: #d9534f;
    background-image: none;
    background-color: transparent;
    border-color: #d9534f;
	outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color;
}

section{
	line-height:25px;
	font-size: 14px;
    line-height: 30px;
    font-weight: normal;
    color: #000;
}
section p{
	width:500px;
	float:right;
	margin-right:30px;
	margin-top:-10px;
}
#section_one{
	width:310px;
	display:block;
	float:left;
}
#section_two{
	width:310px;
	margin:0px 35px;
	display:block;
	float:left;
}
#sub_section_one{
	/*width:485px;*/
	margin-right:30px;
	display:block;
	float:left;
	line-height:18px;
	margin-bottom:18px;
}
#sub_section_one_txt{
	/*width:304px;*/
	float:left;
	line-height:18px;
	margin-left:70px;
}
#sub_section_two{
	width:485px;
	display:block;
	float:left;
	line-height:18px;
	margin-bottom:18px;
}
#sub_section_two_txt{
	width:290px;
	float:left;
	line-height:18px;
	margin-left:70px;
}
#section_two_scontainer{
	float:left;
	width:1000px;
}
#section_left{
	width:650px;
	float:left;
	margin-right:50px;
}
#section_contact_info{
	width:500px;
	float:left;
}
#section_contact{
	width:370px;
	padding:20px 0px;
	float:left;
	margin-right:60px;
}
#section_contact input[type=text] {
	padding:0px 5px;
	width:330px;
	height:30px;
	font-size:14px;
	/*color:#babab9;*/
	margin-bottom:10px;
	border:solid 1px #b3b3b3;
}
#section_contact select{
	padding:5px 5px;
	width:340px;
	/*height:30px;*/
	font-size:14px;
	color:#babab9;
	margin-bottom:10px;
	border:solid 1px #b3b3b3;
}
#section_contact textarea{
	padding:0px 5px;
	width:330px;
	height:150px;
	font-size:14px;
	color:#babab9;
	margin-bottom:10px;
	border:solid 1px #b3b3b3;
}
#section_contact button{
	background-color:#fdad35;
	padding:0px 5px;
	width:345px;
	height:34px;
	font-size:16px;
	color:#fff;
	margin-bottom:19px;
	border:solid 1px #fdad35;/** #d82229 accolm red**/
	cursor:pointer;
}

a{
	color:#000;
	text-decoration:none;
	border:none;
}
a:hover{
	color:#d82229;
	text-decoration:underline;
}
.centerblock {
	width: 100% ;
	margin-left: auto;
	margin-right: auto;
	text-align: center;
}

.row > h2 {
	padding-left: 15px;
}

/**** Customize picture section ****//*
#midsection {
  -webkit-clip-path: inset(0px 400px 1200px 10px);
  clip-path: inset(0px 400px 1200px 10px);
}

.carousel-inner > .item{
	display: inline-block;
}*/
/***** End customizing *********/

/***** Bootstrap customization *******/
.navbar-brand{
	padding: 5px 5px;
}

.h1, h1 {
    font-size: 28px;
}

.container {
	padding-right: 35px;
	padding-left: 35px;
	margin-right: auto;
	margin-left: auto;
}

.form-group {
  margin-bottom: 18px;
}

/*
.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus {
    background-color: #fff;
	color:#d82229; 
	border-bottom:solid 2px #d82229;
}

.nav-pills {
	color:#5a4d4d;
	font-size: 12px;
}

.nav-pills > li > a:hover {
	color:#5a4d4d;
	border-bottom:solid 2px #d82229;
}

.nav-pills > li > a {
	border-radius: 0px;
	border-bottom:solid 2px #fff;
	color: #656565;
}

.nav > li > a {
	font-size: 15px;
	position: relative;
	display: block;
	padding: 12px 10px 0px 10px;
	border-bottom:solid 2px #fff;
}

.nav > li > a:focus {
	background-color: #fff;
}

.nav > li > a:hover{
	text-decoration: none;
	background-color: #fbfbfb;
	border-bottom:solid 2px fff;
	margin-top: 10px;
	padding-top: 0px;
	border-top: 2px solid #d82229;
}

.nav .open > a:focus {
	background-color: #fff;
}

.nav .open > a{
	background-color: #fff;
	border-bottom: 2px solid #fff !important;
}

.nav .open > a:hover{
	background-color: #fbfbfb;
	border-color: #d82229;
	border-bottom:solid 2px #d82229 !important;
	margin-top: 10px;
	padding-top: 0px;
	border-top: 2px solid #d82229;
}
*/

/** carousel **/
.carousel-control:hover,
.carousel-control:focus {
	color: #fff;
	text-decoration: none;
	filter: alpha(opacity=100);
	outline: 0;
	opacity: 1;
}

.carousel-control {
	  position: absolute;
	  top: 125px;
	  bottom: auto;
	  left: 0;
	  width: 15%;
	  font-size: 20px;
	  color: #fff;
	  text-align: center;
	  text-shadow: 0 1px 2px rgba(0, 0, 0, .6);
	  filter: alpha(opacity=100);
	  opacity: .5;
	  height: 50px;
	  width: 50px;
}

.carousel-control.left {
	background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	background-image:      -o-linear-gradient(left, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0)));
	background-image:         linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#00000000', GradientType=1);
	background-repeat: repeat-x;
}
.carousel-control.right {
	  right: 0;
	  left: auto;
	  background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	  background-image:      -o-linear-gradient(left, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	  background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0)));
	  background-image:         linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#00000000', GradientType=1);
	  background-repeat: repeat-x;
}

.thumbnail > img,
.thumbnail a > img,
.carousel-inner > .item > img,
.carousel-inner > .item > a > img {
	  display: block;
	  max-width: 100%;
	  height: 300px;
}

/*********************/
.row-top-buffer { 
	margin-top:20px; 
}


.column-top-buffer { 
	
	padding-right:10px; 
}

/********* background colours **********/
.light-gray {
    background: #F6F6F6 none repeat scroll 0% 0%;
}

.product-box { 
    display:inline-block;
    position:relative;
	width: 100%;
}

.urun-title {
    position: absolute;
    bottom: 80px;
    text-align: left;
    z-index: 15;
    padding-top: 5px;
    padding-bottom: 5px;
    color: #fff;
    padding-left: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .6);
    background-color: rgba(194, 0, 0, 0.93);
    width: 100%;
}

.urun-text-old {
    position: absolute;
    background-color: rgba(15, 15, 15, 0.93);
    color: #fff;
    bottom: 0px;
    text-align: left;
    padding: 200px 15px 20px 15px;
    width:100%;
	opacity:0.25;
	font-weight: bold;
}

.urun-text {
	color: #D3D3D3;
	color: #fff;
	font-size: 2.0rem;
    float: none;
    width: 100%;
    position: absolute;
    left: 0px;
    right: 0px;
    margin: 0px;
    padding: 5% 0.75rem 0.75rem;
    background-color: transparent;
    background-image: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    bottom: -1px;
}

.header-section{
	padding-top: 5px;
	margin-bottom: 0px;
}
.top_bar_container{
	padding-right: 60px;
}
.top_bar_container > a{
	font-size: 13px;
}
.nav-menu{
	font-family: 'Open Sans Semibold';
	letter-spacing: 0px;
	font-size: 20px;
	/*font-weight: bold;*/
	background-color: #ffffff !important;	
	margin-bottom: 0px;
	border-color: #fff;
	border: 0px;
}

.navbar-collapse{
	margin-left: -10px !important;
	padding-left: 0px;
}

.logo-icon{
	width: 162px;
	height: 45px;
	margin-top: 5px;
}

.page-top{
	margin-top: 0px;
	margin-bottom: 15px;
	padding-top: 10px;
	padding-bottom: 10px;
}

.navbar-content{
	margin-top: 15px;
	float: right;
}

.banner-section{
	width: 100%;
	height: auto;
	margin-top: 20px;
	position: block;
	background-color: #f2f2f2;
	padding: 10px 0px;
}

.banner-text{
	display: block; 
	font-size: 32px; 
	text-align: center; 
	color: #656565;
	margin-bottom: 25px;
	line-height: 1.2;
}

.banner-text-small{
	display: block; 
	font-size: 20px; 
	text-align: center; 
	color: #656565;
	margin-bottom: 15px;
}

.banner-image{
	text-align: left;
	margin: 12px 0px;
}

.banner-div{
	height: auto;
}

.banner-text-region{
	max-width: 517px;
    max-height: 200px;
    margin: 12% auto 5% 30%;
}

.home-image{
	width: auto;
	display: block;
	/*position: absolute;*/
	height: 280px;
}

.overlay-text-title{
	display: block; 
	font-size: 22px; 
	text-align: center; 
	color: #ca2f45;
	margin-bottom: 20px;
}

.overlay-text-big{
	display: block; 
	font-size: 30px; 
	text-align: center; 
	color: #a0224c;
	margin-bottom: 20px;
}

.overlay-text-bottom{
	display: block; 
	font-size: 20px; 
	text-align: center; 
	color: #ca2f45;;
	margin-bottom: 20px;
}

h1.row{
	margin-left: 0px;
}

.right-main h2{
	margin-top: 0px;
}

.top-actions{
	margin-top: 15px;
}

.top-left-image-section{
	padding-left: 0px;
	text-align: left;
	margin-left: -12px;
}

ul.main-right-list{
	margin-left: -10px !important;
} 

.section-title{
	margin-left: 0px;
}

.make-request-left{
	text-align: right;
}

.make-request-right{
	text-align: left;
}

.section-title h1{
	margin-top: 10px;
	margin-bottom: 20px;
}

.request-row{
	margin-top: 10px;
	text-align: center;
	display: none;
}

.request-button {
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    color: #d82229;
    background-color: transparent;
    border-color: #d43f3a;
    border: 1px solid #d82229;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.search-button {
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    color: #d82229;
    background-color: transparent;
    border-color: #d43f3a;
    border: 1px solid #d82229;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    border-radius: 0px 4px 4px 0px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.request-button:hover, .request-button:focus, .request-button.focus, .search-button:hover, .search-button:focus, .search-button.focus {
    color: #fff;
    background-color: #c9302c;
    border-color: #ac2925;
	text-decoration: unset;
}

.request-form{
	max-width: 500px;
}

.special-fields{
	padding: 15px 5px;
	border-radius: 5px;
	background-color: #fcfcfc;
}

.asterics{
	color: #ff0000;
	margin-left: 4px;
}

.modal-title{
	margin-top: 5px;
	margin-bottom: 5px;
	text-align: center;
}

.home-request{
	margin-bottom: 30px;
}

.home-button{
	border: 2px solid #8c2012;
	border-radius: 0px !important;
	background-color: transparent;
	border-color: #8c2012;
	color: #fff;
}

.home-button:hover{
	background-color: #fffafa;
	color: #8c2012;
}
.content_spacing {
	margin-bottom: 60px !important;
}
.content_title{
	color: #000;
}

@media (min-width: 900px){
	.nav-pills > li.active > a,
	.nav-pills > li.active > a:hover,
	.nav-pills > li.active > a:focus {
		background-color: #fff;
		color:#d82229; 
		border-bottom:solid 2px #d82229;
	}

	.nav-pills {
		color:#5a4d4d;
		font-size: 12px;
	}

	.nav-pills > li > a:hover {
		color:#5a4d4d;
		border-bottom:solid 2px #d82229;
	}

	.nav-pills > li > a {
		border-radius: 0px;
		border-bottom:solid 2px #fff;
		color: #656565;
	}

	.nav > li > a {
		font-size: 15px;
		position: relative;
		display: block;
		/* padding: 12px 10px 0px 10px; */
		/* border-bottom:solid 2px #fff; */
	}

	/* .nav > li > a:focus {
		background-color: #fff;
	} */

	/* .nav > li > a:hover{
		text-decoration: none;
		background-color: #fbfbfb;
		border-bottom:solid 2px fff;
		margin-top: 10px;
		padding-top: 0px;
		border-top: 2px solid #d82229;
	}
 */
	/* .nav .open > a:focus {
		background-color: #fff;
	}

	.nav .open > a{
		background-color: #fff;
		border-bottom: 2px solid #fff !important;
	}

	.nav .open > a:hover{
		background-color: #fbfbfb;
		border-color: #d82229;
		border-bottom:solid 2px #d82229 !important;
		margin-top: 10px;
		padding-top: 0px;
		border-top: 2px solid #d82229;
	} */
}

@media (max-width: 1200px) {
	.main_section_left_img{
		width: 100%;
		height: auto;
	}
	
	.top-left-image-section{
		margin-left: 0px;
	}
	
	.section-title{
		margin-left: -15px;
	}
	
	#carousel_container{
		width:100%;  /*rcc*/
	}

	.product_bam_section_img{
		width: 100%;
		height: auto;
	}

}

}

@media (max-width: 1000px) {
	.navbar-nav {
	    margin: 7.5px -15px 7.5px 0;
	}
	
	.banner-text{
		font-size: 28px;
	}
	
	.banner-text-small{
		font-size: 18px;
	}

	.banner-text-region{
		max-width: 517px;
		max-height: 170px;
		overflow: hidden;
	}
}

@media (max-width: 991px) {
	.banner-image{
		text-align: right;
		margin: 5px auto;
	}
	
	.banner-image img{
		height: 200px;
		width: auto;
	}

	.banner-text-region{
		max-width: 517px;
		max-height: 170px;
		overflow: hidden;
		margin: 5% auto 1% 12%;
	}
	
	.banner-text{
		font-size: 24px;
	}
	
	.banner-text-small{
		font-size: 16px;
	}
}

@media (max-width: 900px){

	.nav-menu{
		font-size: 16px;
	}
	
	.nav-stacked > li {
		border-bottom: 1px solid #dadada;
		margin-top: 0px !important;
	}
	
	.dropdown-sub{
		background: #fbfbfb !important;
	}
	
	 .sub-menu-links{
		 margin-left: 10px;
	 }
	 
	 .caret{
		 float: right;
	 }
}
 
@media (max-width: 767px){
	.right-main h2{
		margin-top: 30px;
	}
	
	.section-image{
		margin-left: -12px;
	}
	
	.right-main{
		padding-left: 0px;
	}
	
	.request-row{
		display: block;
	}
	
	.main-section h1{
		text-align: center;
	}
	
	.banner-image{
		text-align: center;
		margin: 5px auto;
	}
	
	.banner-image img{
		height: 200px;
		width: auto;
	}

	.banner-text-region{
		width: auto;
		height: auto;
		margin: 5px auto;
		max-height: unset;
		max-width: unset;
	}
	
	.banner-text{
		font-size: 24px;
		margin-left: 5px;
		margin-right: 5px;
	}
	
	.banner-text-small{
		font-size: 16px;
	}
}

@media (max-width: 600px) {
	/*
	.nav > li > a{
		font-size: 12px;
	}*/
	
	.navbar-collapse{
		margin-left: 0px !important;
	}

	.overlay-text-big{
		display: none;
	}
	
	#main_section_left_img{
		width: 360px;
		height: 360px;
	}
	
	.home-image{
		width: 100%;
		height: 200px;
	}
	
	.banner-section{
		width: 100%;
		height: 200px;
	}
	
	.iframe-responsive{
		width: 100%;
		height: 250px;
	}
	
	.map-responsive{
		width: 100%;
		height: 250px;
	}

	.product_bam_section_img{
		width: 100%;
		height: auto;
	}
}

@media (max-width: 500px) {
	.banner-section{
		width: 100%;
		height: auto;
	}
	
	.home-image{
		width: 100%;
		height: 180px;
	}
		
	.overlay-text-bottom{
		display: none;
	}
	
	.banner-text-small{
		font-size: 14px;
	}
	
}
.table.table-borderless td, .table.table-borderless th{
    border: 0;
}

/**** Knowledge base ****/
.article-right-container{
	padding-right: 50px !important;
}

#article_top_container{
	margin-bottom: 80px !important;
}

.side_content_container{
	margin-top: 0px !important;
}
#side_content_container_top{
	margin-bottom: 50px;
}
.bottom-margin li{
	margin-bottom: 10px;
}
.h-heading{
	margin: 0px;
	padding: 0px;
}
.h-heading.header-space{
	margin-bottom: 30px;
}
.main-container-spacing{
	margin-top: 30px;
}
.search_form_container{
	padding-top: 20px;
}

/*Overriding boostrap pagination class */
/* .pagination > li > a, .pagination > li > span{
	border: 0px !important;
}
.pagination > li:first-child > a, .pagination > li:first-child > span{
	border-bottom-right-radius: 0px !important;
	border-top-right-radius: 0px !important;
}
.pagination > li > a, .pagination > li > span {
	margin-left: 5px;
}
.pagination > li:last-child > a, .pagination > li:last-child > span {
	border-bottom-right-radius: 0px !important;
	border-top-right-radius: 0px !important;
}
.pagination > .active > a, .pagination > .active > span, .pagination > .active > a:hover, .pagination > .active > span:hover, .pagination > .active > a:focus, .pagination > .active > span:focus{
	background-color: #d82229;
	border-color: #d82229;
}
.pagination > li > a, .pagination > li > span{
	color: #ababab;
	font-weight: bold;
}
.article-container > .article_show_all{
	margin-left: 8px;

} */

.custom_breadcrumb {
  padding: 8px 15px;
  margin-bottom: 0px;
  list-style: none;
  font-size: 13px;

}
.custom_breadcrumb > li {
  display: inline-block;

}
.custom_breadcrumb > li + li:before {
  padding: 10px;
  color: #ccc;
  content: "/\00a0";
}
.custom_breadcrumb  a{
 	color: #a0a0a0;
}
.custom_breadcrumb  a:hover{
 	color: #d82229;
}
.custom_breadcrumb > .active {
  color: #d82229 !important;
}

/**RESOURCES STYLES **/
.webinar_container{
	margin-bottom: 60px;
}
.webinar_container  button {
	padding: 3px !important;
}
.webinar_container p{
	float: left;
}
#section_two-container div > button{
	padding: 3px !important;
	margin-left: 30px;
}
.webinar_content_container{
	margin-top: 30px;
}

.list-group-item{
	line-height: normal;
}
.badge.space-left{
	margin-left: 20px;
	margin-top: 10px;
}

.knowledge_base_breadcrumb {
  padding: 8px 15px;
  margin-bottom: 0px;
  list-style: none;

}
.knowledge_base_breadcrumb > li {
  display: inline-block;
}
.knowledge_base_breadcrumb > li + li:before {
  padding: 10px;
  color: #ccc;
  content: "/\00a0";
}
.knowledge_base_breadcrumb > .active {
  color: #d82229 !important;
}


/**Product Page Layout***/
.main_row_section .banner-section{
	padding: 30px 0 !important;
}
.bam_header{
	text-align: center;
}
.bam_header h1 {
 color:#656565;
 font-size: 32px;
 font-family: "Open Sans Regular" !important;
}
.bam_header h1 {
    margin-bottom: 8px;
}
.bam_header p {
 margin-bottom:15px;
 font-size:21px;
 color: #656565 !important;
 font-family: "Open Sans Regular" !important;
 line-height: 40px;
}

.bam-carousel .item img{
	width: 100%;
	height: 100%;
}
.carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    border: 1px solid #c92525;
    border-radius: 10px;
    cursor: pointer;
    background-color: #ce2222 !important;
 }
 .carousel-indicators .active {
    margin: 0;
    width: 12px;
    height: 12px;
    background-color: #fff !important;
}

.justify{
	text-align: justify;
}
.bam_section_header{
	color: #656565;
	font-size: 26px;
	font-family: "Open Sans Regular" !important;
	letter-spacing: 1px;
	line-height: 40px;
}

.request-button-inverse {
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    color: #fff;
    background-color:  #c9302c;;
    border-color: #d43f3a;
    border: 1px solid #d82229;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.request-button-inverse:hover, .request-button-inverse:focus, .request-button-inverse.focus {
    color: #d43f3a;
    background-color: transparent;
    border-color: #ac2925;
	text-decoration: unset;
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 0px 0;
    border-radius: 4px;
}

.pages-form-control
{
	margin-top: -6px;
	margin-left: 5px;
	padding: 4px 5px;
	display: block;
    height: 40px;
    font-size: 13px;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
}



div.hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eee;
  text-align: center;
  height: 0px;
  line-height: 0px;
}

.hr-title {
  background-color: #fff;
}

.badge {
  padding: 1px 9px 3px;
  font-size: 12.025px;
  font-weight: bold;
  white-space: nowrap;
  color: #ffffff;
  background-color: #999999;
  -webkit-border-radius: 9px;
  -moz-border-radius: 9px;
  border-radius: 9px;
}
.badge:hover {
  color: #ffffff;
  text-decoration: none;
  cursor: pointer;
}
.badge-error {
  background-color: #b94a48;
}
.badge-error:hover {
  background-color: #953b39;
}
.badge-warning {
  background-color: #f89406;
}
.badge-warning:hover {
  background-color: #c67605;
}
.badge-success {
  background-color: #468847;
}
.badge-success:hover {
  background-color: #356635;
}
.badge-info {
  background-color: #3a87ad;
}
.badge-info:hover {
  background-color: #2d6987;
}
.badge-inverse {
  background-color: #333333;
}
.badge-inverse:hover {
  background-color: #1a1a1a;
}

.navbar-custom .navbar-nav > li > a {
    width: max-content;
}


.navbar:before {
    display: none !important;
    content: "" !important;
}

.licenseManager .navbar {
    flex-wrap: nowrap !important;
}

.licenseManager {
    display: flex;
          flex-direction: row;
          flex-wrap: nowrap !important;
          width: 100%;
          
    gap: 30px;
    margin-inline: 82px;
}

.btn-light {
    transition: all 0.3s ease;
    margin: 0 2px;
}

.btn-light:hover svg {
    transition: fill 0.3s ease;
}

/* Edit button hover */
.btn-light:nth-child(1):hover {
    background-color: #007bff;
}
.btn-light:nth-child(1):hover svg {
    fill: white;
}

/* Download button hover */
.btn-light:nth-child(2):hover {
    background-color: #28a745;
}
.btn-light:nth-child(2):hover svg {
    fill: white;
}

/* Delete button hover */
.btn-light:nth-child(3):hover {
    background-color: #dc3545;
}
.btn-light:nth-child(3):hover svg {
    fill: white;
}

/* Export button hover */
.btn-light:nth-child(4):hover {
    background-color: #17a2b8;
}
.btn-light:nth-child(4):hover svg {
    fill: white;
}

 .form-control {
        display: block;
        width: 56%;
      }


    
.pagination > li > a, .pagination > li > span {
	color: #777;
}

.main-section {
    overflow-x: hidden;
    max-width: 100%;
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.container {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    max-width: 100%;
}

.pages-form-control {
    margin-top: 3px;
    margin-left: 5px;
    padding: 3px 6px;
    display: block;
    height: 26px;
    font-size: 13px;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
}
.nav-tabs > li > a {
    border-radius: 4px 4px 0 0;
}
.table {
    border: none;
}

.table > thead > tr > th,
.table > tbody > tr > td {
    border-left: none;
    border-right: none;
    border-top: none;
    border-bottom: 1px solid #ddd;
}

.table > thead > tr > th {
    background-color: transparent;
    border-bottom: 2px solid #ddd;
}

.table > tbody > tr:last-child > td {
    border-bottom: none;
}

.modal-content {
    font-family: 'Open Sans', sans-serif;
}

.modal-title {
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
}

.modal-body {
    font-family: 'Open Sans', sans-serif;
}

.modal-footer {
    font-family: 'Open Sans', sans-serif;
}

.form-control {
    font-family: 'Open Sans', sans-serif;
}

.control-label {
    font-family: 'Open Sans', sans-serif;
}

