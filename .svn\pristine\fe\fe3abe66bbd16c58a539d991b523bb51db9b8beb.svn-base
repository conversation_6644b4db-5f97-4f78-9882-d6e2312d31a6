package com.accolm.licenseManager.RestInterface;

import java.util.List;

import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.accolm.licenseManager.Entities.Company;
import com.accolm.licenseManager.Entities.Contact;
import com.accolm.licenseManager.Services.ContactService;
import com.accolm.licenseManager.Services.ContactServiceImpl;

@Path("contacts")
public class ContactRI {

	// a contact service layer for separation of concerns.
	private ContactService impl = new ContactServiceImpl();

	// Endpoint to Create a new contact, /api/contacts
	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response createContact(Contact contact) {
		Contact result = impl.createContact(contact);
		return Response.status(Response.Status.CREATED).entity(result).build();
	}


	// 1. endpoint to delete contact
	@DELETE
	@Produces(MediaType.APPLICATION_JSON)
	@Path("/delete/{id}")
	public Response deleteContact(@PathParam("id") int id) {
		boolean isDeleted = impl.deleteContact(id);
		String message = "";
		if (isDeleted) {
			message = "contact sucessfully deleted";
		} else {
			message = "delete contact failed.";
		}

		return Response.status(Response.Status.OK).entity(message).build();
	}

	// Endpoint to Update an existing companies, /api/contacts
	@PUT
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateContact(Contact contact) {
		Contact result = impl.updateContact(contact);
		return Response.status(Response.Status.OK).entity(result).build();
	}
	

	// Endpoint to Update an existing companies, /contacts/company/{companyId} 
	// return only contacts for the specified company ID
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Path("company/{companyId}")
	public Response listContactsByCompanyId(@PathParam("companyId") int companyId) {
		List<Contact> result = impl.getContactsByCompanyId(companyId);
		return Response.status(Response.Status.OK).entity(result).build();
	}
	
	
	// list all contacts
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Path("all")
	public Response listAllContacts() {
		List<Contact> result = impl.listAllContacts();
		return Response.status(Response.Status.OK).entity(result).build();
	}


}
