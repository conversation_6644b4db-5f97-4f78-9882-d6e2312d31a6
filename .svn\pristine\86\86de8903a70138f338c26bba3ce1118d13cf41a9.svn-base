package com.accolm.licenseManager.Utils;

import java.security.SecureRandom;
import java.util.Random;

public class LicenseKeyGenerator {

    private static final String ALPHABET = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private final Random rng = new SecureRandom();

    // Generate a random character from the ALPHABET
    char randomChar() {
        return ALPHABET.charAt(this.rng.nextInt(ALPHABET.length()));
    }

    // Generate a key with custom length, spacing, and spacer character
    public String generateKey(int length, int spacing, char spacerChar) {
        StringBuilder sb = new StringBuilder();
        int spacer = 0;
        while (length > 0) {
            if (spacer == spacing) {
                sb.append(spacerChar);
                spacer = 0;
            }
            length--;
            spacer++;
            sb.append(randomChar());
        }
        return sb.toString();
    }

    // Generate a default key with predefined length, spacing, and spacer character
    public String generateDefaultKey() {
        int defaultLength = 16; // Default length of the key
        int defaultSpacing = 4; // Add a spacer every 5 characters
        char defaultSpacerChar = '-'; // Use hyphen as the spacer character
        return generateKey(defaultLength, defaultSpacing, defaultSpacerChar);
    }
    
    
    public static void main(String[] args) {
		System.out.println(new LicenseKeyGenerator().generateDefaultKey());
	}
    
    
}