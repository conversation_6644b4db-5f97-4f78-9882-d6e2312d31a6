new Vue({
  el: "#company-details",
  data() {
    return {
      contactData: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        mobilePhone: "",
        contactType: "",
      },
      validationErrors: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
      },
      editContactData: {
        id: null,
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        mobilePhone: "",
        contactType: "",
        companyId: null,
      },
      editValidationErrors: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
      },
      allContacts: [], // Store all contacts for filtering
      filters: {
        firstName: "",
        company: "",
        email: "",
        phone: "",
      },
      contactTypeLabels: {
        primary: "Primary",
        secondary: "Secondary",
        technical: "Technical",
        billing: "Billing",
        // lowercase versions in case the API returns lowercase values
        PRIMARY: "Primary",
        SECONDARY: "Secondary",
        TECHNICAL: "Technical",
        BILLING: "Billing",
      },
      company: {
        id: null,
        name: "",
        countryId: null,
        email: "",
        telephone: "",
        status: 0,
        address: "",
        city: "",
        state: "",
        postCode: "",
      },
      deleteContactId: null,
      contacts: [],
      activeTab: "contacts",
      activeContactTab: "list",
      activeLicenseTab: "list",
      licenses: [],
      isFetching: false,
      currentPage: 1,
      maxPerPage: 10,
      itemCount: 0,
      pages: 1,
      licenseProperties: [],
      templates: [],
      companies: [],
      pickKey: [],
      selectedTemplate: null,
      templateFields: [],
      formValues: {},
      formData: {
        company: null,
        templateId: null,
        licenseName: "",
        startDate: "",
        expirationDate: "",
        signingKey: "",
        hardwareLocking: "",
      },
      singleLicense: {
        id: "",
        name: "",
        status: "Valid",
        creationDate: "",
        startDate: "",
        expiryDate: "",
        floatExp: "",
        hardwareLock: "",
        properties: [],
        signKey: {
          id: "",
          name: "",
        },
      },
      editFormData: {
        id: null,
        name: "",
        status: "",
        creationDate: "",
        startDate: "",
        expiryDate: "",
        floatExp: "",
        hardwareLock: false,
        properties: [],
        signKey: {
          id: 0,
          name: "",
        },
        companyId: 0,
      },
      deleteLicenseId: null,
      singleLicense: null,
    };
  },

  methods: {
    // Rename these methods
    editLicense(licenseId) {
      axios
        .get(`${this.apiBaseUrl}/licenses/get/${licenseId}`)
        .then((response) => {
          if (response.status === 200) {
            // Create a copy of the license data for editing
            this.editFormData = { ...response.data };

            // Ensure signKey is properly initialized
            if (!this.editFormData.signKey) {
              this.editFormData.signKey = { id: "", name: "" };
            }

            // Show the edit modal
            $("#editLicenseModal").modal("show");
          }
        })
        .catch((error) => {
          console.error("Error fetching license details for editing:", error);
          $("#licenseErrorModal").modal("show");
        });
    },

    // method to handle adding a property in edit mode
    addEditProperty() {
      if (!this.editFormData.properties) {
        this.editFormData.properties = [];
      }
      this.editFormData.properties.push({
        name: "",
        value: "",
      });
    },

    updateLicense() {
      // Validate form data
      if (
        !this.editFormData.name ||
        !this.editFormData.startDate ||
        !this.editFormData.expiryDate
      ) {
        alert("Please fill in all required fields");
        return;
      }

      // Send update request to the server
      axios
        .put(`${this.apiBaseUrl}/licenses/edit`, this.editFormData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          if (response.status === 200) {
            $("#editLicenseModal").modal("hide");
            $("#editLicenseSuccess").modal("show");

            // Refresh the data
            this.fetchLicenses();

            setTimeout(() => {
              $("#editLicenseSuccess").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error updating license:", error);
          $("#editLicenseModal").modal("hide");
          $("#licenseErrorModal").modal("show");
        });
    },

    //  method to handle removing a property in edit mode
    removeEditProperty(index) {
      if (
        this.editFormData.properties &&
        this.editFormData.properties.length > 0
      ) {
        this.editFormData.properties.splice(index, 1);
      }
    },

    async fetchLicenses() {
      this.isFetching = true;
      try {
        const [res, resCount] = await Promise.all([
          axios.get(
            `${this.apiBaseUrl}/licenses/pagination/${this.currentPage}`
          ),
          axios.get(`${this.apiBaseUrl}/licenses/count`),
        ]);

        if (res.status === 200 && resCount.status === 200) {
          const allLicenses = res.data;
          this.itemCount = resCount.data;
          this.pages = Math.ceil(this.itemCount / this.maxPerPage);

          // Calculate pagination slice based on selected page size
          const startIndex = (this.currentPage - 1) * this.maxPerPage;
          const endIndex = startIndex + this.maxPerPage;

          // Set paginated results using selected page size
          this.licenses = allLicenses.slice(startIndex, endIndex);
          console.log("Current page size:", this.maxPerPage);
          console.log("Displayed licenses:", this.licenses.length);
        }
      } catch (error) {
        console.error("Error fetching licenses:", error);
      } finally {
        this.isFetching = false;
      }
    },
    initializeFormValues() {
      this.formValues = {};
      if (this.templateFields) {
        this.templateFields.forEach((field) => {
          this.formValues[field.name] = "";
        });
      }
    },

    async fetchTemplates() {
      const response = await axios.get(
        `${this.apiBaseUrl}/templates/pagination/${this.currentPage}`
      );
      this.templates = response.data;
    },

    async fetchSigningKeys() {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/keys`);
        console.log("Fetched keys:", response.data);
        this.pickKey = response.data;
      } catch (error) {
        console.error("Error fetching signing keys:", error);
      }
    },
    async onTemplateSelect(event) {
      const templateId = event.target.value;
      if (templateId) {
        try {
          const response = await axios.get(
            `${this.apiBaseUrl}/templates/get/${templateId}`
          );
          this.selectedTemplate = response.data;
          this.templateFields = response.data.fields || [];

          // Initialize form values for template fields
          this.initializeFormValues();

          // Clear existing license properties
          this.licenseProperties = [];

          // Check if the template has properties
          console.log("Template data:", this.selectedTemplate);

          // Check for templateProperties or properties field
          const templateProps =
            this.selectedTemplate.templateProperties ||
            this.selectedTemplate.properties ||
            [];

          if (templateProps && templateProps.length > 0) {
            // Clone the template properties to avoid reference issues
            this.licenseProperties = templateProps.map((prop) => ({
              name: prop.name || "",
              value: prop.value || "",
            }));
            console.log(
              "Populated license properties from template:",
              this.licenseProperties
            );
          } else {
            console.log("No template properties found");
          }
        } catch (error) {
          console.error("Error fetching template details:", error);
        }
      }
    },

    determineInputType(propertyType) {
      // Map property types to HTML input types
      switch (propertyType.toLowerCase()) {
        case "number":
          return "number";
        case "date":
          return "date";
        case "boolean":
          return "checkbox";
        default:
          return "text";
      }
    },

    addLicenseProperty() {
      if (!this.licenseProperties) {
        this.licenseProperties = [];
      }
      this.licenseProperties.push({
        name: "",
        value: "",
      });
    },

    removeLicenseProperty(index) {
      if (this.licenseProperties && this.licenseProperties.length > 0) {
        this.licenseProperties.splice(index, 1);
      }
    },

    async submitLicense() {
      try {
        // Get the selected signing key object
        const selectedKey = this.pickKey.find(
          (key) => key.id == this.formData.signingKey
        );

        //properties array from the licenseProperties
        const customProperties = this.licenseProperties.map((prop) => ({
          name: prop.name,
          value: prop.value,
        }));

        const licenseData = {
          companyId: this.company.id,
          name: this.formData.licenseName,
          status: "ACTIVE",
          creationDate: new Date().toISOString().split("T")[0],
          startDate: this.formData.startDate,
          expiryDate: this.formData.expirationDate,
          floatExp: parseInt(this.formData.floatingDays) || 1,
          hardwareLock: this.formData.hardwareLocking === "true",
          signKey: {
            id: parseInt(this.formData.signingKey),
            name:
              this.pickKey.find(
                (key) => key.id === parseInt(this.formData.signingKey)
              )?.name || "",
          },
          properties: customProperties || [],
        };

        console.log("Submitting license data:", licenseData); // Debug log
        const response = await axios.post(
          `${this.apiBaseUrl}/licenses/create`,
          JSON.stringify(licenseData),
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        console.log("API response:", response);
        if (
          (response.status === 200 || response.status === 201) &&
          response.data
        ) {
          console.log("Success! Showing modal...");
          $("#createSuccessModal").modal("show");
          $("#createSuccessModal").on("hidden.bs.modal", () => {
            this.resetForm();
            this.fetchLicenses();
            this.setActiveTab("licenses");
            this.setActiveLicenseTab("list");

            $('a[href="#list"]').tab("show");
          });
        }
      } catch (error) {
        console.error("Error creating license:", error);
        // Show error modal
        $("#errorModalcreate").modal("show");
      }
    },
    resetForm() {
      this.formValues = {};
      this.formData.company = "";
      this.formData.templateId = "";
      this.formData.licenseName = "";
      this.formData.startDate = "";
      this.formData.expirationDate = "";
      this.formData.signingKey = "";
      this.formData.hardwareLocking = "";
      this.selectedTemplate = null;
      this.templateFields = [];
    },
    displayData() {
      this.fetchLicenses();
    },

    formatDate(dateString) {
      if (!dateString) return "N/A";
      const date = new Date(dateString);
      return date.toLocaleDateString();
    },

    deleteModal(licenseId) {
      this.deleteLicenseId = licenseId;
    },

    async deleteLicense() {
      if (!this.deleteLicenseId) return;

      try {
        const response = await axios.delete(
          `${this.apiBaseUrl}/license/${this.deleteLicenseId}`
        );
        if (response.status === 200) {
          $("#deleteLicenseModal").modal("hide");
          this.fetchLicenses();

          // Show success message
          $("#licenseDeleteSuccessModal").modal("show");
          setTimeout(() => {
            $("#licenseDeleteSuccessModal").modal("hide");
          }, 2000);
        }
      } catch (error) {
        console.error("Error deleting license:", error);
        $("#deleteLicenseModal").modal("hide");
        $("#licenseErrorModal").modal("show");
        setTimeout(() => {
          $("#licenseErrorModal").modal("hide");
        }, 2000);
      }
    },

    async displaySingleLicense(licenseId) {
      try {
        // Set to null or loading state before fetching
        this.singleLicense = null;

        const response = await axios.get(
          `${this.apiBaseUrl}/licenses/get/${licenseId}`
        );
        if (response.status === 200) {
          // Make sure signKey is initialized
          const licenseData = response.data;
          if (!licenseData.signKey) {
            licenseData.signKey = { id: "", name: "" };
          }
          this.singleLicense = licenseData;
          $("#licenseDetailsModal").modal("show");
        }
      } catch (error) {
        console.error("Error fetching license details:", error);
        // Initialize with empty data on error
        this.singleLicense = {
          id: "",
          name: "",
          status: "Valid",
          creationDate: "",
          startDate: "",
          expiryDate: "",
          floatExp: "",
          hardwareLock: "",
          properties: [],
          signKey: {
            id: "",
            name: "",
          },
        };
        $("#licenseErrorModal").modal("show");
      }
    },
    async displayDownloadLicense(licenseId) {
      try {
        const response = await axios.get(
          `${this.apiBaseUrl}/license/${licenseId}/download`,
          {
            responseType: "blob",
          }
        );

        if (response.status === 200) {
          // Create a download link
          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", `license_${licenseId}.lic`);
          document.body.appendChild(link);
          link.click();
          link.remove();
        }
      } catch (error) {
        console.error("Error downloading license:", error);
        $("#licenseErrorModal").modal("show");
        setTimeout(() => {
          $("#licenseErrorModal").modal("hide");
        }, 2000);
      }
    },
    setActiveLicenseTab(tab) {
      this.activeLicenseTab = tab;
    },

    editContact(contactId) {
      const contact = this.contacts.find((c) => c.id === contactId);
      if (contact) {
        // Clone the contact data to avoid direct mutation
        this.editContactData = { ...contact };
        // Show the edit modal
        $("#editContactModal").modal("show");
      }
    },

    validateEditContactForm() {
      let isValid = true;
      // Reset validation errors
      this.editValidationErrors = {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
      };

      // First Name validation
      if (!this.editContactData.firstName.trim()) {
        this.editValidationErrors.firstName = "First name is required";
        isValid = false;
      } else if (this.editContactData.firstName.length < 2) {
        this.editValidationErrors.firstName =
          "First name must be at least 2 characters";
        isValid = false;
      }

      // Last Name validation
      if (!this.editContactData.lastName.trim()) {
        this.editValidationErrors.lastName = "Last name is required";
        isValid = false;
      } else if (this.editContactData.lastName.length < 2) {
        this.editValidationErrors.lastName =
          "Last name must be at least 2 characters";
        isValid = false;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!this.editContactData.email.trim()) {
        this.editValidationErrors.email = "Email is required";
        isValid = false;
      } else if (!emailRegex.test(this.editContactData.email)) {
        this.editValidationErrors.email = "Please enter a valid email address";
        isValid = false;
      }

      // Phone validation (optional but must be valid if provided)
      const phoneRegex =
        /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
      if (
        this.editContactData.phone.trim() &&
        !phoneRegex.test(this.editContactData.phone)
      ) {
        this.editValidationErrors.phone = "Please enter a valid phone number";
        isValid = false;
      }

      // Contact Type validation
      if (!this.editContactData.contactType) {
        this.editValidationErrors.contactType = "Please select a contact type";
        isValid = false;
      }

      return isValid;
    },

    updateContact() {
      if (!this.validateEditContactForm()) {
        return; // Stop submission if validation fails
      }

      axios
        .put(`${this.apiBaseUrl}/contacts`, this.editContactData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          if (response.status === 200) {
            // Close the edit modal
            $("#editContactModal").modal("hide");

            // Refresh the contacts list
            this.fetchContacts();

            // Show success modal
            $("#contactUpdateSuccessModal").modal("show");

            // Auto-hide the modal after 2 seconds
            setTimeout(() => {
              $("#contactUpdateSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error updating contact:", error);

          // Close the edit modal
          $("#editContactModal").modal("hide");

          // Show error modal
          $("#contactErrorModal").modal("show");

          // Auto-hide the modal after 2 seconds
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },
    submitContact() {
      if (!this.validateContactForm()) {
        return; // Stop submission if validation fails
      }

      const contactData = {
        ...this.contactData,
        companyId: this.company.id,
      };

      axios
        .post(`${this.apiBaseUrl}/contacts`, contactData)
        .then((response) => {
          if (response.status === 201) {
            this.activeContactTab = "list";
            this.fetchContacts();
            this.resetContactForm();

            // Show success modal
            $("#contactSuccessModal").modal("show");

            // Auto-hide the modal after 2 seconds
            setTimeout(() => {
              $("#contactSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error adding contact:", error);

          // Show error modal
          $("#contactErrorModal").modal("show");

          // Auto-hide the modal after 2 seconds
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },
    resetLicenseForm() {
      // Reset form data
      this.formData = {
        company: this.company.id, // Keep the company ID
        templateId: "",
        licenseName: "",
        startDate: "",
        expirationDate: "",
        floatingDays: "",
        signingKey: "",
        hardwareLocking: "",
      };

      // Reset template-related data
      this.selectedTemplate = null;
      this.templateFields = [];
      this.formValues = {};

      // Reset license properties
      this.licenseProperties = [];
    },

    resetContactForm() {
      this.contactData = {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        mobilePhone: "",
        contactType: "",
      };
    },

    getCompanyIdFromUrl() {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get("id");
    },

    async fetchContacts() {
      const companyId = this.getCompanyIdFromUrl();
      if (!companyId) return;

      try {
        const response = await axios.get(
          `${this.apiBaseUrl}/contacts/company/${companyId}`
        );
        if (response.status === 200) {
          console.log("Contacts data:", response.data);
          // Store all contacts for filtering
          this.allContacts = response.data;
          this.contacts = [...this.allContacts];
        }
      } catch (error) {
        console.error("Error fetching contacts:", error);
      }
    },

    formatContactType(type) {
      console.log("Contact type value:", type);
      console.log("Contact type type:", typeof type);

      // If type is null or undefined
      if (type == null) return "Unknown";
      if (typeof type === "string" && this.contactTypeLabels[type]) {
        return this.contactTypeLabels[type];
      }
      if (typeof type === "object") {
        if (type.name) return type.name;
        if (type.type && this.contactTypeLabels[type.type])
          return this.contactTypeLabels[type.type];
      }
      return String(type);
    },

    async fetchCompanyData() {
      const id = this.getCompanyIdFromUrl();
      if (id) {
        try {
          const response = await axios.get(
            `${this.apiBaseUrl}/companies/${id}`
          );
          this.company = response.data;
        } catch (error) {
          console.error("Error fetching company:", error);
        }
      }
    },
    setActiveTab(tab) {
      this.activeTab = tab;
    },
    setActiveContactTab(tab) {
      this.activeContactTab = tab;
    },
    validateContactForm() {
      let isValid = true;
      // Reset validation errors
      this.validationErrors = {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
      };

      // First Name validation
      if (!this.contactData.firstName.trim()) {
        this.validationErrors.firstName = "First name is required";
        isValid = false;
      } else if (this.contactData.firstName.length < 2) {
        this.validationErrors.firstName =
          "First name must be at least 2 characters";
        isValid = false;
      }

      // Last Name validation
      if (!this.contactData.lastName.trim()) {
        this.validationErrors.lastName = "Last name is required";
        isValid = false;
      } else if (this.contactData.lastName.length < 2) {
        this.validationErrors.lastName =
          "Last name must be at least 2 characters";
        isValid = false;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!this.contactData.email.trim()) {
        this.validationErrors.email = "Email is required";
        isValid = false;
      } else if (!emailRegex.test(this.contactData.email)) {
        this.validationErrors.email = "Please enter a valid email address";
        isValid = false;
      }

      // Phone validation (optional but must be valid if provided)
      const phoneRegex =
        /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
      if (
        this.contactData.phone.trim() &&
        !phoneRegex.test(this.contactData.phone)
      ) {
        this.validationErrors.phone = "Please enter a valid phone number";
        isValid = false;
      }

      // Contact Type validation
      if (!this.contactData.contactType) {
        this.validationErrors.contactType = "Please select a contact type";
        isValid = false;
      }

      return isValid;
    },

    deleteModal(contactId) {
      this.deleteContactId = contactId;
      $("#deleteContactModal").modal("show");
    },

    // filter method for contacts
    applyFilters() {
      if (!this.allContacts || !this.allContacts.length) {
        // If we don't have the allContacts array yet, use the current contacts
        this.allContacts = [...this.contacts];
      }

      let filtered = [...this.allContacts];

      // Filter by first name
      if (this.filters.firstName) {
        const searchTerm = this.filters.firstName.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.firstName &&
            contact.firstName.toLowerCase().includes(searchTerm)
        );
      }

      // Filter by last name
      if (this.filters.lastName) {
        const searchTerm = this.filters.lastName.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.lastName &&
            contact.lastName.toLowerCase().includes(searchTerm)
        );
      }

      // Filter by contact type
      if (this.filters.contactType) {
        filtered = filtered.filter((contact) => {
          // Handle different possible formats of contactType
          if (typeof contact.contactType === "string") {
            return contact.contactType === this.filters.contactType;
          } else if (
            typeof contact.contactType === "object" &&
            contact.contactType !== null
          ) {
            if (contact.contactType.type) {
              return contact.contactType.type === this.filters.contactType;
            } else if (contact.contactType.name) {
              return contact.contactType.name === this.filters.contactType;
            }
          }
          return false;
        });
      }

      // Filter by email
      if (this.filters.email) {
        const searchTerm = this.filters.email.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.email && contact.email.toLowerCase().includes(searchTerm)
        );
      }

      this.contacts = filtered;
    },

    //for contacts filter reset
    resetFilters() {
      this.filters = {
        firstName: "",
        lastName: "",
        contactType: "",
        email: "",
      };
      this.fetchContacts(); // Reload the original data
    },

    deleteContact() {
      if (!this.deleteContactId) return;

      axios
        .delete(`${this.apiBaseUrl}/contacts/delete/${this.deleteContactId}`)
        .then((response) => {
          if (response.status === 200) {
            $("#deleteContactModal").modal("hide");

            // Remove the deleted contact from the local array
            this.contacts = this.contacts.filter(
              (contact) => contact.id !== this.deleteContactId
            );

            // Show success modal
            $("#contactDeleteSuccessModal").modal("show");
            setTimeout(() => {
              $("#contactDeleteSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error deleting contact:", error);

          // Close the delete confirmation modal
          $("#deleteContactModal").modal("hide");
          $("#contactErrorModal").modal("show");
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },

    submitContact() {
      if (!this.validateContactForm()) {
        return; // Stop submission if validation fails
      }

      const contactData = {
        ...this.contactData,
        companyId: this.company.id,
      };

      axios
        .post(`${this.apiBaseUrl}/contacts`, contactData)
        .then((response) => {
          if (response.status === 201) {
            this.activeContactTab = "list";
            this.fetchContacts();
            this.resetContactForm();

            // Show success modal
            $("#contactSuccessModal").modal("show");

            // Auto-hide the modal after 2 seconds
            setTimeout(() => {
              $("#contactSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error adding contact:", error);

          // Show error modal
          $("#contactErrorModal").modal("show");

          // Auto-hide the modal after 2 seconds
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },
  },

  watch: {
    "filters.firstName": function () {
      this.applyFilters();
    },
    "filters.lastName": function () {
      this.applyFilters();
    },
    "filters.contactType": function () {
      this.applyFilters();
    },
    "filters.email": function () {
      this.applyFilters();
    },
  },

  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },
  },
  mounted() {
    this.fetchCompanyData();
    this.fetchContacts();
    this.fetchLicenses();
    this.fetchTemplates();
    this.fetchSigningKeys();

    this.$nextTick(() => {
      if (this.company && this.company.id) {
        this.formData.company = this.company.id;
      }
    });
  },

  template: `
  <div class="row">
    <div class="col-sm-12" style="text-align: center">
      <h1>{{company.name}}</h1>
    </div>

    <div class="home-request" style="margin-top: 20px;">
      <ul class="nav nav-tabs" role="tablist">
        <li :class="{active: activeTab === 'contacts'}">
          <a @click="setActiveTab('contacts')" data-toggle="tab" href="#contacts">Contacts</a>
        </li>
        <li :class="{active: activeTab === 'licenses'}">
          <a @click="setActiveTab('licenses')" data-toggle="tab" href="#licenses">Licenses</a>
        </li>
        <li :class="{active: activeTab === 'maintenance'}">
          <a @click="setActiveTab('maintenance')" data-toggle="tab" href="#maintenance">Maintenance & Support</a>
        </li>
        <li :class="{active: activeTab === 'extended'}">
          <a @click="setActiveTab('extended')" data-toggle="tab" href="#extended">Extended Support</a>
        </li>
      </ul>



      <div class="tab-content">
        <div id="contacts" :class="['tab-pane', { active: activeTab === 'contacts' }]">
          <ul class="nav nav-tabs" style="margin-top: 15px;">
            <li :class="{active: activeContactTab === 'list'}">
              <a @click="setActiveContactTab('list')" href="#contactsList">List Contacts</a>
            </li>
            <li :class="{active: activeContactTab === 'add'}">
              <a @click="setActiveContactTab('add')" href="#addContact">Add Contact</a>
            </li>
          </ul>

		<!--filters-->
<div class="row mb-3">
  <div class="col-md-12">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h4 class="panel-title">
          <a data-toggle="collapse" href="#contactFilterCollapse">
            <i class="glyphicon glyphicon-filter"></i> Filters
          </a>
        </h4>
      </div>
      <div id="contactFilterCollapse" class="panel-collapse collapse">
        <div class="panel-body">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label>First Name</label>
                <input type="text" class="form-control" v-model="filters.firstName" placeholder="Filter by first name">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>Last Name</label>
                <input type="text" class="form-control" v-model="filters.lastName" placeholder="Filter by last name">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>Contact Type</label>
                <select class="form-control" v-model="filters.contactType">
                  <option value="">All Types</option>
                  <option value="PRIMARY">Primary</option>
                  <option value="SECONDARY">Secondary</option>
                  <option value="TECHNICAL">Technical</option>
                  <option value="BILLING">Billing</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>Email</label>
                <input type="text" class="form-control" v-model="filters.email" placeholder="Filter by email">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 text-right">
              <button class="btn btn-default" @click="resetFilters">
                <i class="glyphicon glyphicon-refresh"></i> Reset Filters
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

          <div class="tab-content">
            <div id="contactsList" :class="['tab-pane', { active: activeContactTab === 'list' }]">
              <table class="table">
                <thead>
                  <tr>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Mobile Phone</th>
                    <th>Contact Type</th>
                    <th>Options</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="contact in contacts" :key="contact.id">
                    <td>{{contact.firstName}}</td>
                    <td>{{contact.lastName}}</td>
                    <td>{{contact.email}}</td>
                    <td>{{contact.phone}}</td>
                    <td>{{contact.mobilePhone}}</td>
                    <td>{{formatContactType(contact.contactType)}}</td>
                    <td>
                      
                       <!-- Edit Button -->
<button class="btn btn-light" @click="editContact(contact.id)" title="Edit">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
        </svg>
      </button>


  <!-- Delete Button -->
 <button class="btn btn-light" @click="deleteModal(contact.id)" title="Delete">
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
    <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
    <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
  </svg>
</button>
                    </td>

                  </tr>
                </tbody>
              </table>

     

            </div>


            <!--- Add Contact Form -->
          <div id="addContact" :class="['tab-pane', { active: activeContactTab === 'add' }]">
     <div style="margin-top: 20px;">
      <div class="row">
        <div class="col-sm-2"></div>
        <div class="col-sm-8">
          <form class="form-horizontal" @submit.prevent="submitContact">
            <div class="form-group" :class="{'has-error': validationErrors.firstName}">
              <div class="col-sm-3">
                <label class="control-label">First Name</label>
              </div>
              <div class="col-sm-6">
                <input type="text" v-model="contactData.firstName" class="form-control" required>
                <span class="help-block" v-if="validationErrors.firstName">{{ validationErrors.firstName }}</span>
              </div>
            </div>

            <div class="form-group" :class="{'has-error': validationErrors.lastName}">
              <div class="col-sm-3">
                <label class="control-label">Last Name</label>
              </div>
              <div class="col-sm-6">
                <input type="text" v-model="contactData.lastName" class="form-control" required>
                <span class="help-block" v-if="validationErrors.lastName">{{ validationErrors.lastName }}</span>
              </div>
            </div>

            <div class="form-group" :class="{'has-error': validationErrors.email}">
              <div class="col-sm-3">
                <label class="control-label">Email</label>
              </div>
              <div class="col-sm-6">
                <input type="email" v-model="contactData.email" class="form-control" required>
                <span class="help-block" v-if="validationErrors.email">{{ validationErrors.email }}</span>
              </div>
            </div>

            <div class="form-group" :class="{'has-error': validationErrors.phone}">
              <div class="col-sm-3">
                <label class="control-label">Phone</label>
              </div>
              <div class="col-sm-6">
                <input type="tel" v-model="contactData.phone" class="form-control">
                <span class="help-block" v-if="validationErrors.phone">{{ validationErrors.phone }}</span>
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-3">
                <label class="control-label">Mobile Phone</label>
              </div>
              <div class="col-sm-6">
                <input type="tel" v-model="contactData.mobilePhone" class="form-control">
              </div>
            </div>

            <div class="form-group" :class="{'has-error': validationErrors.contactType}">
              <div class="col-sm-3">
                <label class="control-label">Contact Type</label>
              </div>
              <div class="col-sm-6">
                <select v-model="contactData.contactType" class="form-control" required>
                  <option value="">Select Contact Type</option>
                  <option value="PRIMARY">Primary</option>
                  <option value="SECONDARY">Secondary</option>
                  <option value="TECHNICAL">Technical</option>
                  <option value="BILLING">Billing</option>
                </select>
                <span class="help-block" v-if="validationErrors.contactType">{{ validationErrors.contactType }}</span>
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-3"></div>
              <div class="col-sm-6">
                <button type="button" @click="submitContact" class="request-button form-control">Add Contact</button>
              </div>
            </div>
          </form>
        </div>
        <div class="col-sm-2"></div>
      </div>
     </div>
     </div>
          </div>
        </div>

        <div id="licenses" :class="['tab-pane', { active: activeTab === 'licenses' }]">
         <ul class="nav nav-tabs" style="margin-top: 15px;">
    <li :class="{active: activeLicenseTab === 'list'}">
      <a @click="setActiveLicenseTab('list')" href="#list">List Licenses</a>
    </li>
    <li :class="{active: activeLicenseTab === 'create'}">
      <a @click="setActiveLicenseTab('create')" href="#create">Create License</a>
    </li>
  </ul>
  
  <!-- Tabs Content -->
  <div class="tab-content">
    <!-- List Tab -->
    <div :class="['tab-pane', { active: activeLicenseTab === 'list' }]" id="list">
      <div class="table-responsive">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th colspan="8" class="tableHeader">
                <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
                <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
                  <span>Page {{currentPage}} of {{pages}}</span>
                  <ul class="pagination pagination-sm" style="margin: 0; border: none;">
                    <li :class="{disabled: currentPage === 1}" style="border: none;">
                      <span @click="currentPage = 1; displayData()" style="border: none;">
                        <i class="glyphicon glyphicon-step-backward"></i>
                      </span>
                    </li>
                    <li :class="{disabled: currentPage === 1}" style="border: none;">
                      <span @click="currentPage > 1 ? (currentPage--, displayData()) : null" style="border: none;">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                      </span>
                    </li>
                    <li :class="{disabled: currentPage === pages}" style="border: none;">
                      <span @click="currentPage < pages ? (currentPage++, displayData()) : null" style="border: none;">
                        <i class="glyphicon glyphicon-chevron-right"></i>
                      </span>
                    </li>
                    <li :class="{disabled: currentPage === pages}" style="border: none;">
                      <span @click="currentPage = pages; displayData()" style="border: none;">
                        <i class="glyphicon glyphicon-step-forward"></i>
                      </span>
                    </li>
                  </ul>
                </div>
              </th>
            </tr>
            <tr>
              <th>Name</th>
              <th>Status</th>
              <th>Creation Date</th>
              <th>Start Date</th>
              <th>Expiration Date</th>
              <th>Hardware Locked</th>
              <th>Signing Key</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody v-show='!isFetching'>
            <tr v-for="license in licenses" :key="license.id">
            <td style="cursor: pointer;" @click="displaySingleLicense(license.id)"><a href="#">{{ license.name }}</a></td>
              <td>
                <span :class="{'text-success': license.validationStatus, 'text-danger': !license.validationStatus}">
                  {{ license.validationStatus ? 'Valid' : 'Invalid' }}
                </span>
              </td>
              <td>{{ formatDate(license.creationDate) }}</td>
              <td>{{ formatDate(license.startDate) }}</td>
              <td>{{ formatDate(license.expiryDate) }}</td>
              <td>{{ license.hardwareLock ? 'Yes' : 'No' }}</td>
              <td>{{ license.signKey && license.signKey.name ? license.signKey.name : 'N/A' }}</td>
              <td>
                <div class="btn-group">
                   <button @click="editLicense(license.id)" class="btn btn-light">
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
    <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
  </svg>
</button>
                  <button @click="displayDownloadLicense(license.id)" class="btn btn-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                      <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                    </svg>
                  </button>
                  <button data-toggle="modal" data-target="#deleteLicenseModal" @click="deleteModal(license.id)" class="btn btn-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                      <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <th colspan="8" class="tableHeader">
                <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
                <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
                  <span>Page {{currentPage}} of {{pages}}</span>
                  <ul class="pagination pagination-sm" style="margin: 0; border: none;">
                    <li :class="{disabled: currentPage === 1}" style="border: none;">
                      <span @click="currentPage = 1; displayData()" style="border: none;">
                        <i class="glyphicon glyphicon-step-backward"></i>
                      </span>
                    </li>
                    <li :class="{disabled: currentPage === 1}" style="border: none;">
                      <span @click="currentPage > 1 ? (currentPage--, displayData()) : null" style="border: none;">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                      </span>
                    </li>
                    <li :class="{disabled: currentPage === pages}" style="border: none;">
                      <span @click="currentPage < pages ? (currentPage++, displayData()) : null" style="border: none;">
                        <i class="glyphicon glyphicon-chevron-right"></i>
                      </span>
                    </li>
                    <li :class="{disabled: currentPage === pages}" style="border: none;">
                      <span @click="currentPage = pages; displayData()" style="border: none;">
                        <i class="glyphicon glyphicon-step-forward"></i>
                      </span>
                    </li>
                  </ul>
                </div>
              </th>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
    
    <!-- Create License Tab -->
    <div :class="['tab-pane', { active: activeLicenseTab === 'create' }]" id="create">
      <div class="row">
        <div class="col-sm-2"></div>
        <div class="col-sm-8" style="margin-top: 29px;">
          <form class="form-horizontal" @submit.prevent="submitLicense">
            <div class="form-group">
              <div class="col-sm-3">
                <label class="control-label">Company</label>
              </div>
              <div class="col-sm-6">
                <input type="text" class="form-control" :value="company.name" disabled>
                <input type="hidden" v-model="formData.company">
              </div>
            </div>
            
            <div class="form-group">
              <div class="col-sm-3">
                <label class="control-label">Template</label>
              </div>
              <div class="col-sm-6">
                <select v-model="formData.templateId" class="form-control" required @change="onTemplateSelect">
                  <option value="">Select Template</option>
                  <option v-for="template in templates" :key="template.id" :value="template.id">{{template.name}}</option>
                </select>
              </div>
            </div>
            
            <!-- Additional fields that appear only when template is selected -->
            <div v-show="formData.templateId">
              <div class="form-group">
                <div class="col-sm-3">
                  <label class="control-label">License Name</label>
                </div>
                <div class="col-sm-6">
                  <input type="text" v-model="formData.licenseName" class="form-control" required placeholder="Enter license name">
                </div>
              </div>
              
              <div class="form-group">
                <div class="col-sm-3">
                  <label class="control-label">Start Date</label>
                </div>
                <div class="col-sm-6">
                  <input type="date" v-model="formData.startDate" class="form-control" required>
                </div>
              </div>
              
              <div class="form-group">
                <div class="col-sm-3">
                  <label class="control-label">Expiration Date</label>
                </div>
                <div class="col-sm-6">
                  <input type="date" v-model="formData.expirationDate" class="form-control" required>
                </div>
              </div>
              
             <div class="form-group">
  <div class="col-sm-3">
    <label class="control-label">Signing Key</label>
  </div>
  <div class="col-sm-6">
    <select v-model="formData.signingKey" class="form-control" required>
      <option value="" disabled selected>Select Signing Key</option>
      <option v-for="key in pickKey" :key="key.id" :value="key.id">{{key.name}}</option>
    </select>
  </div>
</div>
              
              <div class="form-group">
                <div class="col-sm-3">
                  <label class="control-label">Hardware Locking</label>
                </div>
                <div class="col-sm-6">
                  <select v-model="formData.hardwareLocking" class="form-control" required>
                    <option value="" disabled selected>Select Option</option>
                    <option value="true">True</option>
                    <option value="false">False</option>
                  </select>
                </div>
              </div>
              
              <!-- Dynamic fields based on template -->
              <template v-if="selectedTemplate">
                <div class="form-group" v-for="field in templateFields" :key="field.name">
                  <div class="col-sm-3">
                    <label class="control-label">{{field.label}}</label>
                  </div>
                  <div class="col-sm-6">
                    <input 
                      :type="field.type" 
                      class="form-control" 
                      v-model="formValues[field.name]" 
                      :required="field.required">
                  </div>
                </div>
              </template>
            </div>

          
<div class="form-group" v-show="formData.templateId">
  <div class="col-sm-12">
    <div class="card shadow mb-4">
      <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary" style="font-family: 'Open Sans';color: #ac2925;font-size: 14px">
          Additional License Properties
        </h6>
      </div>
      <div class="card-body">
        <table class="table table-hover table-bordered">
          <thead>
            <tr>
              <th>Property</th>
              <th>Value</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(prop, index) in licenseProperties" :key="index">
              <td>
                <input v-model="prop.name" type="text" class="form-control">
              </td>
              <td>
                <input v-model="prop.value" type="text" class="form-control">
              </td>
              <td>
                <button type="button" @click="removeLicenseProperty(index)" class="btn btn-danger btn-sm">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <button type="button" @click="addLicenseProperty" class="btn btn-light">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
          Add Property
        </button>
      </div>
    </div>
  </div>
</div>
      
            <div class="form-group">
              <div class="col-sm-3"></div>
              <div class="col-sm-6">
                <button type="submit" class="request-button form-control" :disabled="!formData.templateId">Create License</button>
              </div>
            </div>
          </form>
        </div>
        <div class="col-sm-2"></div>
      </div>
    </div>
  </div>
        </div>

        <div id="maintenance" :class="['tab-pane', { active: activeTab === 'maintenance' }]">
          <!-- Maintenance content -->
        </div>

        <div id="extended" :class="['tab-pane', { active: activeTab === 'extended' }]">
          <!-- Extended support content -->
        </div>
      </div>
    </div>

    <!-- License Details Modal -->
<div class="modal fade" id="licenseDetailsModal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">License Details</h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body" v-if="singleLicense">
        <form class="form-horizontal">
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">License Name</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="singleLicense.name" disabled class="form-control">
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Start Date</label>
            </div>
            <div class="col-sm-9">
              <input type="date" v-model="singleLicense.startDate" disabled class="form-control">
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Expiration Date</label>
            </div>
            <div class="col-sm-9">
              <input type="date" v-model="singleLicense.expiryDate" disabled class="form-control">
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Floating Days</label>
            </div>
            <div class="col-sm-9">
              <input type="number" v-model="singleLicense.floatExp" disabled class="form-control">
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3" style="padding-right: 0px;">
              <label class="control-label">Hardware Locking</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="singleLicense.hardwareLock" disabled class="form-control">
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Signing Key</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="singleLicense.signKey && singleLicense.signKey.name" disabled class="form-control">
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-12">
              <div class="panel panel-default">
                <div class="panel-heading">License Properties</div>
                <div class="panel-body">
                  <table class="table table-hover table-bordered">
                    <thead>
                      <tr>
                        <th>Property</th>
                        <th>Value</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="prop in singleLicense.properties" :key="prop.id">
                        <td>{{prop.name}}</td>
                        <td>{{prop.value}}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-body" v-else>
        <p>Loading license details...</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>


<!-- Edit License Modal -->
<div class="modal fade" id="editLicenseModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit License</h5>
        <button type="button" class="close" style="margin-top: -25px" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form class="form-horizontal">
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">License Name *</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="editFormData.name" class="form-control" required>
            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Start Date *</label>
            </div>
            <div class="col-sm-9">
              <input type="date" v-model="editFormData.startDate" class="form-control" required>
            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Expiration Date *</label>
            </div>
            <div class="col-sm-9">
              <input type="date" v-model="editFormData.expiryDate" class="form-control" required>
            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Floating Days</label>
            </div>
            <div class="col-sm-9">
              <input type="number" v-model="editFormData.floatExp" class="form-control">
            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Hardware Locking</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editFormData.hardwareLock" class="form-control">
                <option :value="true">True</option>
                <option :value="false">False</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Signing Key</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editFormData.signKey.id" class="form-control">
                <option v-for="key in pickKey" :key="key.id" :value="key.id">
                  {{key.name}}
                </option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Status</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editFormData.status" class="form-control">
                <option value="ACTIVE">Active</option>
                <option value="INACTIVE">Inactive</option>
                <option value="EXPIRED">Expired</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Company</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editFormData.companyId" class="form-control">
                <option v-for="company in companies" :key="company.id" :value="company.id">
                  {{company.name}}
                </option>
              </select>
            </div>
          </div>
          <!-- License Properties Section -->
          <div class="form-group">
            <div class="col-sm-12">
              <div class="card shadow mb-4">
                <div class="card-header py-3">
                  <h6 class="m-0 font-weight-bold text-primary" style="font-family: 'Open Sans';color: #ac2925;font-size: 14px">
                    License Properties
                  </h6>
                </div>
                <div class="card-body">
                  <table class="table table-hover table-bordered">
                    <thead>
                      <tr>
                        <th>Property</th>
                        <th>Value</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(prop, index) in editFormData.properties" :key="index">
                        <td>
                          <input v-model="prop.name" type="text" class="form-control">
                        </td>
                        <td>
                          <input v-model="prop.value" type="text" class="form-control">
                        </td>
                        <td>
                          <button type="button" @click="removeEditProperty(index)" class="btn btn-danger btn-sm">
                            <i class="fas fa-trash-alt"></i>
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <button type="button" @click="addEditProperty" class="btn btn-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                    </svg>
                    Add Property
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="request-button" @click="updateLicense">Save Changes</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Success Modal -->
<div class="modal fade" id="editLicenseSuccess" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>License updated successfully!</p>
      </div>
    </div>
  </div>
</div>


<!-- License Update Success Modal -->
<div class="modal fade" id="licenseUpdateSuccessModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>License updated successfully!</p>
      </div>
    </div>
  </div>
</div>


    <!-- Edit Contact Modal -->
    <div class="modal fade" id="editContactModal" tabindex="-1" role="dialog">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Edit Contact</h5>
            <button type="button" class="close" data-dismiss="modal">
              <span>&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <form class="form-horizontal">
              <div class="form-group" :class="{'has-error': editValidationErrors.firstName}">
                <div class="col-sm-3">
                  <label class="control-label">First Name</label>
                </div>
                <div class="col-sm-9">
                  <input type="text" v-model="editContactData.firstName" class="form-control" required>
                  <span class="help-block" v-if="editValidationErrors.firstName">{{ editValidationErrors.firstName }}</span>
                </div>
              </div>

              <div class="form-group" :class="{'has-error': editValidationErrors.lastName}">
                <div class="col-sm-3">
                  <label class="control-label">Last Name</label>
                </div>
                <div class="col-sm-9">
                  <input type="text" v-model="editContactData.lastName" class="form-control" required>
                  <span class="help-block" v-if="editValidationErrors.lastName">{{ editValidationErrors.lastName }}</span>
                </div>
              </div>

              <div class="form-group" :class="{'has-error': editValidationErrors.email}">
                <div class="col-sm-3">
                  <label class="control-label">Email</label>
                </div>
                <div class="col-sm-9">
                  <input type="email" v-model="editContactData.email" class="form-control" required>
                  <span class="help-block" v-if="editValidationErrors.email">{{ editValidationErrors.email }}</span>
                </div>
              </div>

              <div class="form-group" :class="{'has-error': editValidationErrors.phone}">
                <div class="col-sm-3">
                  <label class="control-label">Phone</label>
                </div>
                <div class="col-sm-9">
                  <input type="tel" v-model="editContactData.phone" class="form-control">
                  <span class="help-block" v-if="editValidationErrors.phone">{{ editValidationErrors.phone }}</span>
                </div>
              </div>

              <div class="form-group">
                <div class="col-sm-3">
                  <label class="control-label">Mobile Phone</label>
                </div>
                <div class="col-sm-9">
                  <input type="tel" v-model="editContactData.mobilePhone" class="form-control">
                </div>
              </div>

              <div class="form-group" :class="{'has-error': editValidationErrors.contactType}">
                <div class="col-sm-3">
                  <label class="control-label">Contact Type</label>
                </div>
                <div class="col-sm-9">
                  <select v-model="editContactData.contactType" class="form-control" required>
                    <option value="">Select Contact Type</option>
                    <option value="PRIMARY">Primary</option>
                    <option value="SECONDARY">Secondary</option>
                    <option value="TECHNICAL">Technical</option>
                    <option value="BILLING">Billing</option>
                  </select>
                  <span class="help-block" v-if="editValidationErrors.contactType">{{ editValidationErrors.contactType }}</span>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
            <button type="button" class="request-button" @click="updateContact">Save Changes</button>
          </div>
        </div>
      </div>
    </div>


      <!-- create licenese modal success -->
    <div class="modal fade" id="licenseSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Success</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                      <p>Contact updated successfully!</p>
                  </div>
              </div>
          </div>
      </div>


<!-- Error Modal for create licenses page -->
<div class="modal fade" id="licenseErrorModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p>An error occurred. Please try again.</p>
            </div>
        </div>
    </div>
</div>

    <!-- Update Success Modal -->
    <div class="modal fade" id="contactUpdateSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Success</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                      <p>Contact updated successfully!</p>
                  </div>
              </div>
          </div>
      </div>

      <!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteContactModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Confirm Delete</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
        <p>Are you sure you want to delete this contact?</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" @click="deleteContact">Delete</button>
      </div>
    </div>
  </div>
</div>

 <!-- Success Modal for create licenses page -->
<div class="modal fade" id="createSuccessModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>Operation completed successfully!</p>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal for create licenses page -->
<div class="modal fade" id="errorModalcreate" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p>An error occurred. Please try again.</p>
            </div>
        </div>
    </div>
</div>

<!-- Delete Success Modal -->
<div class="modal fade" id="contactDeleteSuccessModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>Contact deleted successfully!</p>
      </div>
    </div>
  </div>
</div>

    <!-- Success Modal -->
 <div class="modal fade" id="contactSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Success</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                      <p>Contact added successfully!</p>
                  </div>
              </div>
          </div>
      </div>

    <!-- Error Modal -->
    <div class="modal fade" id="contactErrorModal" tabindex="-1" role="dialog">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Error</h5>
            <button type="button" class="close" data-dismiss="modal">
              <span>&times;</span>
            </button>
          </div>
          <div class="modal-body text-center">
  <span class="glyphicon glyphicon-remove-circle text-danger" style="font-size: 48px;"></span>
  <p>An error occurred. Please try again.</p>
</div>
        </div>
      </div>
    </div>

  
    
  </div>
`,
});
