package com.accolm.licenseManager.Entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import java.util.Objects;

@Entity
@Table(name = "signing_key")
public class SigningKey {

    @Id
    @Column(name = "SigningKey_ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "SigningKey_Name", unique = true, nullable = false)
    private String name;

    @Column(name = "SigningKey_Type")
    private String type;

    @Column(name = "SigningKey_Value")
    private String value;

    @Column(name = "public_key")
    private String public_key;

    @Column(name = "private_key")
    private String private_key;

    @Transient  // This field will not be persisted in the database
    private String filePath;

    public SigningKey() {}

    public SigningKey(Integer id, String name, String type, String value, String private_key, String public_key) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.value = value;
        this.private_key = private_key;
        this.public_key = public_key;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getPublic_key() {
        return public_key;
    }

    public void setPublic_key(String public_key) {
        this.public_key = public_key;
    }

    public String getPrivate_key() {
        return private_key;
    }

    public void setPrivate_key(String private_key) {
        this.private_key = private_key;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SigningKey)) return false;
        SigningKey that = (SigningKey) o;
        return id == that.id &&
                Objects.equals(name, that.name) &&
                Objects.equals(type, that.type) &&
                Objects.equals(value, that.value) &&
                Objects.equals(public_key, that.public_key) &&
                Objects.equals(private_key, that.private_key);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, type, value, public_key, private_key);
    }

    @Override
    public String toString() {
        return "SigningKey{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", value='" + value + '\'' +
                ", public_key='" + public_key + '\'' +
                ", filePath='" + filePath + '\'' +  // Include file path but exclude private key for security
                '}';
    }
}
