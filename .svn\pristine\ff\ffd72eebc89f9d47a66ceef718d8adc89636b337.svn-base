package com.accolm.licenseManager.Entities;

import java.util.ArrayList;

import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.NamedNativeQueries;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Entity
@Table(name = "license")
@NamedNativeQuery(
    name="createNeverLicenses",
    query="INSERT INTO license (Company_ID, CreationDate, FloatingExpiry, Hardware_Locked, Name, StartDate, Status, SigningKey_ID, licenseKey) VALUES (?,?,?,?,?,?,?,?,?)"
    )
public class License {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "License_ID")
    private int id;
    
    
    @NotNull
    @Size(min = 1, max = 100, message = "Name must be between 1 and 100 characters.")
    @Column(name = "Name")
    private String name;
    
    
    @NotNull
    @Column(name = "Company_ID")
    private String companyId;

    @NotNull
    @Column(name = "Status")
    private String status;

    @NotNull
    @Column(name = "CreationDate")
    private String creationDate;

    @NotNull
    @Column(name = "StartDate")
    private String startDate;

    @NotNull
    @Column(name = "ExpiryDate")
    private String expiryDate;
    
    @NotNull
    @Column(name = "licenseKey")
    private String licenseKey;

    @Column(name = "FloatingExpiry")
    private int floatExp;

    @Column(name = "Hardware_Locked")
    private boolean hardwareLock;

    // One-to-One relationship with SigningKey
    @OneToOne(cascade = { CascadeType.PERSIST, CascadeType.MERGE })
    @JoinColumn(name = "SigningKey_ID", referencedColumnName = "SigningKey_ID")
    private SigningKey signKey;

    // One-to-Many relationship with LicenseProperty
    @OneToMany(orphanRemoval = true, cascade = CascadeType.ALL)
    @JoinColumn(name = "License_ID")
    private List<LicenseProperty> properties = new ArrayList<>();

    // Default Constructor
    public License() {
    }

    // Parameterized Constructor
    public License(int id, String name, String status, String creationDate, String startDate, String expiryDate,
                   int floatExp, boolean hardwareLock, SigningKey signKey, List<LicenseProperty> properties) {
        this.id = id;
        this.name = name;
        this.status = status;
        this.creationDate = creationDate;
        this.startDate = startDate;
        this.expiryDate = expiryDate;
        this.floatExp = floatExp;
        this.hardwareLock = hardwareLock;
        this.signKey = signKey;
        this.properties = properties;
    }

    public License(String name, String status, String creationDate, String startDate, String expiryDate, int floatExp,
                   boolean hardwareLock, SigningKey signKey, List<LicenseProperty> properties) {
        this.name = name;
        this.status = status;
        this.creationDate = creationDate;
        this.startDate = startDate;
        this.expiryDate = expiryDate;
        this.floatExp = floatExp;
        this.hardwareLock = hardwareLock;
        this.signKey = signKey;
        this.properties = properties;
    }
    
    // with licenseKey
    public License(int id,
			@NotNull @Size(min = 1, max = 100, message = "Name must be between 1 and 100 characters.") String name,
			@NotNull String companyId, @NotNull String status, @NotNull String creationDate, @NotNull String startDate,
			@NotNull String expiryDate, @NotNull String licenseKey, int floatExp, boolean hardwareLock,
			SigningKey signKey, List<LicenseProperty> properties) {
		super();
		this.id = id;
		this.name = name;
		this.companyId = companyId;
		this.status = status;
		this.creationDate = creationDate;
		this.startDate = startDate;
		this.expiryDate = expiryDate;
		this.licenseKey = licenseKey;
		this.floatExp = floatExp;
		this.hardwareLock = hardwareLock;
		this.signKey = signKey;
		this.properties = properties;
	}

	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}
	
	public String getLicenseKey() {
		return licenseKey;
	}

	public void setLicenseKey(String licenseKey) {
		this.licenseKey = licenseKey;
	}

	// Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(String creationDate) {
        this.creationDate = creationDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public int getFloatExp() {
        return floatExp;
    }

    public void setFloatExp(int floatExp) {
        this.floatExp = floatExp;
    }

    public boolean getHardwareLock() {
        return hardwareLock;
    }

    public void setHardwareLock(boolean hardwareLock) {
        this.hardwareLock = hardwareLock;
    }

    public List<LicenseProperty> getProperties() {
        return properties;
    }

    public void setProperties(List<LicenseProperty> properties) {
        this.properties = properties;
    }

    public SigningKey getSignKey() {
        return signKey;
    }

    public void setSignKey(SigningKey signKey) {
        this.signKey = signKey;
    }

    // Utility method to calculate license expiration
    public void calculateExpirationDate() {
        // Add logic to calculate expiration date based on `startDate` and `floatExp`.
        // Placeholder implementation
    }

	@Override
	public String toString() {
		return "License [id=" + id + ", name=" + name + ", companyId=" + companyId + ", status=" + status
				+ ", creationDate=" + creationDate + ", startDate=" + startDate + ", expiryDate=" + expiryDate
				+ ", licenseKey=" + licenseKey + ", floatExp=" + floatExp + ", hardwareLock=" + hardwareLock
				+ ", signKey=" + signKey + ", properties=" + properties + "]";
	}



 
}
