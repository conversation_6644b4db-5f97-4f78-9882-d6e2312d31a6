package com.accolm.licenseManager.Services;

import java.util.List;

import org.springframework.security.core.userdetails.UserDetailsService;

import com.accolm.licenseManager.Entities.Contact;
import com.accolm.licenseManager.Entities.User;

// leverage spring security userdetails dao methods
public interface UserService  {

	// create a user
	User createUser(User user);

	// get all users
	List<User> getAllUsers();

	// count of users
	long  getCountOfUsers();

	// update user
	String updateUser(User user);

	// delete user
	String deleteUser(String userName);
	


}
