package com.accolm.licenseManager.DAO;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.Query;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.accolm.licenseManager.Entities.Company;
import com.accolm.licenseManager.Entities.MaintenanceSupport;
import com.accolm.licenseManager.Utils.LocalEntityManagerFactory;

public class MaintenanceSupportDAOImpl implements MaintenanceSupportDAO {

	Logger logger = LogManager.getLogger(MaintenanceSupportDAOImpl.class);

	EntityManager em;

	public MaintenanceSupportDAOImpl() {
		em = LocalEntityManagerFactory.createEntityManager();
	}

	@Override
	public MaintenanceSupport createMaintenanceSupport(MaintenanceSupport request) {

		logger.info("Creating a MaintenanceSupport request...");
		MaintenanceSupport tempMaintenanceSupport = request;
		// save
		try {
			// managed transactions by RESOURCE_LOCAL
			EntityTransaction tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.
			tempMaintenanceSupport = em.merge(tempMaintenanceSupport); // create the entity
			tx.commit(); // commit the transaction
			logger.info("maintenanceSupport request successfully saved.");
		} catch (Exception e) {
			logger.error("Error saving maintenanceSupport request: ", e);
		} finally {
			em.close(); // close persistence context.
		}

		return tempMaintenanceSupport;
	}

	@Override
	public List<MaintenanceSupport> fetchAllMaintenanceSupportRequests() {
		logger.info("Get all maintenanceSupports requests");
		List<MaintenanceSupport> maintenanceSupports = null;
		// READ
		try {
			// managed transactions by RESOURCE_LOCAL
			EntityTransaction tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.
			// create q query.
			Query query = em.createQuery("SELECT ms from MaintenanceSupport ms"); // JPQL

			// execute query.
			maintenanceSupports = query.getResultList();

			tx.commit(); // commit the transaction
			logger.info("MaintenanceSupport requests returned successfully.");
		} catch (Exception e) {
			logger.error("Error getting all MaintenanceSupport requests: ", e);
		} finally {
			em.close(); // close persistence context.
		}

		return maintenanceSupports;
	}

	@Override
	public MaintenanceSupport fetchAllMaintenanceSupportRequestById(int id) {

		logger.info("Get maintenance-support by ID: {}", id);

		MaintenanceSupport maintenanceSupport = null;

		// READ
		try {
			// managed transcations by RESOURCE_LOCAL
			EntityTransaction tx = em.getTransaction(); // get a transaction to the database
			if (!tx.isActive()) {
				tx.begin(); // begin a transaction
			}

			// Find the company (create q query)
			maintenanceSupport = em.find(MaintenanceSupport.class, id);

			// Commit the transaction
			if (tx.isActive()) {
				tx.commit();
			}

			logger.info("maintenance-support request returned successfully.");
		} catch (Exception e) {
			logger.error("Error getting company: ", e);

			throw new RuntimeException("Failed to fetch maintenance-support with ID " + id, e);
		} finally {
			if (em.isOpen()) {
				em.close();
			}
		}

		return maintenanceSupport;

	}

}
