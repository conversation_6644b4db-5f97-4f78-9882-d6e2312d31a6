package com.accolm.licenseManager.Security;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;

import org.apache.tomcat.dbcp.dbcp2.BasicDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.access.ExceptionTranslationFilter;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import com.accolm.licenseManager.Security.Authentication.ActiveDirectoryAuthenticationProvider;
import com.accolm.licenseManager.Security.Authentication.UsernamePasswordAuthenticationFilterFailureHandler;
import com.accolm.licenseManager.Security.Authentication.UsernamePasswordAuthenticationFilterSuccessHandler;
import com.accolm.licenseManager.Security.Filters.LicenseManagerExceptionTranslationFilter;
import com.accolm.licenseManager.Security.Provisioning.LicenseManagerJdbcUserDetailsManager;
import com.accolm.licenseManager.Utils.Settings;

@EnableWebSecurity // in this case, leverage on Spring Security default security filters.
@Configuration // create spring beans on startup of the application via Annotation
public class LicenseManagerApplicationSecurityConfiguration {

	// Authentication
	// Authorization.

	@Bean // A chain of filters collected from Spring Security Framework
	public SecurityFilterChain appSecurityFilterChain(HttpSecurity http) throws Exception {

		http.authorizeHttpRequests().requestMatchers("/userLogin").permitAll(); // send the request to a filter url.
		http.authorizeHttpRequests().requestMatchers("/blanklicense.html").authenticated(); // licenses pages in the
																							// application
		http.authorizeHttpRequests().requestMatchers("/companies.html").authenticated(); // licenses pages in the
																							// application
		http.authorizeHttpRequests().requestMatchers("/companiesData.html").authenticated(); // licenses pages in the
																								// application
		http.authorizeHttpRequests().requestMatchers("/company-new.html").authenticated(); // licenses pages in the
																							// application
		http.authorizeHttpRequests().requestMatchers("/importkey.html").authenticated(); // licenses pages in the
																							// application
		http.authorizeHttpRequests().requestMatchers("/importlicense.html").authenticated(); // licenses pages in the
																								// application
		http.authorizeHttpRequests().requestMatchers("/importtemplate.html").authenticated(); // licenses pages in the
																								// application
		http.authorizeHttpRequests().requestMatchers("/index.html").authenticated(); // licenses pages in the
																						// application
		http.authorizeHttpRequests().requestMatchers("/keymanager.html").authenticated(); // licenses pages in the
																							// application
		http.authorizeHttpRequests().requestMatchers("/licenses.html").authenticated(); // licenses pages in the
																						// application
		http.authorizeHttpRequests().requestMatchers("/listkeys.html").authenticated(); // licenses pages in the
																						// application
		http.authorizeHttpRequests().requestMatchers("/templatelicense.html").authenticated(); // licenses pages in the
																								// application
		http.authorizeHttpRequests().requestMatchers("/templatemanager.html").authenticated(); // licenses pages in the
																								// application
		http.authorizeHttpRequests().requestMatchers("/templates.html").authenticated(); // licenses pages in the
																							// application

		// set authentication request on pages
		http.authorizeHttpRequests().requestMatchers("/m&s.html").authenticated();
		http.authorizeHttpRequests().requestMatchers("/createUser.html").authenticated();
		http.authorizeHttpRequests().requestMatchers("/manageUsers.html").authenticated();
		http.authorizeHttpRequests().requestMatchers("/contacts.html").authenticated();

		http.csrf().disable(); // disable csrf for the post to be allowed.

		http.addFilterAt(usernamePasswordAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
		http.addFilterAt(exceptionTranslationFilter(), ExceptionTranslationFilter.class); // verbose implementation
																							// session timeout
		return http.build(); // create the SecurityFilterChain, i.e a collection of security filters from
								// spring.

	}

	// when the /userLogin request is invoked from form submission.
	// reply with the UsernamePasswordAuthenticationFilter.
	@Bean
	public UsernamePasswordAuthenticationFilter usernamePasswordAuthenticationFilter() {

		UsernamePasswordAuthenticationFilter usernamePasswordAuthenticationFilter = new UsernamePasswordAuthenticationFilter();
		usernamePasswordAuthenticationFilter.setFilterProcessesUrl("/userLogin"); // html form POST to on this url for
																					// handling.
		usernamePasswordAuthenticationFilter.setAuthenticationManager(authenticationManager());
		usernamePasswordAuthenticationFilter.setUsernameParameter("username"); // form has username paramater
		usernamePasswordAuthenticationFilter.setPasswordParameter("password"); // form has password parameter.
		usernamePasswordAuthenticationFilter.setPostOnly(true);
		usernamePasswordAuthenticationFilter.setAuthenticationSuccessHandler(onSuccessAuthenticationHandler());
		usernamePasswordAuthenticationFilter.setAuthenticationFailureHandler(onFailuerAuthenticationHandler());
		return usernamePasswordAuthenticationFilter;
	}

	@Bean
	LicenseManagerExceptionTranslationFilter exceptionTranslationFilter() {

		LicenseManagerExceptionTranslationFilter exceptionTranslationFilter = new LicenseManagerExceptionTranslationFilter(
				new AuthenticationEntryPoint() {

					@Override
					public void commence(HttpServletRequest request, HttpServletResponse response,
							AuthenticationException authException) throws IOException, ServletException {
						response.sendRedirect(request.getContextPath().concat("/"));
					}
				});

		exceptionTranslationFilter.setAccessDeniedHandler(new AccessDeniedHandler() {

			@Override
			public void handle(HttpServletRequest request, HttpServletResponse response,
					AccessDeniedException accessDeniedException) throws IOException, ServletException {
				// forward the response to the error page
				response.sendRedirect(request.getContextPath().concat("/access-denied.html"));
			}
		});

		return exceptionTranslationFilter;
	}

	@Bean
	AuthenticationSuccessHandler onSuccessAuthenticationHandler() {
		return new UsernamePasswordAuthenticationFilterSuccessHandler();
	}

	@Bean
	AuthenticationFailureHandler onFailuerAuthenticationHandler() {
		return new UsernamePasswordAuthenticationFilterFailureHandler();
	}

	// set up the type(s) of authentication to be understood by the application.
	@Bean
	public AuthenticationManager authenticationManager() {

		return new ProviderManager(List.of(databaseAuthenticationProvider(), activeDirectoryAuthenticationProvider()));
	}

	@Bean
	DaoAuthenticationProvider databaseAuthenticationProvider() {
		DaoAuthenticationProvider databaseAuthenticationProvider = new DaoAuthenticationProvider();
		databaseAuthenticationProvider.setUserDetailsService(licenseManagerJdbcUserDetailsManager(dataSource()));
		databaseAuthenticationProvider.setPasswordEncoder(passwordEncoder());
		return databaseAuthenticationProvider;

	}

	@Bean
	public PasswordEncoder passwordEncoder() {
		return PasswordEncoderFactories.createDelegatingPasswordEncoder();
	}

	// database authentication setup.
	// 1. DDL layering on the table.

	// 2. DataSource connection.
	@Bean
	BasicDataSource dataSource() {
		BasicDataSource dataSource = new BasicDataSource();

		// PropertyFile
		dataSource.setDriverClassName(Settings.getProperty("db.driverClassName"));
		dataSource.setUrl(Settings.getProperty("db.url"));
		dataSource.setUsername(Settings.getProperty("db.username"));
		dataSource.setPassword(Settings.getProperty("db.password"));

		return dataSource;
	}

	// 3. LicenseManagerJdbcUserDetailsManager, DAO - expose JDBCUserDetailsManager
	// handling dao operations.
	// Provides CRUD operations for both users and groups
	@Bean
	LicenseManagerJdbcUserDetailsManager licenseManagerJdbcUserDetailsManager(DataSource dataSource) {
		LicenseManagerJdbcUserDetailsManager users = new LicenseManagerJdbcUserDetailsManager(dataSource);
		return users;
	}

	// Active Directory Authentication provider.
	@Bean
	ActiveDirectoryAuthenticationProvider activeDirectoryAuthenticationProvider() {
		// PropertyFile
		String domain = Settings.getProperty("ad.domain");
		String url = Settings.getProperty("ad.url");

		return new ActiveDirectoryAuthenticationProvider(domain, url);

	}

	public static void main(String[] args) {
	}

}
