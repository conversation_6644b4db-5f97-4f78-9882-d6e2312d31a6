package com.accolm.licenseManager.RestInterface;

import java.util.List;

import javax.ws.rs.*;
import java.io.InputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.glassfish.jersey.media.multipart.FormDataParam;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;

import com.accolm.licenseManager.Entities.SigningKey;
import com.accolm.licenseManager.Services.SigningKeyService;



@Path("/keys") // Base path for all end points related to Signing Keys
public class SigningKeyRI {

    private SigningKeyService service = new SigningKeyService();

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Response getSigningKeys(@QueryParam("page") @DefaultValue("0") int page) {
        List<SigningKey> keys = service.getSigningKeys(page);
        if (keys == null || keys.isEmpty()) {
            return Response.status(Response.Status.NO_CONTENT).entity("No signing keys found.").build();
        }
        return Response.ok(keys).build();
    }
    
    
    // Retrieve a specific signing key by ID
    @GET
    @Path("get/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getSigningKey(@PathParam("id") int id) {
        if (id <= 0) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Invalid ID provided.").build();
        }
        SigningKey key = service.getSigningKey(id);
        if (key == null || key.getId() <= 0) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("Signing key with ID " + id + " not found.")
                    .build();
        }
        return Response.ok(key).build();
    }

    // Save a new signing key
    @POST
    @Path("save")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response saveSigningKey(SigningKey signingKey) {
        if (signingKey == null || signingKey.getName() == null || signingKey.getName().trim().isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Invalid signing key data provided.").build();
        }
        SigningKey savedKey = service.saveSigningKey(signingKey);
        /*if (savedKey == null || savedKey.getId() <= 0) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("Failed to save signing key.")
                    .build();
        }*/
        
        if (savedKey == null) {
            return Response.status(Response.Status.CONFLICT)
                    .entity("Signing key not saved: name or key already exists.")
                    .build();
        }
        if (savedKey.getId() <= 0) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("Failed to save signing key due to unknown error.")
                    .build();
        }

        return Response.status(Response.Status.CREATED).entity(savedKey).build();
    }

    // Update an existing signing key
    @PUT
    @Path("update")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public Response updateSigningKey(SigningKey signingKey) {
        if (signingKey == null || signingKey.getId() <= 0) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Invalid signing key data provided.").build();
        }
        String updateResult = service.updateSigningKey(signingKey);
        if (updateResult.contains("Error")) {
            return Response.status(Response.Status.BAD_REQUEST).entity(updateResult).build();
        }
        return Response.ok("Signing key updated successfully.").build();
    }

    // Delete a signing key by ID
    @DELETE
    @Path("delete/{id}")
    @Produces(MediaType.TEXT_PLAIN)
    public Response deleteSigningKey(@PathParam("id") int id) {
        if (id <= 0) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Invalid ID provided.").build();
        }
        String deleteResult = service.deleteSigningKey(id);
        if (deleteResult.contains("Error")) {
            return Response.status(Response.Status.NOT_FOUND).entity(deleteResult).build();
        }
        return Response.ok("Signing key deleted successfully.").build();
    }

    // Count the total number of signing keys
    @GET
    @Path("count")
    @Produces(MediaType.TEXT_PLAIN)
    public Response countSigningKeys() {
        long count = service.countSigningKeys();
        return Response.ok(count).build();
    }

    // Generate a new signing key with a custom or default name
    @POST
    @Path("generate")
    @Produces(MediaType.APPLICATION_JSON)
    public Response generateSigningKey(@QueryParam("name") String name) {
        if (name == null || name.trim().isEmpty()) {
            name = "DefaultKey-" + System.currentTimeMillis();
        }
        SigningKey newKey = service.generateSigningKey(name);
        if (newKey == null || newKey.getId() <= 0) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("Failed to generate signing key.")
                    .build();
        }
        return Response.ok(newKey).build();
    }

    // Search signing keys by input
    @GET
    @Path("/search/{input}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response searchSigningKeys(@PathParam("input") String input) {
        if (input == null || input.trim().isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Search input cannot be empty.").build();
        }
        List<SigningKey> result = service.search(input);
        if (result == null || result.isEmpty()) {
            return Response.status(Response.Status.NOT_FOUND).entity("No signing keys found for the input provided.").build();
        }
        return Response.ok(result).build();
    }
    
    
    // Export a signing key
    @GET
    @Path("export/{id}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response exportSigningKey(@PathParam("id") int id) {
        String tempDir = System.getProperty("java.io.tmpdir");
        String exportResult = service.exportSigningKey(id, tempDir);

        if (exportResult.contains("Error")) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("Error exporting signing key with ID " + id)
                    .build();
        }

        SigningKey key = service.getSigningKey(id);
        if (key == null) {
            return Response.status(Response.Status.NOT_FOUND).entity("Signing key with ID " + id + " not found.").build();
        }

        File exportedFile = new File(tempDir, key.getName() + "_Signing_Key.key");
        if (!exportedFile.exists()) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("Exported file not found for signing key with ID " + id)
                    .build();
        }

        Response.ResponseBuilder response = Response.ok(exportedFile);
        response.header("Content-Disposition", "attachment; filename=" + exportedFile.getName());
        return response.build();
    }

    // Import signing key with file upload
    @POST
    @Path("import")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.TEXT_PLAIN)
    public Response importSigningKey(
        @FormDataParam("file") InputStream fileInputStream,
        @FormDataParam("file") FormDataContentDisposition fileDetail) {

        if (fileInputStream == null || fileDetail == null) {
            return Response.status(Response.Status.BAD_REQUEST).entity("No file uploaded.").build();
        }

        // Validate file extension to ensure it's a signing key file
        String fileName = fileDetail.getFileName();
        if (!fileName.endsWith(".key")) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Invalid file type. Only .key files are allowed.").build();
        }

        // Save the uploaded file to a temporary directory
        String tempFilePath = System.getProperty("java.io.tmpdir") + "/" + fileName;
        try (FileOutputStream outStream = new FileOutputStream(tempFilePath)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                           .entity("Error saving uploaded file: " + e.getMessage())
                           .build();
        }

        // Process the imported file using the service
        /*String result = service.importSigningKey(tempFilePath);
        if (result.contains("Error")) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(result).build();
        }*/
        
        String result = service.importSigningKey(tempFilePath);
        if (result == null || result.toLowerCase().contains("failed") || result.toLowerCase().contains("error")) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }


        return Response.ok(result).build();
    }
}
