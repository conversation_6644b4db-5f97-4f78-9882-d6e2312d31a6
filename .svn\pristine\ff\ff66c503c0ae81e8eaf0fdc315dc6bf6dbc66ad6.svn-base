package com.accolm.licenseManager.DAO;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.Query;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.accolm.licenseManager.Entities.Company;
import com.accolm.licenseManager.Entities.Contact;
import com.accolm.licenseManager.Utils.LocalEntityManagerFactory;

public class ContactDAOImpl implements ContactDAO {

	Logger logger = LogManager.getLogger(ContactDAOImpl.class);

	EntityManager em;

	public ContactDAOImpl() {
		em = LocalEntityManagerFactory.createEntityManager();
	}

	@Override
	public Contact createContact(Contact contact) {

		logger.info("Creating a contact for company with companyId, {} ", contact.getCompanyId());
		Contact tempContact = null;

		EntityTransaction tx = null;
		// save
		try {
			// managed transactions by RESOURCE_LOCAL
			tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.
			tempContact = em.merge(contact); // create the entity
			tx.commit(); // commit the transaction
			logger.info("Contact created successfully.");
		} catch (Exception e) {

			logger.error("Error creating contact: ", e);
			throw new RuntimeException(
					"Failed to create contact for company with companyId, " + contact.getCompanyId());

		} finally {
			em.close(); // close persistence context.
		}

		return tempContact;

	}


	@Override
	public boolean deleteContact(int id) {
		logger.info("Delete contact");
		// start transaction
		EntityTransaction tx = null;
		boolean isDeleted = false;
		try {
			tx = em.getTransaction();
			tx.begin();

			// create query to delete company.
			Contact theContact = em.find(Contact.class, id);
			em.remove(theContact);

			// commit transaction
			tx.commit();
			isDeleted = true;
			logger.info("contact successfully deleted");

		} catch (Exception e) {
			if (tx != null && tx.isActive()) {
				tx.rollback();
			}
			logger.error("Error deleting contact", e);
		} finally {
			if (em.isOpen()) {
				em.close();
			}
		}

		return isDeleted;

	}

	@Override
	public Contact updateContact(Contact contact) {
		logger.info("updating a contact...");
		Contact tempContact = null;
		// save
		try {
			// managed transactions by RESOURCE_LOCAL
			EntityTransaction tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.
			tempContact = em.merge(contact); // create the entity
			tx.commit(); // commit the transaction
			logger.info("Contact updated successfully.");
		} catch (Exception e) {
			logger.error("Error updating contact: ", e);
		} finally {
			em.close(); // close persistence context.
		}

		return tempContact;
	}

	@Override
	public List<Contact> getContactsByCompanyId(int companyId) {

		logger.info("Get contact by companyId : {}", companyId);

		List<Contact> contacts = null;

		// READ
		try {
			// managed transcations by RESOURCE_LOCAL
			EntityTransaction tx = em.getTransaction(); // get a transaction to the database
			if (!tx.isActive()) {
				tx.begin(); // begin a transaction
			}

			Query theQuery = em.createQuery("SELECT c FROM Contact c where c.companyId = :companyId");
			theQuery.setParameter("companyId", Integer.valueOf(companyId));

			contacts = theQuery.getResultList();

			// Commit the transaction
			if (tx.isActive()) {
				tx.commit();
			}

			logger.info("Contacts from Company returned successfully.");
		} catch (Exception e) {
			logger.error("Error getting contacts from company: ", e);

			throw new RuntimeException("Failed to fetch contacts  with companyId " + companyId, e);
		} finally {
			if (em.isOpen()) {
				em.close();
			}
		}

		return contacts;

	}

}
