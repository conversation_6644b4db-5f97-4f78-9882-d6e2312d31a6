package com.accolm.licenseManager.Services;

import java.security.KeyPair;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.apache.commons.codec.binary.Hex; 
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.accolm.licenseManager.Entities.SigningKey;
import com.accolm.licenseManager.DAO.SigningKeyDao;
import com.accolm.licenseManager.Utils.LicenseKeyGenerator;

public class SigningKeyService {

    private final SigningKeyDao dao = new SigningKeyDao();
    private final LicenseKeyGenerator keyGenerator = new LicenseKeyGenerator();
    Logger logger = LogManager.getLogger(SigningKeyService.class);

    // Retrieve all signing keys with pagination
    public List<SigningKey> getSigningKeys(int page) {
        if (page < 0) {
            page = 0; // Default to first page if invalid page number provided
        }
        return dao.getSigningKeys(page);
    }

    // Retrieve a specific signing key by ID
    public SigningKey getSigningKey(int id) {
        if (id <= 0) {
            SigningKey errorResponse = new SigningKey();
            errorResponse.setId(id);
            errorResponse.setName("Invalid request: ID must be greater than 0.");
            errorResponse.setValue("Invalid ID provided.");
            return errorResponse;
        }
        return dao.get(id);
    }

    // Save a new signing key
    public SigningKey saveSigningKey(SigningKey signingKey) {
        if (signingKey == null || signingKey.getName() == null || signingKey.getName().trim().isEmpty()) {
            SigningKey errorResponse = new SigningKey();
            errorResponse.setName("Error: Invalid SigningKey object provided.");
            return errorResponse;
        }
        
     // Auto-generate keys if not provided
        if ((signingKey.getPrivate_key() == null || signingKey.getPrivate_key().isEmpty()) &&
            (signingKey.getPublic_key() == null || signingKey.getPublic_key().isEmpty())) {

            KeyPair keyPair = dao.getKey();
            signingKey.setType(keyPair.getPublic().getAlgorithm());
            signingKey.setPublic_key(new String(Hex.encodeHex(keyPair.getPublic().getEncoded())));
            signingKey.setPrivate_key(new String(Hex.encodeHex(keyPair.getPrivate().getEncoded())));
        }

        return dao.add(signingKey);
    }

    // Update an existing signing key
    public String updateSigningKey(SigningKey signingKey) {
        if (signingKey == null || signingKey.getId() <= 0) {
            return "Error: Invalid SigningKey object provided.";
        }
        return dao.update(signingKey);
    }

    // Delete a signing key by ID
    public String deleteSigningKey(int id) {
        if (id <= 0) {
            return "Error: ID must be greater than 0.";
        }
        return dao.delete(id);
    }

    // Count the total number of signing keys
    public long countSigningKeys() {
        return dao.count();
    }

    // Generate a signing key
    public SigningKey generateSigningKey(String name) {
        if (name == null || name.trim().isEmpty()) {
            name = "DefaultKey-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        }

        KeyPair keyPair = dao.getKey();
        SigningKey signingKey = new SigningKey();
        signingKey.setName(name);
        signingKey.setType(keyPair.getPublic().getAlgorithm());

        signingKey.setPublic_key(new String(Hex.encodeHex(keyPair.getPublic().getEncoded())));
        signingKey.setPrivate_key(new String(Hex.encodeHex(keyPair.getPrivate().getEncoded())));

        return dao.add(signingKey);
    }

    // Generate a signing key with a system-generated default name
    public SigningKey generateDefaultKey() {
        String defaultName = keyGenerator.generateKey(16, 4, '-');
        return generateSigningKey(defaultName);
    }

    // Search signing keys by input
    public List<SigningKey> search(String input) {
        return dao.searchKey(input);
    }
    
    // Export signing key to a file 
    public String exportSigningKey(int id, String directoryPath) {
        return dao.exportSigningKey(id, directoryPath);
    }
    
    // Import signing key from a file
    public String importSigningKey(String filePath) {
        return dao.importSigningKey(filePath);
    }
}
