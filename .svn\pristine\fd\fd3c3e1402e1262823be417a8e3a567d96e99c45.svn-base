var PaginationStrip = Vue.component("pagination-strip", {
  props: ["currentPage", "pages", "maxPerPage", "itemCount"],
  methods: {
    async onChange(event) {
      const numberToGet = parseInt(event.target.value);
      this.$emit("update:maxPerPage", numberToGet);
      this.$emit("refresh");
    },
    async onNext() {
      if (this.currentPage < this.pages) {
        this.$emit("update:currentPage", this.currentPage + 1);
        this.$emit("refresh");
      }
    },
    async onPrev() {
      if (this.currentPage > 1) {
        this.$emit("update:currentPage", this.currentPage - 1);
        this.$emit("refresh");
      }
    },
    async onFirst() {
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onLast() {
      this.$emit("update:currentPage", this.pages);
      this.$emit("refresh");
    },
  },
  template: `
     <th class="tableHeader" colspan="10" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
      <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
      <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
        <span>Page {{currentPage}} of {{pages}}</span>
        <ul class="pagination pagination-sm" style="margin: 0; border: none;">
          <li :class="{disabled: currentPage === 1}" style="border: none;">
            <span @click="onFirst()" style="border: none;">
              <i class="glyphicon glyphicon-step-backward"></i>
            </span>
          </li>
          <li :class="{disabled: currentPage === 1}" style="border: none;">
            <span @click="onPrev()" style="border: none;">
              <i class="glyphicon glyphicon-chevron-left"></i>
            </span>
          </li>
          <li :class="{disabled: currentPage === pages}" style="border: none;">
            <span @click="onNext()" style="border: none;">
              <i class="glyphicon glyphicon-chevron-right"></i>
            </span>
          </li>
          <li :class="{disabled: currentPage === pages}" style="border: none;">
            <span @click="onLast()" style="border: none;">
              <i class="glyphicon glyphicon-step-forward"></i>
            </span>
          </li>
        </ul>
        <span style="white-space: nowrap;">Results per page : </span>
        <select class="pages-form-control" @change="onChange($event)" style="margin-left: 5px;">
  <option value="10" :selected="maxPerPage === 10">10</option>
  <option value="25" :selected="maxPerPage === 25">25</option>
  <option value="50" :selected="maxPerPage === 50">50</option>
  <option value="100" :selected="maxPerPage === 100">100</option>
</select>
      </div>
    </th>
  `,
});

// company-autocomplete component
Vue.component("company-autocomplete", {
  props: ["companies", "value"],
  data() {
    return {
      searchText: "",
      showDropdown: false,
      filteredCompanies: [],
      selectedCompany: null,
      highlightedIndex: -1,
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          // Find the company by ID and set the search text
          const company = this.companies.find((c) => c.id === parseInt(newVal));
          if (company) {
            this.searchText = company.name;
            this.selectedCompany = company;
          }
        } else {
          this.searchText = "";
          this.selectedCompany = null;
        }
      },
    },
    searchText(newVal) {
      if (!newVal) {
        this.selectedCompany = null;
        this.$emit("input", "");
      }

      // Filter companies based on search text
      this.filterCompanies();
    },
  },
  methods: {
    filterCompanies() {
      if (!this.searchText) {
        this.filteredCompanies = [];
        this.showDropdown = false;
        return;
      }

      const searchTerm = this.searchText.toLowerCase();
      this.filteredCompanies = this.companies.filter((company) =>
        company.name.toLowerCase().includes(searchTerm)
      );

      this.showDropdown = this.filteredCompanies.length > 0;
      this.highlightedIndex = -1;
    },
    selectCompany(company) {
      this.selectedCompany = company;
      this.searchText = company.name;
      this.$emit("input", company.id);
      this.showDropdown = false;
    },
    onFocus() {
      if (this.searchText) {
        this.filterCompanies();
      }
    },
    onBlur() {
      // Delay hiding dropdown to allow click events to register
      setTimeout(() => {
        this.showDropdown = false;

        // If there's text but no selection, reset
        if (this.searchText && !this.selectedCompany) {
          const matchingCompany = this.companies.find(
            (c) => c.name.toLowerCase() === this.searchText.toLowerCase()
          );

          if (matchingCompany) {
            this.selectCompany(matchingCompany);
          } else {
            this.searchText = this.selectedCompany
              ? this.selectedCompany.name
              : "";
          }
        }
      }, 200);
    },
    onKeyDown(e) {
      if (!this.showDropdown) return;

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          this.highlightedIndex = Math.min(
            this.highlightedIndex + 1,
            this.filteredCompanies.length - 1
          );
          this.scrollToHighlighted();
          break;
        case "ArrowUp":
          e.preventDefault();
          this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
          this.scrollToHighlighted();
          break;
        case "Enter":
          e.preventDefault();
          if (this.highlightedIndex >= 0) {
            this.selectCompany(this.filteredCompanies[this.highlightedIndex]);
          }
          break;
        case "Escape":
          e.preventDefault();
          this.showDropdown = false;
          break;
      }
    },
    scrollToHighlighted() {
      this.$nextTick(() => {
        const highlighted = this.$el.querySelector(".highlighted");
        if (highlighted) {
          highlighted.scrollIntoView({
            block: "nearest",
            behavior: "smooth",
          });
        }
      });
    },
    clearSelection() {
      this.searchText = "";
      this.selectedCompany = null;
      this.$emit("input", "");
    },
  },
  template: `
    <div class="company-autocomplete-container" style="position: relative; width: 100%;">
      <div class="input-group" style="width: 56%;">
        <input
          type="text"
          class="form-control"
          v-model="searchText"
          @focus="onFocus"
          @blur="onBlur"
          @keydown="onKeyDown"
          placeholder="Search for a company..."
          style="width: calc(100% - 34px); border-radius: 4px 0 0 4px;"
        >
        <div class="input-group-btn" style="width: 34px; position: absolute; right: 0; top: 0; height: 100%;">
          <button 
            v-if="searchText" 
            class="btn btn-default" 
            type="button" 
            @click="clearSelection"
            style="height: 100%; border-radius: 0 4px 4px 0;"
          >
            <i class="glyphicon glyphicon-remove"></i>
          </button>
          <button 
            v-else 
            class="btn btn-default" 
            type="button"
            style="height: 100%; border-radius: 0 4px 4px 0; cursor: default;"
            disabled
          >
            <i class="glyphicon glyphicon-search"></i>
          </button>
        </div>
      </div>
      <div 
        v-show="showDropdown" 
        class="dropdown-menu" 
        style="display: block; width: 56%; max-height: 300px; overflow-y: auto; position: absolute; z-index: 1000;"
      >
        <a 
          v-for="(company, index) in filteredCompanies" 
          :key="company.id"
          href="#" 
          class="dropdown-item" 
          :class="{ 'highlighted': index === highlightedIndex }"
          @mousedown.prevent="selectCompany(company)"
          @mouseover="highlightedIndex = index"
          style="display: block; padding: 8px 15px; text-decoration: none; color: #333;"
        >
          {{ company.name }}
        </a>
      </div>
    </div>
  `,
});

var displayLicenses = Vue.component("display-licenses", {
  components: {
    "pagination-strip": PaginationStrip,
    "company-autocomplete": Vue.component("company-autocomplete"),
  },
  data() {
    return {
      licenses: [],
      companies: [],
      pickKey: [],
      templates: [],
      selectedTemplate: null,
      templateFields: [],
      formValues: {},
      selectedFile: null,
      hasDeleted: false,
      isFetching: true,
      expirationEditable: true,
      currentPage: 1,
      pages: 0,
      licenseProperties: [],
      itemCount: 0,
      maxPerPage: 10,
      oneLicense: {
        id: "",
        name: "",
        status: "Valid",
        creationDate: "",
        startDate: "",
        expiryDate: "",
        floatExp: "",
        hardwareLock: "",
        properties: [{}],
        signKey: [{}],
      },
      did: 0,
      rowCount: 1,
      formData: {
        licenseName: "",
        expirationDate: "",
        floatingDays: "",
        hardwareLocking: "",
        company: "",
        importCompany: "",
        signingKey: "",
        templateId: "",
      },
      editFormData: {
        id: null,
        name: "",
        status: "",
        creationDate: "",
        startDate: "",
        expiryDate: "",
        floatExp: "",
        hardwareLock: false,
        properties: [],
        signKey: {
          id: 0,
          name: "",
        },
        companyId: 0,
      },
      disableBtn: true,
      noShow: false,
      search: "",
      maxVisibleButtons: 3,
    };
  },
  methods: {
    editLicense(id) {
      axios
        .get(`${this.apiBaseUrl}/licenses/get/${id}`)
        .then((response) => {
          if (response.status === 200) {
            // Create a copy of the license data for editing
            this.editFormData = { ...response.data };

            // Handle empty expiration date as "Never" for display
            if (
              !this.editFormData.expiryDate ||
              this.editFormData.expiryDate === ""
            ) {
              this.editFormData.expiryDate = "Never";
            }

            // Show the edit modal
            $("#editLicenseModal").modal("show");
          }
        })
        .catch((error) => {
          console.error("Error fetching license details for editing:", error);
          $("#errorModal").modal("show");
        });
    },

    // method to handle the update
    updateLicense() {
      // Validate form data
      if (!this.editFormData.name || !this.editFormData.startDate) {
        alert("Please fill in all required fields");
        return;
      }

      // Handle "Never" expiration - send empty string to backend
      if (this.editFormData.expiryDate === "Never") {
        this.editFormData.expiryDate = "";
      }

      // Send update request to the server
      axios
        .put(`${this.apiBaseUrl}/licenses/update`, this.editFormData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          if (response.status === 200) {
            $("#editLicenseModal").modal("hide");
            $("#editLicenseSuccess").modal("show");

            // Refresh the data
            this.displayData();
            setTimeout(() => {
              $("#editLicenseSuccess").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error updating license:", error);
          $("#editLicenseModal").modal("hide");
          $("#errorModal").modal("show");

          setTimeout(() => {
            $("#errorModal").modal("hide");
          }, 2000);
        });
    },

    // method to handle adding a property in edit mode
    addEditProperty() {
      if (!this.editFormData.properties) {
        this.editFormData.properties = [];
      }
      this.editFormData.properties.push({
        name: "",
        value: "",
      });
    },

    // method to handle removing a property in edit mode
    removeEditProperty(index) {
      if (
        this.editFormData.properties &&
        this.editFormData.properties.length > 0
      ) {
        this.editFormData.properties.splice(index, 1);
      }
    },

    async onTemplateSelect(event) {
      const templateId = event.target.value;
      if (templateId) {
        try {
          const response = await axios.get(
            `${this.apiBaseUrl}/templates/get/${templateId}`
          );
          this.selectedTemplate = response.data;
          this.templateFields = response.data.fields || [];

          // Log the template data to see what we're working with
          console.log("Selected template:", this.selectedTemplate);

          // Initialize form values for template fields
          this.initializeFormValues();

          // Clear existing license properties
          this.licenseProperties = [];

          // Determine the template's expiration type
          let templateExpiryType = "never";

          // Check if template has a fixed expiration date
          const hasFixedExpiry =
            this.selectedTemplate.licenseExpiry &&
            this.selectedTemplate.licenseExpiry !== "Never" &&
            this.selectedTemplate.licenseExpiry !== " " &&
            this.selectedTemplate.licenseExpiry.trim() !== "";

          // Check if template has floating expiry
          const hasFloatingExpiry =
            this.selectedTemplate.floatExpiry &&
            parseInt(this.selectedTemplate.floatExpiry) > 0;

          if (hasFixedExpiry) {
            templateExpiryType = "fixed";
          } else if (hasFloatingExpiry) {
            templateExpiryType = "floating";
          }

          console.log("Template expiry type:", templateExpiryType);

          // Set expirationEditable flag based on template settings
          // For templates with no expiry, field should be disabled
          // For templates with fixed expiry, field should be blank and editable
          // For templates with floating expiry, field should be pre-filled and editable
          this.expirationEditable = templateExpiryType !== "never";

          // Handle expiration date field based on template type
          if (templateExpiryType === "never") {
            // No expiration date
            console.log("Template has no expiry date");
            this.formData.expirationDate = "";
            this.formData.floatingDays = "";
          } else if (templateExpiryType === "fixed") {
            // Fixed expiration date - leave blank for user to choose
            console.log(
              "Template has fixed expiry type - leaving blank for user to choose"
            );
            this.formData.expirationDate = ""; // Clear the field for user input
            this.formData.floatingDays = "";
          } else if (templateExpiryType === "floating") {
            // Floating expiration - calculate based on template
            console.log(
              "Template has floating expiry:",
              this.selectedTemplate.floatExpiry
            );
            this.formData.floatingDays = this.selectedTemplate.floatExpiry;

            // For floating from creation, calculate an expiration date
            const today = new Date();
            const expiryDate = new Date(today);
            expiryDate.setDate(
              today.getDate() + parseInt(this.selectedTemplate.floatExpiry)
            );
            this.formData.expirationDate = expiryDate
              .toISOString()
              .split("T")[0];
            console.log(
              "Calculated expiration date:",
              this.formData.expirationDate
            );
          }

          // Apply template properties to license properties
          if (
            this.selectedTemplate.templateProperties &&
            this.selectedTemplate.templateProperties.length > 0
          ) {
            // Clone the template properties to avoid reference issues
            this.licenseProperties =
              this.selectedTemplate.templateProperties.map((prop) => ({
                name: prop.name || "",
                value: prop.value || "",
              }));
            console.log(
              "Populated license properties from template:",
              this.licenseProperties
            );
          } else {
            console.log("No template properties found");
          }
        } catch (error) {
          console.error("Error fetching template details:", error);
        }
      }
    },

    async getTemplates() {
      try {
        // First get the count of templates
        const countResponse = await axios.get(
          `${this.apiBaseUrl}/templates/count`
        );
        const totalTemplates = countResponse.data;

        // Calculate how many pages we need to fetch
        const pageSize = 10; // Assuming page size is 10
        const totalPages = Math.ceil(totalTemplates / pageSize);

        // Fetch all pages and combine results
        let allTemplates = [];
        for (let page = 1; page <= totalPages; page++) {
          const response = await axios.get(
            `${this.apiBaseUrl}/templates/pagination/${page}`
          );
          allTemplates = allTemplates.concat(response.data);
        }

        // Remove duplicates by creating a map with template ID as key
        const templateMap = {};
        allTemplates.forEach((template) => {
          templateMap[template.id] = template;
        });

        // Convert back to array and sort by ID (assuming newer templates have higher IDs)
        // This will ensure newer templates appear at the end of the list
        this.templates = Object.values(templateMap).sort((a, b) => a.id - b.id);

        console.log(
          "Fetched all templates (duplicates removed, sorted by ID):",
          this.templates.length
        );
      } catch (error) {
        console.error("Error fetching templates:", error);
      }
    },
    async getSigningKey() {
      const response = await axios.get(`${this.apiBaseUrl}/keys`);
      console.log("Fetched keys:", response.data);
      this.pickKey = response.data;
    },
    formatDate(dateString) {
      if (
        !dateString ||
        dateString === "" ||
        dateString === "Never" ||
        dateString === "null"
      ) {
        return "Never";
      }
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return "Never";
        }
        return date.toISOString().split("T")[0];
      } catch (e) {
        return "Never";
      }
    },

    async displayData() {
      this.isFetching = true;
      try {
        const [res, resCount] = await Promise.all([
          axios.get(
            `${this.apiBaseUrl}/licenses/pagination/${this.currentPage}`
          ),
          axios.get(`${this.apiBaseUrl}/licenses/count`),
        ]);

        if (res.status === 200 && resCount.status === 200) {
          const allLicenses = res.data;

          allLicenses.forEach((license) => {
            // Handle empty expiration dates as "Never"
            if (
              !license.expiryDate ||
              license.expiryDate === "" ||
              license.expiryDate === "null"
            ) {
              license.expiryDate = "Never";
            }
          });
          this.itemCount = resCount.data;
          this.pages = Math.ceil(this.itemCount / this.maxPerPage);

          // Calculate pagination slice based on selected page size
          const startIndex = (this.currentPage - 1) * this.maxPerPage;
          const endIndex = startIndex + this.maxPerPage;

          // Set paginated results using selected page size
          this.licenses = allLicenses.slice(startIndex, endIndex);
          console.log("Current page size:", this.maxPerPage);
          console.log("Displayed licenses:", this.licenses.length);
        }
      } catch (error) {
        console.error("Error fetching licenses:", error);
      } finally {
        this.isFetching = false;
      }
    },
    initializeFormValues() {
      this.formValues = {};
      if (this.templateFields) {
        this.templateFields.forEach((field) => {
          this.formValues[field.name] = "";
        });
      }
    },
    exportTemplate(licenseId) {
      axios
        .get(`${this.apiBaseUrl}/licenses/template/export/${licenseId}`, {
          responseType: "blob",
        })
        .then((response) => {
          const fileURL = window.URL.createObjectURL(response.data);
          const a = document.createElement("a");
          a.href = fileURL;
          a.setAttribute("download", `template_${licenseId}.json`);
          document.body.appendChild(a);
          a.click();
          $("#exportSuccessModal").modal("show");
        })
        .catch((error) => {
          $("#exportErrorModal").modal("show");
          console.error("Export failed:", error);
        });
    },
    reloadPage() {
      window.location.reload(true);
    },
    deleteLicense(id) {
      axios
        .delete(`${this.apiBaseUrl}/licenses/delete/${id}`)
        .then((res) => {
          if (res.status === 200 && res.data) {
            $("#deleteLicenseModal").modal("hide");
            $("#successModal").modal("show");
            $("#successModal").on("hidden.bs.modal", () => {
              this.displayData();
            });
          }
        })
        .catch(() => {
          $("#deleteLicenseModal").modal("hide");
          $("#errorModal").modal("show");
        });
    },
    displaySingleLicense(id) {
      axios.get(`${this.apiBaseUrl}/licenses/get/${id}`).then((response) => {
        this.oneLicense = response.data;
      });
    },
    displayDownloadLicense(id) {
      axios
        .get(`${this.apiBaseUrl}/licenses/download/${id}`, {
          responseType: "blob",
        })
        .then((response) => {
          if (response.status == 200) {
            const fileURL = window.URL.createObjectURL(response.data);
            const a = document.createElement("a");
            a.href = fileURL;
            const fileName =
              this.licenses.find((license) => license.id === id)?.name + ".lic";
            a.setAttribute("download", fileName);
            document.body.appendChild(a);
            a.click();
            $("#downloadSuccessModal").modal("show");
          }
        })
        .catch(() => {
          $("#errorModal").modal("show");
        });
    },

    async submitLicense() {
      try {
        //properties array from the licenseProperties
        const customProperties = this.licenseProperties.map((prop) => ({
          name: prop.name,
          value: prop.value,
        }));

        // Get current date in YYYY-MM-DD format for the start date
        const currentDate = new Date().toISOString().split("T")[0];

        // Get company name
        const selectedCompany = this.companies.find(
          (company) => company.id === parseInt(this.formData.company)
        );
        const companyName = selectedCompany ? selectedCompany.name : "Unknown";

        // Get template name
        const selectedTemplate = this.templates.find(
          (template) => template.id === parseInt(this.formData.templateId)
        );
        const templateName = selectedTemplate
          ? selectedTemplate.name
          : "Unknown";

        // Generate license name: Company-Template-Date-Random
        const randomSuffix = Math.floor(Math.random() * 10000)
          .toString()
          .padStart(4, "0");
        const formattedDate = currentDate.replace(/-/g, "");
        const generatedName = `${companyName}-${templateName}-${formattedDate}-${randomSuffix}`;

        // Handle expiration date - send empty string to backend when it should be "Never"
        let expiryDate = "";
        if (this.expirationEditable && this.formData.expirationDate) {
          expiryDate = this.formData.expirationDate;
        }

        const licenseData = {
          companyId: parseInt(this.formData.company),
          name: generatedName, // Use the generated name instead of user input
          status: "ACTIVE",
          creationDate: currentDate,
          startDate: currentDate, // Use current date as start date
          expiryDate: expiryDate, // Send empty string for "Never"
          floatExp: parseInt(this.formData.floatingDays) || 0, // Use 0 if not specified
          hardwareLock: this.formData.hardwareLocking === "true",
          signKey: {
            id: parseInt(this.formData.signingKey),
            name:
              this.pickKey.find(
                (key) => key.id === parseInt(this.formData.signingKey)
              )?.name || "",
          },
          properties: customProperties || [],
        };

        console.log("Submitting license data:", licenseData); // Debug log
        const response = await axios.post(
          `${this.apiBaseUrl}/licenses/create`,
          JSON.stringify(licenseData),
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        console.log("API response:", response);
        if (
          (response.status === 200 || response.status === 201) &&
          response.data
        ) {
          console.log("Success! Showing modal...");
          $("#createSuccessModal").modal("show");
          $("#createSuccessModal").on("hidden.bs.modal", () => {
            this.resetForm();
            this.displayData();

            $('a[href="#list"]').tab("show");
          });
        }
      } catch (error) {
        console.error("Error creating license:", error);
        // Show error modal
        $("#errorModalcreate").modal("show");
      }
    },
    handleDisabledExpiryClick() {
      // Show warning modal
      $("#expiryWarningModal").modal({
        backdrop: "static",
        keyboard: false,
      });
      $("#expiryWarningModal").modal("show");
    },

    resetForm() {
      this.formValues = {};
      this.formData.company = "";
      this.formData.templateId = "";
      this.formData.expirationDate = "";
      this.formData.signingKey = "";
      this.formData.hardwareLocking = "";
      this.selectedTemplate = null;
      this.templateFields = [];
      this.licenseProperties = [];
    },
    deleteModal(id) {
      this.did = id;
    },
    async searchLicenses(query) {
      if (query !== "") {
        if (query.startsWith(" ")) {
          this.search = "";
        } else {
          const res = await axios.get(
            `${this.apiBaseUrl}/licenses/search/${query}`
          );
          if (res.status == 200) {
            this.licenses = res.data;
          }
        }
      } else {
        this.displayData();
      }
    },

    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file && file.name.endsWith(".lic")) {
        this.selectedFile = file;
        const label = event.target.nextElementSibling;
        label.textContent = file.name;
      } else if (file) {
        $("#importErrorModal").modal("show");
        this.resetImportField();
      }
    },
    async getCompanies() {
      const response = await axios.get(`${this.apiBaseUrl}/companies`);
      this.companies = response.data;
    },

    addLicenseProperty() {
      if (!this.licenseProperties) {
        this.licenseProperties = [];
      }
      this.licenseProperties.push({
        name: "",
        value: "",
      });
    },

    removeLicenseProperty(index) {
      if (this.licenseProperties && this.licenseProperties.length > 0) {
        this.licenseProperties.splice(index, 1);
      }
    },

    importLicense() {
      if (!this.selectedFile) return;

      const formData = new FormData();
      formData.append("file", this.selectedFile);

      axios
        .post(`${this.apiBaseUrl}/licenses/template/import`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((response) => {
          $("#importSuccessModal").modal("show");
          this.resetImportField();
          this.displayData();
        })
        .catch((error) => {
          $("#importErrorModal").modal("show");
          console.error("Import failed:", error);
          this.resetImportField();
        });
    },
    resetImportField() {
      this.selectedFile = null;
      const input = document.getElementById("templateFile");
      const label = input.nextElementSibling;
      input.value = "";
      label.textContent = "Choose license (.lic)...";
    },
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },
  },
  mounted() {
    this.displayData();
    this.getSigningKey();
    this.getTemplates();
    this.getCompanies();

    $("#createSuccessModal").on("hidden.bs.modal", () => {
      this.resetForm();
      this.displayData();
      $('a[href="#list"]').tab("show");
    });
  },
  template: `
  <div class="main-section" style="font-family: 'Open Sans', sans-serif;">
      <div class="container">
          <div class="row">
              <div class="col-sm-12" style="text-align: center">
                  <h1>Licenses</h1>
              </div>
              <div class="col-sm-12">
                  <!-- Tabs Navigation -->
                  <ul class="nav nav-tabs" role="tablist">
                      <li class="active"><a href="#list" role="tab" data-toggle="tab">List Licenses</a></li>
                      <li><a href="#create" role="tab" data-toggle="tab">Create License</a></li>
                      <li><a href="#import" role="tab" data-toggle="tab">Import License</a></li>
                  </ul>
  
                  <!-- Tabs Content -->
                  <div class="tab-content">
                      <!-- List Tab -->
                      <div class="tab-pane active" id="list">
                          <div class="table-responsive">
                              <table class="table table-bordered table-hover">
                                  <thead>
                                      <tr>
                                          <pagination-strip
                                              :current-page.sync="currentPage"
                                              :pages="pages"
                                              :max-per-page.sync="maxPerPage"
                                              :item-count="itemCount"
                                              @refresh="displayData"
                                          ></pagination-strip>
                                      </tr>
                                      <tr>
                                          <th>Name</th>
                                          <th>License Code</th>
                                          <th>Status</th>
                                          <th>Creation Date</th>
                                          <th>Expiration Date</th>
                                          
                                          <th>Signing Key</th>
                                          <th>Action</th>
                                      </tr>
                                  </thead>
                                  <tbody v-show='!isFetching'>
                                      <tr v-for="license in licenses" :key="'license-' + license.id">
                                          <td style="cursor: pointer;" @click="displaySingleLicense(license.id)" data-toggle="modal" data-target="#licenseDetailsModal">
                                              <a href="#">{{ license.name }}</a>
                                          </td>
                                          <td>{{ license.licenseKey || 'N/A' }}</td>
                                          <td>
                                              <span :class="{'text-success': license.validationStatus, 'text-danger': !license.validationStatus}">
                                                  {{ license.validationStatus ? 'Valid' : 'Invalid' }}
                                              </span>
                                          </td>
                                          <td>{{ formatDate(license.creationDate) }}</td>
                                          
                                          <td>{{ formatDate(license.expiryDate) }}</td>

                                          <td>{{ license.signKey && license.signKey.name ? license.signKey.name : 'N/A' }}</td>
                                          <td>
                                              <div class="btn-group">
                                               <button @click="editLicense(license.id)" class="btn btn-light">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
      <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
    </svg>
  </button>
                                                  <button @click="displayDownloadLicense(license.id)" class="btn btn-light">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                          <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                                                          <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                                                      </svg>
                                                  </button>
                                                  <button data-toggle="modal" data-target="#deleteLicenseModal" @click="deleteModal(license.id)" class="btn btn-light">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                          <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                          <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                      </svg>
                                                  </button>
                                              </div>
                                          </td>
                                      </tr>
                                  </tbody>
                                  <tbody v-show="isFetching">
                        <tr>
                          <td colspan="8" class="text-center">
                            <p>Loading licenses...</p>
                          </td>
                        </tr>
                      </tbody>
                                  <tfoot>
                                      <tr>
                                                                                <pagination-strip
                                            :current-page.sync="currentPage"
                                            :pages="pages"
                                            :max-per-page.sync="maxPerPage"
                                            :item-count="itemCount"
                                            @refresh="displayData"
                                        ></pagination-strip>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>

<!-- Create License Tab -->
<div class="tab-pane" id="create">
    <div class="row">
        <div class="col-sm-2"></div>
        <div class="col-sm-8" style="margin-top: 29px;">
            <form class="form-horizontal" @submit.prevent="submitLicense">
                <div class="form-group">
                    <div class="col-sm-3">
                        <label class="control-label">Company</label>
                    </div>
                    <div class="col-sm-6">
                        <!-- Replace the select dropdown with the autocomplete component -->
                        <company-autocomplete 
                          :companies="companies" 
                          v-model="formData.company"
                        ></company-autocomplete>
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-sm-3">
                        <label class="control-label">Template</label>
                    </div>
                    <div class="col-sm-6">
                        <select v-model="formData.templateId" class="form-control" required @change="onTemplateSelect">
                            <option value="">Select Template</option>
                            <option v-for="template in templates" :key="'dropdown-template-' + template.id" :value="template.id">
                                {{template.name}}
                            </option>
                        </select>
                    </div>
                </div>

<!-- Additional fields that appear only when both company and template are selected -->
<div v-show="formData.company && formData.templateId">
   
   <div class="form-group">
    <div class="col-sm-3">
        <label class="control-label">Expiration Date</label>
    </div>
    <div class="col-sm-6">
        <!-- For disabled state (no expiry templates) -->
        <input v-if="!expirationEditable"
               type="text" 
               class="form-control" 
               value="Never"
               style="background-color: #f2f2f2; color: #999; cursor: not-allowed;"
               readonly
               @click="handleDisabledExpiryClick">
        <!-- For enabled state (fixed or floating expiry templates) -->
        <input v-else
               type="date" 
               v-model="formData.expirationDate" 
               class="form-control" 
               required
               :placeholder="selectedTemplate && selectedTemplate.licenseExpiry !== 'Never' ? 'Select expiration date' : ''">
        
    </div>
</div>

    <div class="form-group">
        <div class="col-sm-3">
            <label class="control-label">Signing Key</label>
        </div>
        <div class="col-sm-6">
            <select v-model="formData.signingKey" class="form-control" required>
                <option value="" disabled selected>Select Signing Key</option>
                <option v-for="key in pickKey" :key="'key-' + key.id" :value="key.id">
                    {{key.name}}
                </option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <div class="col-sm-3">
            <label class="control-label">Hardware Locking</label>
        </div>
        <div class="col-sm-6">
            <select v-model="formData.hardwareLocking" class="form-control" required>
                <option value=""disabled selected>Select Option</option>
                <option value="true">True</option>
                <option value="false">False</option>
            </select>
        </div>
    </div>

    <!-- Dynamic fields based on template -->
    <template v-if="selectedTemplate">
        <div class="form-group" v-for="(field, fieldIndex) in templateFields" :key="'template-field-' + fieldIndex">
            <div class="col-sm-3">
                <label class="control-label">{{field.label}}</label>
            </div>
            <div class="col-sm-6">
                <input 
                    :type="field.type" 
                    class="form-control" 
                    v-model="formValues[field.name]"
                    :required="field.required"
                >
            </div>
        </div>
    </template>
</div>

<div class="form-group" v-show="formData.company && formData.templateId">
  <div class="col-sm-12">
    <div class="card shadow mb-4">
      <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary" style="font-family: 'Open Sans';color: #ac2925;font-size: 14px">
          Additional License Properties
        </h6>
      </div>
      <div class="card-body">
        <table class="table table-hover table-bordered">
          <thead>
            <tr>
              <th>Property</th>
              <th>Value</th>
              <th>Action</th>
            </tr>
          </thead>
       <tbody>
  <tr v-for="(prop, index) in licenseProperties" :key="'create-prop-' + index">
    <td>
      <input v-model="prop.name" type="text" class="form-control" readonly>
    </td>
    <td>
      <input v-model="prop.value" type="text" class="form-control">
    </td>
    <td>
      <button type="button" @click="removeLicenseProperty(index)" class="btn btn-danger btn-sm">
        <i class="fas fa-trash-alt"></i>
      </button>
    </td>
  </tr>
</tbody>
        </table>
        <button type="button" @click="addLicenseProperty" class="btn btn-light">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
          Add Property
        </button>
      </div>
    </div>
  </div>
</div>

                <div class="form-group">
                    <div class="col-sm-3"></div>
                    <div class="col-sm-6">
                        <button type="submit" class="request-button form-control" :disabled="!formData.templateId || !formData.company">
                            Create License
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-sm-2"></div>
    </div>
</div>


                    <!-- Import Tab -->
                    <div class="tab-pane" id="import">
                        <div class="home-request">
                            <div class="row content-container">
                                <div class="col-sm-2"></div>
                                <div class="col-sm-8">
                                    <div class="form-horizontal" style="margin-top: 20px; padding: 20px;">
                                        <div class="form-group">
                                            <div class="col-sm-3">
                                                <label class="control-label">Company</label>
                                            </div>
                                            <div class="col-sm-6">
                                                <!-- Replace the select dropdown with the autocomplete component -->
                                                <company-autocomplete 
                                                  :companies="companies" 
                                                  v-model="formData.importCompany"
                                                ></company-autocomplete>
                                            </div>
                                        </div>
                                   
                                        <div class="form-group">
                                            <div class="col-sm-3">
                                                <label class="control-label">Upload License File</label>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="input">
                                                    <input type="file" 
                                                        id="templateFile" 
                                                        @change="handleFileSelect" 
                                                        accept=".lic" 
                                                        required 
                                                        class="form-control">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6">
                                                <button @click.prevent="importLicense" 
                                                    class="request-button form-control" 
                                                    :disabled="!selectedFile">
                                                    Import License
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-2"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- All Modals -->

                <!-- License Details Modal -->
                <div class="modal fade" id="licenseDetailsModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">License Details</h4>
                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                            </div>
                            <div class="modal-body">
                                <form class="form-horizontal">
                                    <div class="form-group">
                                        <div class="col-sm-3">
                                            <label class="control-label">License Name</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="text" v-model="oneLicense.name" disabled class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group">
    <div class="col-sm-3">
        <label class="control-label">License Code</label>
    </div>
    <div class="col-sm-6">
        <input type="text" v-model="oneLicense.licenseKey" disabled class="form-control">
    </div>
</div>

                                    <div class="form-group">
                                        <div class="col-sm-3">
                                            <label class="control-label">Start Date</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="date" v-model="oneLicense.startDate" disabled class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="col-sm-3">
                                            <label class="control-label">Expiration Date</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="date" v-model="oneLicense.expiryDate" disabled class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                     <div class="col-sm-3">
                                            <label class="control-label">Floating Days</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="number" v-model="oneLicense.floatExp" disabled class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="col-sm-3" style="padding-right: 0px;">
                                            <label class="control-label">Hardware Locking</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="text" v-model="oneLicense.hardwareLock" disabled class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="col-sm-3">
                                            <label class="control-label">Signing Key</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="text" v-model="oneLicense.signKey.name" disabled class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group">
  <div class="col-sm-12">
    <div class="panel panel-default">
      <div class="panel-heading">License Properties</div>
      <div class="panel-body">
        <table class="table table-hover table-bordered">
          <thead>
            <tr>
              <th>Property</th>
              <th>Value</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(prop, index) in oneLicense.properties" :key="'detail-prop-' + index">
              <td>{{prop.name}}</td>
              <td>{{prop.value}}</td>
            </tr>
          </tbody>
        </table>
        </div>
      </div>
    </div>
  </div>
  </form>
</div>

                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>

<!-- Edit License Modal -->
<div class="modal fade" id="editLicenseModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit License</h5>
        <button type="button" class="close" style="margin-top: -25px" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form class="form-horizontal">
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">License Name *</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="editFormData.name" class="form-control" required>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Start Date *</label>
            </div>
            <div class="col-sm-9">
              <input type="date" v-model="editFormData.startDate" class="form-control" required>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Expiration Date *</label>
            </div>
            <div class="col-sm-9">
              <input type="date" v-model="editFormData.expiryDate" class="form-control" required>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Floating Days</label>
            </div>
            <div class="col-sm-9">
              <input type="number" v-model="editFormData.floatExp" class="form-control">
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Hardware Locking</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editFormData.hardwareLock" class="form-control">
                <option :value="true">True</option>
                <option :value="false">False</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Signing Key</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editFormData.signKey.id" class="form-control">
                <option v-for="key in pickKey" :key="'edit-key-' + key.id" :value="key.id">
                  {{key.name}}
                </option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Status</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editFormData.status" class="form-control">
                <option value="ACTIVE">Active</option>
                <option value="INACTIVE">Inactive</option>
                <option value="EXPIRED">Expired</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Company</label>
            </div>
            <div class="col-sm-9">
              <!-- Replace the select dropdown with the autocomplete component -->
              <company-autocomplete 
                :companies="companies" 
                v-model="editFormData.companyId"
              ></company-autocomplete>
            </div>
          </div>

          <!-- License Properties Section -->
          <div class="form-group">
            <div class="col-sm-12">
              <div class="card shadow mb-4">
                <div class="card-header py-3">
                  <h6 class="m-0 font-weight-bold text-primary" style="font-family: 'Open Sans';color: #ac2925;font-size: 14px">
                    License Properties
                  </h6>
                </div>
                <div class="card-body">
                  <table class="table table-hover table-bordered">
                    <thead>
                      <tr>
                        <th>Property</th>
                        <th>Value</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                   <tbody>
  <tr v-for="(prop, index) in editFormData.properties" :key="'edit-prop-' + index">
    <td>
      <input v-model="prop.name" type="text" class="form-control" readonly>
    </td>
    <td>
      <input v-model="prop.value" type="text" class="form-control">
    </td>
    <td>
      <button type="button" @click="removeEditProperty(index)" class="btn btn-danger btn-sm">
        <i class="fas fa-trash-alt"></i>
      </button>
    </td>
  </tr>
</tbody>
                  </table>
                  <button type="button" @click="addEditProperty" class="btn btn-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                    </svg>
                    Add Property
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="request-button" @click="updateLicense">Save Changes</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Success Modal -->
<div class="modal fade" id="editLicenseSuccess" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>License updated successfully!</p>
      </div>
    </div>
  </div>
</div>


                <!-- Success Modal -->
                <div class="modal fade" id="successModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Success</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body text-center">
                                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                                <p>Operation completed successfully!</p>
                            </div>
                        </div>
                    </div>
                </div>

               <!-- Success Modal for create licenses page -->
<div class="modal fade" id="createSuccessModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>Operation completed successfully!</p>
            </div>
        </div>
    </div>
</div>


<!-- Error Modal for create licenses page -->
<div class="modal fade" id="errorModalcreate" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p>An error occurred. Please try again.</p>
            </div>
        </div>
    </div>
</div>


                <!-- Error Modal -->
                <div class="modal fade" id="errorModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Error</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body text-center">
                                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                                <p>An error occurred. Please try again.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Confirmation Modal -->
                <div class="modal fade" id="deleteLicenseModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Confirm Delete</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body text-center">
                                <p>Are you sure you want to delete this license?</p>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-danger" @click="deleteLicense(did)">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
`,
});

new Vue({
  el: "#license",
  components: {
    "display-licenses": displayLicenses,
  },
  template: `
        <div class="wrapper">
            <display-licenses></display-licenses>
        </div>
    `,
});
