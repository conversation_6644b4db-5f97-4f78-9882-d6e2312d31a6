<?xml version="1.0" encoding="UTF-8"?>
<persistence xmlns="http://xmlns.jcp.org/xml/ns/persistence"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/persistence
             http://xmlns.jcp.org/xml/ns/persistence/persistence_2_1.xsd"
	version="2.1">

	<persistence-unit name="LicenseManagerUnit"
		transaction-type="RESOURCE_LOCAL">
		<provider>org.eclipse.persistence.jpa.PersistenceProvider</provider>

		<!-- Entity Classes -->
		<class>com.accolm.licenseManager.Entities.License</class>
		<class>com.accolm.licenseManager.Entities.LicenseProperty</class>
		<class>com.accolm.licenseManager.Entities.SigningKey</class>
		<class>com.accolm.licenseManager.Entities.Template</class>
		<class>com.accolm.licenseManager.Entities.TemplateProperty</class>
		<class>com.accolm.licenseManager.Entities.Company</class>
		<class>com.accolm.licenseManager.Entities.Country</class>
		<class>com.accolm.licenseManager.Entities.Contact</class>
		<class>com.accolm.licenseManager.Entities.MaintenanceSupport</class>
		




		<exclude-unlisted-classes>true</exclude-unlisted-classes>

		<!-- Database Connection Properties -->
		<properties>

			<!-- LOCAL  -->
			<property name="javax.persistence.jdbc.url"
				value="*******************************************" />
			<property name="javax.persistence.jdbc.user" value="root" />
			<property name="javax.persistence.jdbc.password"
				value="phil" />
			<property name="javax.persistence.jdbc.driver"
				value="com.mysql.cj.jdbc.Driver" />


			<!--  61 SERVER   -->
<!-- 			<property name="javax.persistence.jdbc.url" -->
<!-- 			value="jdbc:mysql://**********:3306/license_manager" /> -->
<!-- 			<property name="javax.persistence.jdbc.user" value="mftapp" /> -->
<!-- 			<property name="javax.persistence.jdbc.password" -->
<!-- 			value="BlueObject1" /> -->
<!-- 			<property name="javax.persistence.jdbc.driver" -->
<!-- 			value="com.mysql.cj.jdbc.Driver" /> -->


			<!-- EclipseLink Specific Properties -->
			<property name="eclipselink.logging.level" value="FINE" />
			<property name="eclipselink.ddl-generation"
				value="create-tables" />
			<property name="eclipselink.ddl-generation.output-mode"
				value="database" />
		</properties>
	</persistence-unit>
</persistence>
