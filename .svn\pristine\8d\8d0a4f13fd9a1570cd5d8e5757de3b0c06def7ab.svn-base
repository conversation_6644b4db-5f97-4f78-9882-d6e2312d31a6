package com.accolm.licenseManager.Services;

import java.util.List;
import java.util.Optional;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;

import com.accolm.licenseManager.DAO.AuthorityDao;
import com.accolm.licenseManager.DAO.AuthorityDaoImpl;
import com.accolm.licenseManager.DAO.ContactDAO;
import com.accolm.licenseManager.DAO.ContactDAOImpl;
import com.accolm.licenseManager.DAO.UserDao;
import com.accolm.licenseManager.DAO.UserDaoImpl;
import com.accolm.licenseManager.Entities.Contact;
import com.accolm.licenseManager.Entities.License;
import com.accolm.licenseManager.Entities.User;

public class UserServiceImpl implements UserService {

	private UserDao userDaoImpl;

	AuthorityDao authorityDaoImpl;

	Logger logger = LogManager.getLogger(UserServiceImpl.class);

	public UserServiceImpl() {
		userDaoImpl = new UserDaoImpl();
		authorityDaoImpl = new AuthorityDaoImpl();
	}

	@Override
	public User createUser(User user) {

		user.setPassword(PasswordEncoderFactories.createDelegatingPasswordEncoder().encode(user.getPassword()));

		user = userDaoImpl.createUser(user);

		if (user != null) {

			authorityDaoImpl.insertUserAuthorities(user);
		}

		return user;
	}

	@Override
	public List<User> getAllUsers() {
		return userDaoImpl.getAllUsers();
	}

	@Override
	public long getCountOfUsers() {
		return userDaoImpl.getUsersCount();
	}

	@Override
	public User updateUser(User user) {
		if (user == null || user.getUsername() == null || user.getUsername().trim().isEmpty()) {
			return null;
		}
		
		if(null != user.getPassword()) {
			user.setPassword(PasswordEncoderFactories.createDelegatingPasswordEncoder().encode(user.getPassword()));
		}


		user = userDaoImpl.updateUser(user);

		if (user != null) {

			authorityDaoImpl.updateUserAuthorities(user);
		}

		return user;
	}

	@Override
	public String deleteUser(String userName) {
		if (null == userName) {
			return "invalid username  provided";
		}

		// revoke user priviledges before user deletion.
		String message = authorityDaoImpl.deleteUserAuthorities(userName);

		if (message.contains("successfully")) {

			message = userDaoImpl.deleteUser(userName);
		}

		return message;
	}

}
