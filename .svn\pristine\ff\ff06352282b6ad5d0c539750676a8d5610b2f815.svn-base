package com.accolm.licenseManager.Entities;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "template")
public class Template {

	@Id
	@Column(name = "Template_ID")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;

	@Column(name = "Name", nullable = false)
	private String name;

//	@Transient
//	private String expiryType;
//	
//	@Transient
//	private boolean floatFromFirstUse;

	@Column(name = "LicenseExpiry", nullable = true)
	private String licenseExpiry;

	@Column(name = "FloatingExpiry", nullable = true)
	private int floatExpiry;

	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name = "Template_ID") // Foreign key in TemplateProperty
	private List<TemplateProperty> templateProperties = new ArrayList<>();

	public void addTemplateProperty(TemplateProperty property) {
		this.templateProperties.add(property);
	}

	// Constructors
	public Template() {
	}

	public Template(int id, String name, String licenseExpiry, int floatExpiry) {
		this.id = id;
		this.name = name;
		this.licenseExpiry = licenseExpiry;
		this.floatExpiry = floatExpiry;
	}

//	public String getExpiryType() {
//		return expiryType;
//	}
//
//	public void setExpiryType(String expiryType) {
//		this.expiryType = expiryType;
//	}
//	
//	
//	public boolean getFloatFromFirstUse() {
//		return floatFromFirstUse;
//	}
//
//	public void setFloatFromFirstUse(boolean floatFromFirstUse) {
//		this.floatFromFirstUse = floatFromFirstUse;
//	}

	// Getters and Setters
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getLicenseExpiry() {
		return licenseExpiry;
	}

	public void setLicenseExpiry(String licenseExpiry) {
		this.licenseExpiry = licenseExpiry;
	}

	public int getFloatExpiry() {
		return floatExpiry;
	}

	public void setFloatExpiry(int floatExpiry) {
		this.floatExpiry = floatExpiry;
	}

	public List<TemplateProperty> getTemplateProperties() {
		return templateProperties;
	}

	public void setTemplateProperties(List<TemplateProperty> templateProperties) {
		this.templateProperties = templateProperties;
	}

	@Override
	public String toString() {
		return "Template{" + "id=" + id + ", name='" + name + '\'' + ", licenseExpiry='" + licenseExpiry + '\''
				+ ", floatExpiry=" + floatExpiry + ", templateProperties=" + templateProperties + '}';
	}
}
