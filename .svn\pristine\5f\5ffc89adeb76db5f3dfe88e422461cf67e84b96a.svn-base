// Pagination Strip Component (reused from other pages)
var PaginationStrip = Vue.component("pagination-strip", {
  props: ["currentPage", "pages", "maxPerPage", "itemCount"],
  methods: {
    async onChange(event) {
      const numberToGet = parseInt(event.target.value);
      this.$emit("update:maxPerPage", numberToGet);
      this.$emit("refresh");
    },
    async onNext() {
      if (this.currentPage < this.pages) {
        this.$emit("update:currentPage", this.currentPage + 1);
        this.$emit("refresh");
      }
    },
    async onPrev() {
      if (this.currentPage > 1) {
        this.$emit("update:currentPage", this.currentPage - 1);
        this.$emit("refresh");
      }
    },
    async onFirst() {
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onLast() {
      this.$emit("update:currentPage", this.pages);
      this.$emit("refresh");
    },
  },
  template: `
       <th class="tableHeader" colspan="8" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
        <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
        <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
          <span>Page {{currentPage}} of {{pages}}</span>
          <ul class="pagination pagination-sm" style="margin: 0; border: none;">
            <li :class="{disabled: currentPage === 1}" style="border: none;">
              <span @click="onFirst()" style="border: none;">
                <i class="glyphicon glyphicon-step-backward"></i>
              </span>
            </li>
            <li :class="{disabled: currentPage === 1}" style="border: none;">
              <span @click="onPrev()" style="border: none;">
                <i class="glyphicon glyphicon-chevron-left"></i>
              </span>
            </li>
            <li :class="{disabled: currentPage === pages}" style="border: none;">
              <span @click="onNext()" style="border: none;">
                <i class="glyphicon glyphicon-chevron-right"></i>
              </span>
            </li>
            <li :class="{disabled: currentPage === pages}" style="border: none;">
              <span @click="onLast()" style="border: none;">
                <i class="glyphicon glyphicon-step-forward"></i>
              </span>
            </li>
          </ul>
          <span style="white-space: nowrap;">Results per page : </span>
          <select class="pages-form-control" @change="onChange($event)" style="margin-left: 5px;">
            <option value="10" :selected="maxPerPage === 10">10</option>
            <option value="25" :selected="maxPerPage === 25">25</option>
            <option value="50" :selected="maxPerPage === 50">50</option>
            <option value="100" :selected="maxPerPage === 100">100</option>
          </select>
        </div>
      </th>
    `,
});

// Main Manage Users Component
var ManageUsersComponent = Vue.component("manage-users-component", {
  components: {
    "pagination-strip": PaginationStrip,
  },
  data() {
    return {
      customers: [],
      administrators: [],
      companies: [],
      loading: true,
      error: null,
      currentPage: 1,
      pages: 0,
      itemCount: 0,
      maxPerPage: 10,
      editFormData: {
        id: null,
        firstName: "",
        lastName: "",
        userName: "",
        email: "",
        password: "",
        companyId: 0,
        role: "",
        accountStatus: true,
      },
      deleteUserName: "",
      activeTab: "customers",
    };
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },

    displayedUsers() {
      return this.activeTab === "customers"
        ? this.customers
        : this.administrators;
    },
  },
  mounted() {
    this.fetchUsers();
    this.fetchCompanies();
  },
  methods: {
    async fetchUsers() {
      this.loading = true;
      try {
        // Fetch all users
        const response = await axios.get(`${this.apiBaseUrl}/users/all`);

        if (response.status === 200) {
          const allUsers = response.data || [];

          // Debug: Log all users to see their structure
          console.log("All users fetched:", allUsers);

          // Add unique IDs and filter by role
          const usersWithIds = allUsers.map((user, index) => {
            // Create a unique ID combining email, role, and index to avoid duplicates
            if (!user.id) {
              const emailPart = user.email || "no-email";
              const rolePart = user.role || "no-role";
              user.id = `${emailPart}_${rolePart}_${index}`;
            }
            return user;
          });

          // Separate users by role
          this.customers = usersWithIds.filter((user) => {
            const role = user.role ? user.role.toLowerCase() : "";
            return role === "user" || role === "customer";
          });

          this.administrators = usersWithIds.filter((user) => {
            const role = user.role ? user.role.toLowerCase() : "";
            return role === "admin" || role === "administrator";
          });

          // Debug: Log filtered results
          console.log("Customers:", this.customers);
          console.log("Administrators:", this.administrators);

          // Fetch company names for users
          await this.fetchUserCompanyNames();

          // Set pagination data based on active tab
          this.updatePaginationData();
        }
      } catch (error) {
        console.error("Error fetching users:", error);
        this.error = "Failed to load users";
      } finally {
        this.loading = false;
      }
    },

    async fetchUserCompanyNames() {
      const allUsers = [...this.customers, ...this.administrators];

      for (const user of allUsers) {
        if (user.companyId) {
          try {
            const companyResponse = await axios.get(
              `${this.apiBaseUrl}/companies/${user.companyId}`
            );
            if (companyResponse.data && companyResponse.data.name) {
              user.companyName = companyResponse.data.name;
            } else {
              user.companyName = "Unknown";
            }
          } catch (error) {
            console.error(`Error fetching company for user ${user.id}:`, error);
            user.companyName = "Error";
          }
        } else {
          user.companyName = "N/A";
        }
      }
    },

    async fetchCompanies() {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/companies`);
        this.companies = response.data || [];
      } catch (error) {
        console.error("Error fetching companies:", error);
      }
    },

    updatePaginationData() {
      const currentUsers = this.displayedUsers;
      this.itemCount = currentUsers.length;
      this.pages = Math.ceil(this.itemCount / this.maxPerPage);

      // Reset to first page if current page is beyond available pages
      if (this.currentPage > this.pages && this.pages > 0) {
        this.currentPage = 1;
      }
    },

    switchTab(tab) {
      this.activeTab = tab;
      this.currentPage = 1;
      this.updatePaginationData();
    },

    getPaginatedUsers() {
      const startIndex = (this.currentPage - 1) * this.maxPerPage;
      const endIndex = startIndex + this.maxPerPage;
      return this.displayedUsers.slice(startIndex, endIndex);
    },

    async toggleUserStatus(user) {
      try {
        const updatedUser = {
          ...user,
          accountStatus: !user.accountStatus,
        };

        const response = await axios.put(
          `${this.apiBaseUrl}/users/${user.id}`,
          updatedUser,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.status === 200) {
          // Update the user status in the local data
          user.accountStatus = !user.accountStatus;

          // Show success message
          $("#statusSuccessModal").modal("show");
          setTimeout(() => {
            $("#statusSuccessModal").modal("hide");
          }, 1500);
        }
      } catch (error) {
        console.error("Error updating user status:", error);
        $("#errorModal").modal("show");
        setTimeout(() => {
          $("#errorModal").modal("hide");
        }, 2000);
      }
    },

    editUser(user) {
      // Populate edit form with user data
      this.editFormData = {
        id: user.id,
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        userName: user.userName || "",
        email: user.email || "",
        password: "", // Don't pre-fill password for security
        companyId: user.companyId || 0,
        role: user.role || "",
        accountStatus:
          user.accountStatus !== undefined ? user.accountStatus : true,
      };

      // Show edit modal
      $("#editUserModal").modal("show");
    },

    async updateUser() {
      try {
        // Validate form data
        if (
          !this.editFormData.firstName ||
          !this.editFormData.lastName ||
          !this.editFormData.userName ||
          !this.editFormData.email ||
          !this.editFormData.role
        ) {
          alert("Please fill in all required fields");
          return;
        }

        // Prepare update data (exclude password if empty)
        const updateData = {
          id: this.editFormData.id,
          firstName: this.editFormData.firstName,
          lastName: this.editFormData.lastName,
          userName: this.editFormData.userName,
          email: this.editFormData.email,
          companyId: this.editFormData.companyId,
          role: this.editFormData.role,
          accountStatus: this.editFormData.accountStatus,
        };

        // Only include password if it's provided
        if (
          this.editFormData.password &&
          this.editFormData.password.trim() !== ""
        ) {
          updateData.password = this.editFormData.password;
        }

        // Call the API endpoint to update the user using PUT method
        const response = await axios.put(
          `${this.apiBaseUrl}/users/update`,
          updateData,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.status === 200) {
          $("#editUserModal").modal("hide");
          $("#editSuccessModal").modal("show");

          // Refresh the data to reflect changes
          this.fetchUsers();

          setTimeout(() => {
            $("#editSuccessModal").modal("hide");
          }, 2000);
        }
      } catch (error) {
        console.error("Error updating user:", error);
        $("#editUserModal").modal("hide");
        $("#errorModal").modal("show");

        setTimeout(() => {
          $("#errorModal").modal("hide");
        }, 2000);
      }
    },

    deleteModal(user) {
      // Store the userName instead of user id for deletion
      this.deleteUserName = user.userName;
      $("#deleteUserModal").modal("show");
    },

    async deleteUser() {
      try {
        const response = await axios.delete(
          `${this.apiBaseUrl}/users/delete/${this.deleteUserName}`
        );

        if (response.status === 200) {
          $("#deleteUserModal").modal("hide");
          $("#successModal").modal("show");

          // Refresh the data
          this.fetchUsers();

          setTimeout(() => {
            $("#successModal").modal("hide");
          }, 2000);
        }
      } catch (error) {
        console.error("Error deleting user:", error);
        $("#deleteUserModal").modal("hide");
        $("#errorModal").modal("show");

        setTimeout(() => {
          $("#errorModal").modal("hide");
        }, 2000);
      }
    },

    async deleteUser() {
      try {
        const response = await axios.delete(
          `${this.apiBaseUrl}/users/${this.deleteId}`
        );

        if (response.status === 200) {
          $("#deleteUserModal").modal("hide");
          $("#successModal").modal("show");

          // Refresh the data
          this.fetchUsers();

          setTimeout(() => {
            $("#successModal").modal("hide");
          }, 2000);
        }
      } catch (error) {
        console.error("Error deleting user:", error);
        $("#deleteUserModal").modal("hide");
        $("#errorModal").modal("show");

        setTimeout(() => {
          $("#errorModal").modal("hide");
        }, 2000);
      }
    },

    formatDate(dateString) {
      if (!dateString || dateString === "" || dateString === "null") {
        return "N/A";
      }
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return "N/A";
        }
        return date.toISOString().split("T")[0];
      } catch (e) {
        return "N/A";
      }
    },

    maskPassword(password) {
      if (!password) return "••••••••";
      return "••••••••";
    },
  },

  template: `
      <div class="main-section" style="font-family: 'Open Sans', sans-serif;">
          <div class="container">
              <div class="row">
                  <div class="col-sm-12" style="text-align: center">
                      <h1>Manage Users</h1>
                  </div>
                  <div class="col-sm-12">
                      <!-- Tabs Navigation -->
                      <ul class="nav nav-tabs" role="tablist">
                          <li :class="{active: activeTab === 'customers'}">
                              <a href="#customers" role="tab" data-toggle="tab" @click="switchTab('customers')">Customers</a>
                          </li>
                          <li :class="{active: activeTab === 'administrators'}">
                              <a href="#administrators" role="tab" data-toggle="tab" @click="switchTab('administrators')">Administrators</a>
                          </li>
                      </ul>
    
                      <!-- Tabs Content -->
                      <div class="tab-content">
                          <!-- Customers Tab -->
                          <div class="tab-pane" :class="{active: activeTab === 'customers'}" id="customers">
                              <div class="table-responsive">
                                  <table class="table table-hover">
                                      <thead>
                                          <tr>
                                              <pagination-strip
                                                  :current-page.sync="currentPage"
                                                  :pages="pages"
                                                  :max-per-page.sync="maxPerPage"
                                                  :item-count="itemCount"
                                                  @refresh="fetchUsers"
                                              ></pagination-strip>
                                          </tr>
                                          <tr>
                                              <th>First Name</th>
                                              <th>Last Name</th>
                                              <th>Username</th>
                                              <th>Email</th>
                                              <th>Password</th>
                                              <th>Company</th>
                                              <th>Role</th>
                                              <th>Account Status</th>
                                              <th>Actions</th>
                                          </tr>
                                      </thead>
                                      <tbody v-show='!loading'>
                                          <tr v-for="user in getPaginatedUsers()" :key="'customer-' + user.id" v-show="activeTab === 'customers'">
                                              <td>{{ user.firstName || 'N/A' }}</td>
                                              <td>{{ user.lastName || 'N/A' }}</td>
                                              <td>{{ user.userName || 'N/A' }}</td>
                                              <td>{{ user.email || 'N/A' }}</td>
                                              <td>{{ maskPassword(user.password) }}</td>
                                              <td>{{ user.companyName || 'N/A' }}</td>
                                              <td>
                                                  <span class="label label-info">{{ user.role || 'User' }}</span>
                                              </td>
                                                                                                                                          <td>
                                                  <label class="toggle-switch" style="transform: scale(0.7);">
                                                      <input type="checkbox" :checked="user.accountStatus" @change="toggleUserStatus(user)">
                                                      <span class="slider" :class="{'inactive': !user.accountStatus}"></span>
                                                  </label>
                                              </td>

                                              <td>
                                                  <div class="btn-group">
                                                      <button @click="editUser(user)" class="btn btn-light">
                                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                              <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                                                          </svg>
                                                      </button>
                                                      <button @click="deleteModal(user)" class="btn btn-light">
                                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                              <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                              <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                          </svg>
                                                      </button>
                                                  </div>
                                              </td>
                                          </tr>
                                      </tbody>
                                      <tbody v-show="loading">
                                          <tr>
                                              <td colspan="8" class="text-center">
                                                  <p>Loading users...</p>
                                              </td>
                                          </tr>
                                      </tbody>
                                      <tfoot>
                                          <tr>
                                              <pagination-strip
                                                  :current-page.sync="currentPage"
                                                  :pages="pages"
                                                  :max-per-page.sync="maxPerPage"
                                                  :item-count="itemCount"
                                                  @refresh="fetchUsers"
                                              ></pagination-strip>
                                          </tr>
                                      </tfoot>
                                  </table>
                              </div>
                          </div>
    
                          <!-- Administrators Tab -->
                          <div class="tab-pane" :class="{active: activeTab === 'administrators'}" id="administrators">
                              <div class="table-responsive">
                                  <table class="table table-hover">
                                      <thead>
                                          <tr>
                                              <pagination-strip
                                                  :current-page.sync="currentPage"
                                                  :pages="pages"
                                                  :max-per-page.sync="maxPerPage"
                                                  :item-count="itemCount"
                                                  @refresh="fetchUsers"
                                              ></pagination-strip>
                                          </tr>
                                          <tr>
                                              <th>First Name</th>
                                              <th>Last Name</th>
                                              <th>Username</th>
                                              <th>Email</th>
                                              <th>Password</th>
                                              <th>Role</th>
                                              <th>Account Status</th>
                                              <th>Actions</th>
                                          </tr>
                                      </thead>
                                      <tbody v-show='!loading'>
                                          <tr v-for="user in getPaginatedUsers()" :key="'admin-' + user.id" v-show="activeTab === 'administrators'">
                                              <td>{{ user.firstName || 'N/A' }}</td>
                                              <td>{{ user.lastName || 'N/A' }}</td>
                                              <td>{{ user.userName || 'N/A' }}</td>
                                              <td>{{ user.email || 'N/A' }}</td>
                                              <td>{{ maskPassword(user.password) }}</td>
                                              
                                              <td>
                                                  <span class="label label-warning">{{ user.role || 'Admin' }}</span>
                                              </td>
                                                                                            <td>
                                                  <label class="toggle-switch" style="transform: scale(0.7);">
                                                      <input type="checkbox" :checked="user.accountStatus" @change="toggleUserStatus(user)">
                                                      <span class="slider" :class="{'inactive': !user.accountStatus}"></span>
                                                  </label>
                                              </td>

                                          <td>
                                              <div class="btn-group">
                                                  <button @click="editUser(user)" class="btn btn-light">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                          <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                                                      </svg>
                                                  </button>
                                                  <button @click="deleteModal(user)" class="btn btn-light">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                          <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                          <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                      </svg>
                                                  </button>
                                              </div>
                                          </td>
                                      </tr>
                                  </tbody>
                                  <tbody v-show="loading">
                                      <tr>
                                          <td colspan="8" class="text-center">
                                              <p>Loading users...</p>
                                          </td>
                                      </tr>
                                  </tbody>
                                  <tfoot>
                                      <tr>
                                          <pagination-strip
                                              :current-page.sync="currentPage"
                                              :pages="pages"
                                              :max-per-page.sync="maxPerPage"
                                              :item-count="itemCount"
                                              @refresh="fetchUsers"
                                          ></pagination-strip>
                                      </tr>
                                  </tfoot>
                              </table>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>

      <!-- Edit User Modal -->
      <div class="modal fade" id="editUserModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Edit User</h5>
                      <button type="button" class="close" style="margin-top: -25px" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body">
                      <form class="form-horizontal">
                          <div class="form-group">
                              <div class="col-sm-3">
                                  <label class="control-label">First Name *</label>
                              </div>
                              <div class="col-sm-9">
                                  <input type="text" v-model="editFormData.firstName" class="form-control" required>
                              </div>
                          </div>

                          <div class="form-group">
                              <div class="col-sm-3">
                                  <label class="control-label">Last Name *</label>
                              </div>
                              <div class="col-sm-9">
                                  <input type="text" v-model="editFormData.lastName" class="form-control" required>
                              </div>
                          </div>

                          <div class="form-group">
                              <div class="col-sm-3">
                                  <label class="control-label">Username *</label>
                              </div>
                              <div class="col-sm-9">
                                  <input type="text" v-model="editFormData.userName" class="form-control" required>
                              </div>
                          </div>

                          <div class="form-group">
                              <div class="col-sm-3">
                                  <label class="control-label">Email *</label>
                              </div>
                              <div class="col-sm-9">
                                  <input type="email" v-model="editFormData.email" class="form-control" required>
                              </div>
                          </div>

                          <div class="form-group">
                              <div class="col-sm-3">
                                  <label class="control-label">New Password</label>
                              </div>
                              <div class="col-sm-9">
                                  <input type="password" v-model="editFormData.password" class="form-control" placeholder="Leave blank to keep current password">
                              </div>
                          </div>

                          <div class="form-group">
                              <div class="col-sm-3">
                                  <label class="control-label">Company</label>
                              </div>
                              <div class="col-sm-9">
                                  <select v-model="editFormData.companyId" class="form-control">
                                      <option value="0">No Company</option>
                                      <option v-for="company in companies" :key="'edit-company-' + company.id" :value="company.id">
                                          {{company.name}}
                                      </option>
                                  </select>
                              </div>
                          </div>

                          <div class="form-group">
                              <div class="col-sm-3">
                                  <label class="control-label">Role *</label>
                              </div>
                              <div class="col-sm-9">
                                  <select v-model="editFormData.role" class="form-control" required>
                                      <option value="">Select Role</option>
                                      <option value="admin">Admin</option>
                                      <option value="user">User</option>
                                      <option value="customer">Customer</option>
                                  </select>
                              </div>
                          </div>

                          <div class="form-group">
                              <div class="col-sm-3">
                                  <label class="control-label">Account Status</label>
                              </div>
                              <div class="col-sm-9">
                                  <label class="toggle-switch">
                                      <input type="checkbox" v-model="editFormData.accountStatus">
                                      <span class="slider" :class="{'inactive': !editFormData.accountStatus}"></span>
                                  </label>
                                  <span style="margin-left: 10px;">{{ editFormData.accountStatus ? 'Active' : 'Inactive' }}</span>
                              </div>
                          </div>
                      </form>
                  </div>
                  <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                      <button type="button" class="request-button" @click="updateUser">Save Changes</button>
                  </div>
              </div>
          </div>
      </div>

      <!-- Delete Confirmation Modal -->
      <div class="modal fade" id="deleteUserModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Confirm Delete</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <p>Are you sure you want to delete this user?</p>
                      <div class="mt-3">
                          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                          <button type="button" class="btn btn-danger" @click="deleteUser">Delete</button>
                      </div>
                  </div>
              </div>
          </div>
      </div>

      <!-- Success Modal -->
      <div class="modal fade" id="successModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Success</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                      <p>Operation completed successfully!</p>
                  </div>
              </div>
          </div>
      </div>

      <!-- Edit Success Modal -->
      <div class="modal fade" id="editSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Success</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                      <p>User updated successfully!</p>
                  </div>
              </div>
          </div>
      </div>

      <!-- Status Success Modal -->
      <div class="modal fade" id="statusSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Success</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                      <p>User status updated successfully!</p>
                  </div>
              </div>
          </div>
      </div>

      <!-- Error Modal -->
      <div class="modal fade" id="errorModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Error</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                      <p>An error occurred. Please try again.</p>
                  </div>
              </div>
          </div>
      </div>

  </div>
  `,
});

// Initialize the Vue application
new Vue({
  el: "#manageUsers",
  components: {
    "manage-users-component": ManageUsersComponent,
  },
  template: `
    <div class="wrapper">
      <manage-users-component></manage-users-component>
    </div>
  `,
});
