package com.accolm.licenseManager.Services;

import java.util.Optional;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;

import com.accolm.licenseManager.DAO.AuthorityDao;
import com.accolm.licenseManager.DAO.AuthorityDaoImpl;
import com.accolm.licenseManager.DAO.ContactDAO;
import com.accolm.licenseManager.DAO.ContactDAOImpl;
import com.accolm.licenseManager.DAO.UserDao;
import com.accolm.licenseManager.DAO.UserDaoImpl;
import com.accolm.licenseManager.Entities.Contact;
import com.accolm.licenseManager.Entities.User;

public class UserServiceImpl implements UserService {

	private UserDao userDaoImpl;

	AuthorityDao authorityDaoImpl;

	Logger logger = LogManager.getLogger(UserServiceImpl.class);

	public UserServiceImpl() {
		userDaoImpl = new UserDaoImpl();
		authorityDaoImpl = new AuthorityDaoImpl();
	}

	@Override
	public User createUser(User user) {

		user.setPassword(PasswordEncoderFactories.createDelegatingPasswordEncoder().encode(user.getPassword()));

		user = userDaoImpl.createUser(user);

		if (user != null) {

			authorityDaoImpl.insertUserAuthorities(user);
		}

		return user;
	}

}
