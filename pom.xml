<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>bam4mft</groupId>
	<artifactId>bam4mft</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>war</packaging>
	<!--
	<properties>
		<maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
	</properties>
	-->

	<build>
		<sourceDirectory>src</sourceDirectory>
		<resources>
			<resource>
				<directory>src</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
		</resources>
		<plugins>


		<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.10.1</version>
				<configuration>
					<source>17</source>
					<target>17</target>
					<compilerVersion>17</compilerVersion>
				</configuration>
			</plugin>
			
			<plugin>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.3.2</version>
				<configuration>
					<warSourceDirectory>WebContent</warSourceDirectory>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-install-plugin</artifactId>
				<version>3.1.0</version>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<groupId>javax.faces</groupId>
			<artifactId>javax.faces</artifactId>
			<version>2.3.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.faces-2.3.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>tomcat-catalina</groupId>
			<artifactId>tomcat-catalina</artifactId>
			<version>9.0.6</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/tomcat-catalina-9.0.6.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>tomcat-api</groupId>
			<artifactId>tomcat-api</artifactId>
			<version>9.0.68</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/tomcat-api-9.0.68.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>log4j-core</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.18.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/log4j-core-2.18.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>log4j-api</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.18.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/log4j-api-2.18.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>net.engio</groupId>
			<artifactId>mbassador</artifactId>
			<version>1.3.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/mbassador-1.3.2.jar</systemPath>
		</dependency>
		
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.4</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/commons-lang-2.4.jar</systemPath>
		</dependency>
		
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.8.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/commons-io-2.8.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>eclipselink</groupId>
			<artifactId>eclipselink</artifactId>
			<version>9.4.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/eclipselink.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.accolm.scheduler</groupId>
			<artifactId>com.accolm.scheduler</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/com.accolm.scheduler.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>json-minimal</groupId>
			<artifactId>json-minimal</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/json-minimal.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>ehcache</groupId>
			<artifactId>ehcache</artifactId>
			<version>3.9.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/ehcache-3.9.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jcifs</groupId>
			<artifactId>jcifs</artifactId>
			<version>1.3.17</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jcifs-1.3.17.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jasypt</groupId>
			<artifactId>jasypt</artifactId>
			<version>1.9.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jasypt-1.9.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jasypt31</groupId>
			<artifactId>jasypt31</artifactId>
			<version>1.9.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jasypt-spring31-1.9.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jasypt-spring</groupId>
			<artifactId>jasypt-spring</artifactId>
			<version>1.9.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jasypt-springsecurity3-1.9.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jstl</groupId>
			<artifactId>jstl</artifactId>
			<version>1.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jstl-1.2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>quartz</groupId>
			<artifactId>quartz</artifactId>
			<version>2.2.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/quartz-2.2.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>quartz-jobs</groupId>
			<artifactId>quartz-jobs</artifactId>
			<version>2.2.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/quartz-jobs-2.2.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>omnifaces</groupId>
			<artifactId>omnifaces</artifactId>
			<version>1.10</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/omnifaces-1.10.jar</systemPath>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>padlock</groupId>-->
<!--			<artifactId>padlock</artifactId>-->
<!--			<version>2.3</version>-->
<!--			<scope>system</scope>-->
<!--			<systemPath>Y:\Product-Development\Developers\Samuel\padlock-2.3.jar</systemPath>-->
<!--		</dependency>-->
		<dependency>
			<groupId>javax.annotation</groupId>
			<artifactId>javax.annotation-api</artifactId>
			<version>1.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.annotation-api-1.2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.glassfish.hk2.external</groupId>
			<artifactId>javax.inject</artifactId>
			<version>2.5.0-b05</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.inject-2.5.0-b05.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.glassfish</groupId>
			<artifactId>javax.json</artifactId>
			<version>1.1.4</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.json-1.1.4.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>javax.json.bind</groupId>
			<artifactId>javax.json.bind-api</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.json.bind-api-1.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.6.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.mail-1.6.2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.osgi.core</groupId>
			<artifactId>org.osgi.core</artifactId>
			<version>4.2.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/org.osgi.core-4.2.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.beans</groupId>
			<artifactId>org.springframework.beans</artifactId>
    		<version>5.3.24</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-beans-5.3.24.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>json</groupId>
			<artifactId>json</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/json-20171018.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-core</groupId>
			<artifactId>spring-security-core</artifactId>
			<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-core-5.8.13.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>tomahawk20</groupId>
			<artifactId>tomahawk20</artifactId>
			<version>1.1.11</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/tomahawk20-1.1.11.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>8.3.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/mysql-connector-j-8.3.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-lang3</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.3.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/commons-lang3-3.3.2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.1.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/commons-logging-1.1.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.2.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/commons-fileupload-1.2.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.3</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/commons-codec-1.3.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-web</groupId>
			<artifactId>spring-security-web</artifactId>
			<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-web-5.8.13.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.5.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/postgresql-42.5.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-ldap</groupId>
			<artifactId>spring-ldap</artifactId>
			<version>3.0.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-ldap-core-3.0.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.ldap</groupId>
			<artifactId>spring-ldap</artifactId>
			<version>1.3.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-ldap-1.3.1.RELEASE-all.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-saml2</groupId>
			<artifactId>spring-security-saml2</artifactId>
			<version>2.0.0.M31</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-saml2-core-2.0.0.M31.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-ldap</groupId>
			<artifactId>spring-security-ldap</artifactId>
			<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-ldap-5.8.13.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-ldap</groupId>
			<artifactId>spring-security-ldap</artifactId>
			<version>6.2.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-ldap-6.2.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.web</groupId>
			<artifactId>org.springframework.web</artifactId>
			<version>5.2.4</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-web-5.2.4.RELEASE.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.context</groupId>
			<artifactId>org.springframework.context</artifactId>
    		<version>5.3.24</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-context-5.3.24.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-indexer</artifactId>
			<version>5.3.24</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-context-indexer-5.3.24.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.context.support</groupId>
			<artifactId>org.springframework.context.support</artifactId>
			<version>5.3.24</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-context-support-5.3.24.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.aop</groupId>
			<artifactId>org.springframework.aop</artifactId>
    		<version>5.3.42</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-aop-5.3.24.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.binding</groupId>
			<artifactId>org.springframework.binding</artifactId>
			<version>2.5.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-binding-2.5.0.RELEASE.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.core</groupId>
			<artifactId>org.springframework.core</artifactId>
			<version>5.3.24</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-core-5.3.24.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.expression</groupId>
			<artifactId>org.springframework.expression</artifactId>
			<version>5.3.24</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-expression-5.3.24.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.faces</groupId>
			<artifactId>org.springframework.faces</artifactId>
			<version>2.5.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-faces-2.5.0.RELEASE.jar</systemPath>
		</dependency>
		<dependency>
    		<groupId>org.springframework.jdbc</groupId>
			<artifactId>org.springframework.jdbc</artifactId>
			<version>5.3.24</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-jdbc-5.3.24.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.js</groupId>
			<artifactId>org.springframework.js</artifactId>
			<version>2.4.7</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-js-2.4.7.RELEASE.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.webflow</groupId>
			<artifactId>org.springframework.webflow</artifactId>
			<version>2.5.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-webflow-2.5.0.RELEASE.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>4.0.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.servlet-api-4.0.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.70</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/bcprov-jdk15on-1.70.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.eclipse.persistence</groupId>
			<artifactId>javax.persistence</artifactId>
			<version>2.0.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.persistence-2.0.0.jar</systemPath>
		</dependency>
		<!--dependency>
			<groupId>jakarta.json</groupId>
			<artifactId>jakarta.json</artifactId>
			<version>1.1.6</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jakarta.json-1.1.6.jar</systemPath>
		</dependency-->
		<dependency>
			<groupId>jersey-client</groupId>
			<artifactId>jersey-client</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-client.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jersey-common</groupId>
			<artifactId>jersey-common</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-common.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.core</groupId>
			<artifactId>jersey-server</artifactId>
			<version>2.38</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-server-2.38.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jersey-container-servlet-core</groupId>
			<artifactId>jersey-container-servlet-core</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-container-servlet-core.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jersey-container-servlet</groupId>
			<artifactId>jersey-container-servlet</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-container-servlet.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.inject</groupId>
			<artifactId>jersey-hk2</artifactId>
			<version>2.38.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-hk2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.media</groupId>
			<artifactId>jersey-media-multipart</artifactId>
			<version>2.38</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-media-multipart-2.38.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jersey-media-jaxb</groupId>
			<artifactId>jersey-media-jaxb</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-media-jaxb.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.media</groupId>
			<artifactId>jersey-media-sse</artifactId>
			<version>2.38.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-media-sse.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.media</groupId>
			<artifactId>jersey-media-json-binding</artifactId>
			<version>2.38.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jersey-media-json-binding.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>hibernate-validator</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>4.0.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/hibernate-validator-4.0.2.GA.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>activation</groupId>
			<artifactId>activation</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/activation.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>aopalliance</groupId>
			<artifactId>aopalliance</artifactId>
			<version>3.0.3</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/aopalliance-repackaged-3.0.3.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>javax.cache</groupId>
			<artifactId>cache-api</artifactId>
			<version>1.1.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/cache-api-1.1.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.hierynomus</groupId>
			<artifactId>asn-one</artifactId>
			<version>0.6.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/asn-one-0.6.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>asm-debug</groupId>
			<artifactId>asm-debug</artifactId>
			<version>5.0.4</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/asm-debug-all-5.0.4.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>camel-core</groupId>
			<artifactId>camel-core</artifactId>
			<version>2.13.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/camel-core-2.13.2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>camel-quartz</groupId>
			<artifactId>camel-quartz</artifactId>
			<version>2.13.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/camel-quartz2-2.13.2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>cdi-api</groupId>
			<artifactId>cdi-api</artifactId>
			<version>1.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/cdi-api-1.2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>db2jcc4</groupId>
			<artifactId>db2jcc4</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/db2jcc4.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>facelets</groupId>
			<artifactId>facelets</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/facelets-taglib-0.2_jsf-2.0_spring-3.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.eclipse</groupId>
			<artifactId>yasson</artifactId>
			<version>1.0.11</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/yasson-1.0.11.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>hk2-api</groupId>
			<artifactId>hk2-api</artifactId>
			<version>2.6.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/hk2-api-2.6.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>hk2-locator</groupId>
			<artifactId>hk2-locator</artifactId>
			<version>2.6.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/hk2-locator-2.6.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>hk2-utils</groupId>
			<artifactId>hk2-utils</artifactId>
			<version>2.6.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/hk2-utils-2.6.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.sun.istack</groupId>
			<artifactId>istack-commons-runtime</artifactId>
			<version>4.1.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/istack-commons-runtime-4.1.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jaxb-api</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>2.3.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jaxb-api-2.3.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-runtime</artifactId>
			<version>2.3.2</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jaxb-runtime-2.3.2.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>jpa-api</groupId>
			<artifactId>jpa-api</artifactId>
			<version>2.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/jpa-api-2.0-cr-1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>osgi-resource-locator</groupId>
			<artifactId>osgi-resource-locator</artifactId>
			<version>1.0.3</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/osgi-resource-locator-1.0.3.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>slf4j</groupId>
			<artifactId>slf4j</artifactId>
			<version>1.7.9</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/slf4j-api-1.7.9.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>smbj</groupId>
			<artifactId>smbj</artifactId>
			<version>0.14.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/smbj-0.14.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>ch.swaechter</groupId>
			<artifactId>smbjwrapper</artifactId>
			<version>1.2.0</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/smbjwrapper-1.2.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>smtp</groupId>
			<artifactId>smtp</artifactId>
			<version>1.4.5</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/smtp.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-acl</groupId>
			<artifactId>spring-security-acl</artifactId>
			<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-acl-5.8.13.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-aspects</groupId>
			<artifactId>spring-security-aspects</artifactId>
			<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-aspects-5.8.13.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-aspects</groupId>
			<artifactId>spring-aspects</artifactId>
			<version>5.3.24</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-aspects-5.3.24.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-cas</groupId>
			<artifactId>spring-security-cas</artifactId>
			<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-cas-5.8.13.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-crypto</groupId>
			<artifactId>spring-security-crypto</artifactId>
			<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-crypto-5.8.13.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-config</groupId>
			<artifactId>spring-security-config</artifactId>
			<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-config-5.8.13.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-security-taglibs</groupId>
			<artifactId>spring-security-taglibs</artifactId>
			<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-taglibs-5.8.13.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>spring-tx</groupId>
			<artifactId>spring-tx</artifactId>
			<version>5.2.4</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-tx-5.2.4.RELEASE.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
			<version>12.6.1.jre11</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/mssql-jdbc-12.6.1.jre11.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>validation-api</groupId>
			<artifactId>validation-api</artifactId>
			<version>2.0.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/validation-api-2.0.1.Final.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>tomcat-juli</groupId>
			<artifactId>tomcat-juli</artifactId>
			<version>9.0.7</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/tomcat-juli-9.0.7.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>javax.ws.rs</groupId>
			<artifactId>javax.ws.rs-api</artifactId>
			<version>2.1.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.ws.rs-api-2.1.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>javax.websocket</groupId>
    		<artifactId>javax.websocket-api</artifactId>
    		<version>1.1</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javax.websocket-api-1.1.jar</systemPath>
		</dependency>	
		<dependency>
			<groupId>org.springframework.security</groupId>
    		<artifactId>spring-security-saml2-service-provider</artifactId>
    		<version>5.8.13</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/spring-security-saml2-service-provider-5.8.13.jar</systemPath>
		</dependency>	
		<dependency>
			<groupId>org.jvnet.mimepull</groupId>
    		<artifactId>mimepull</artifactId>
    		<version>1.9.15</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/mimepull-1.9.15.jar</systemPath>
		</dependency>	
		<dependency>
			<groupId>javassist</groupId>
    		<artifactId>javassist</artifactId>
    		<version>3.30.2-GA</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/javassist-3.30.2-GA.jar</systemPath>
		</dependency>	
		<dependency>
			<groupId>org.apache.tomcat</groupId>
    		<artifactId>tomcat-dbcp</artifactId>
    		<version>9.0.89</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/tomcat-dbcp-9.0.89.jar</systemPath>
		</dependency>	
		<!--dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.5.6</version>
			<scope>system</scope>
			<systemPath>D:/devops/licensingManager/v1/currentLibs/logback-classic-1.5.6.jar</systemPath>
		</dependency-->

	</dependencies>
</project>