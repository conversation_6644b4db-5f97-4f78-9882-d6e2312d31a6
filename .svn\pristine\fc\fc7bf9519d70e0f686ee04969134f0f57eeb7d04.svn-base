// Company autocomplete component
Vue.component("company-autocomplete", {
  props: ["companies", "value"],
  data() {
    return {
      searchText: "",
      filteredCompanies: [],
      showDropdown: false,
      selectedCompany: null,
      highlightedIndex: -1,
    };
  },
  mounted() {
    if (this.value) {
      const company = this.companies.find((c) => c.id === parseInt(this.value));
      if (company) {
        this.selectedCompany = company;
        this.searchText = company.name;
      }
    }
  },
  methods: {
    filterCompanies() {
      if (!this.searchText) {
        this.filteredCompanies = [];
        this.showDropdown = false;
        return;
      }

      const searchTerm = this.searchText.toLowerCase();
      this.filteredCompanies = this.companies.filter((company) =>
        company.name.toLowerCase().includes(searchTerm)
      );

      this.showDropdown = this.filteredCompanies.length > 0;
      this.highlightedIndex = -1;
    },
    selectCompany(company) {
      this.selectedCompany = company;
      this.searchText = company.name;
      this.$emit("input", company.id);
      this.showDropdown = false;
    },
    onFocus() {
      if (this.searchText) {
        this.filterCompanies();
      }
    },
    onBlur() {
      setTimeout(() => {
        this.showDropdown = false;

        if (this.searchText && !this.selectedCompany) {
          const matchingCompany = this.companies.find(
            (c) => c.name.toLowerCase() === this.searchText.toLowerCase()
          );

          if (matchingCompany) {
            this.selectCompany(matchingCompany);
          } else {
            this.searchText = this.selectedCompany
              ? this.selectedCompany.name
              : "";
          }
        }
      }, 200);
    },
    onKeyDown(e) {
      if (!this.showDropdown) return;

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          this.highlightedIndex = Math.min(
            this.highlightedIndex + 1,
            this.filteredCompanies.length - 1
          );
          this.scrollToHighlighted();
          break;
        case "ArrowUp":
          e.preventDefault();
          this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
          this.scrollToHighlighted();
          break;
        case "Enter":
          e.preventDefault();
          if (this.highlightedIndex >= 0) {
            this.selectCompany(this.filteredCompanies[this.highlightedIndex]);
          }
          break;
        case "Escape":
          e.preventDefault();
          this.showDropdown = false;
          break;
      }
    },
    scrollToHighlighted() {
      this.$nextTick(() => {
        const highlighted = this.$el.querySelector(".highlighted");
        if (highlighted) {
          highlighted.scrollIntoView({
            block: "nearest",
            behavior: "smooth",
          });
        }
      });
    },
    clearSelection() {
      this.searchText = "";
      this.selectedCompany = null;
      this.$emit("input", "");
    },
  },
  template: `
      <div class="company-autocomplete-container" style="position: relative; width: 100%;">
        <div class="input-group" style="width: 100%;">
          <input
            type="text"
            class="form-control"
            v-model="searchText"
            @input="filterCompanies"
            @focus="onFocus"
            @blur="onBlur"
            @keydown="onKeyDown"
            placeholder="Search for a company..."
            style="width: calc(100% - 34px); border-radius: 4px 0 0 4px;"
          >
          <div class="input-group-btn" style="width: 34px; position: absolute; right: 0; top: 0; height: 100%;">
            <button 
              v-if="searchText" 
              class="btn btn-default" 
              type="button" 
              @click="clearSelection"
              style="height: 100%; border-radius: 0 4px 4px 0;"
            >
              <i class="glyphicon glyphicon-remove"></i>
            </button>
            <button 
              v-else 
              class="btn btn-default" 
              type="button"
              style="height: 100%; border-radius: 0 4px 4px 0; cursor: default;"
              disabled
            >
              <i class="glyphicon glyphicon-search"></i>
            </button>
          </div>
        </div>
        <div 
          v-show="showDropdown" 
          class="dropdown-menu" 
          style="display: block; width: 100%; max-height: 300px; overflow-y: auto; position: absolute; z-index: 1000;"
        >
          <a 
            v-for="(company, index) in filteredCompanies" 
            :key="company.id"
            href="#" 
            class="dropdown-item" 
            :class="{ 'highlighted': index === highlightedIndex }"
            @mousedown.prevent="selectCompany(company)"
            @mouseover="highlightedIndex = index"
            style="display: block; padding: 8px 15px; text-decoration: none; color: #333;"
          >
            {{ company.name }}
          </a>
        </div>
      </div>
    `,
});

var CreateUserComponent = Vue.component("create-user-component", {
  components: {
    "company-autocomplete": Vue.component("company-autocomplete"),
  },
  data() {
    return {
      formData: {
        firstName: "",
        lastName: "",
        userName: "",
        email: "",
        role: "",
        password: "",
        confirmPassword: "",
        companyId: "",
      },
      formErrors: {
        userName: "",
        email: "",
        password: "",
        confirmPassword: "",
      },
      loading: false,
      companies: [],
    };
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },

    isFormValid() {
      const baseValidation =
        this.formData.firstName &&
        this.formData.lastName &&
        this.formData.userName &&
        this.formData.email &&
        this.formData.role &&
        this.formData.password &&
        this.formData.confirmPassword &&
        this.formData.password === this.formData.confirmPassword &&
        this.isValidEmail(this.formData.email) &&
        this.formData.password.length >= 6;

      // If role is 'user', also require company selection
      if (this.formData.role === "user") {
        return baseValidation && this.formData.companyId;
      }

      return baseValidation;
    },

    showCompanyField() {
      return this.formData.role === "user";
    },
  },
  mounted() {
    this.fetchCompanies();
  },
  methods: {
    async fetchCompanies() {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/companies`);
        this.companies = response.data || [];
      } catch (error) {
        console.error("Error fetching companies:", error);
      }
    },

    onRoleChange() {
      // Clear company selection when role changes
      if (this.formData.role !== "user") {
        this.formData.companyId = "";
      }
    },

    validateForm() {
      let isValid = true;
      this.formErrors = {
        userName: "",
        email: "",
        password: "",
        confirmPassword: "",
      };

      // Username validation
      if (this.formData.userName.length < 3) {
        this.formErrors.userName =
          "Username must be at least 3 characters long";
        isValid = false;
      }

      // Email validation
      if (!this.isValidEmail(this.formData.email)) {
        this.formErrors.email = "Please enter a valid email address";
        isValid = false;
      }

      // Password validation
      if (this.formData.password.length < 6) {
        this.formErrors.password =
          "Password must be at least 6 characters long";
        isValid = false;
      }

      // Confirm password validation
      if (this.formData.password !== this.formData.confirmPassword) {
        this.formErrors.confirmPassword = "Passwords do not match";
        isValid = false;
      }

      return isValid;
    },

    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    async createUser() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;

      try {
        const userData = {
          firstName: this.formData.firstName,
          lastName: this.formData.lastName,
          userName: this.formData.userName,
          email: this.formData.email,
          role: this.formData.role,
          password: this.formData.password,
        };

        // Add company ID only if role is 'user' and company is selected
        if (this.formData.role === "user" && this.formData.companyId) {
          userData.companyId = parseInt(this.formData.companyId);
        }

        console.log("Creating user:", userData);

        const response = await axios.post(
          `${this.apiBaseUrl}/users`,
          userData,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.status === 200 || response.status === 201) {
          console.log("User created successfully:", response.data);

          // Reset form
          this.resetForm();

          // Show success modal
          $("#createSuccessModal").modal("show");

          setTimeout(() => {
            $("#createSuccessModal").modal("hide");
          }, 2000);
        }
      } catch (error) {
        console.error("Error creating user:", error);

        // Show error modal
        $("#createErrorModal").modal("show");

        setTimeout(() => {
          $("#createErrorModal").modal("hide");
        }, 2000);
      } finally {
        this.loading = false;
      }
    },

    resetForm() {
      this.formData = {
        firstName: "",
        lastName: "",
        userName: "",
        email: "",
        role: "",
        password: "",
        confirmPassword: "",
        companyId: "",
      };
      this.formErrors = {
        userName: "",
        email: "",
        password: "",
        confirmPassword: "",
      };
    },
  },

  template: `
    <div class="main-section" style="font-family: 'Open Sans', sans-serif;">
        <div class="container">
            <div class="row">
                <div class="col-sm-12" style="text-align: center">
                    <h1>Create User</h1>
                </div>
                <div class="col-sm-12">
                    <div class="row">
                        <div class="col-sm-2"></div>
                        <div class="col-sm-8" style="margin-top: 29px;">
                            <form class="form-horizontal" @submit.prevent="createUser">
                                
                                <!-- First Name -->
                                <div class="form-group">
                                    <div class="col-sm-3">
                                        <label class="control-label">First Name</label>
                                    </div>
                                    <div class="col-sm-6">
                                        <input 
                                            type="text" 
                                            v-model="formData.firstName" 
                                            class="form-control" 
                                            required
                                            placeholder="Enter first name">
                                    </div>
                                </div>
  
                                <!-- Last Name -->
                                <div class="form-group">
                                    <div class="col-sm-3">
                                        <label class="control-label">Last Name</label>
                                    </div>
                                    <div class="col-sm-6">
                                        <input 
                                            type="text" 
                                            v-model="formData.lastName" 
                                            class="form-control" 
                                            required
                                            placeholder="Enter last name">
                                    </div>
                                </div>
  
                                <!-- Username -->
                                <div class="form-group" :class="{'has-error': formErrors.userName}">
                                    <div class="col-sm-3">
                                        <label class="control-label">Username</label>
                                    </div>
                                    <div class="col-sm-6">
                                        <input 
                                            type="text" 
                                            v-model="formData.userName" 
                                            class="form-control" 
                                            required
                                            placeholder="Enter username">
                                        <span class="help-block" v-if="formErrors.userName" style="color: #d9534f; font-size: 12px;">{{ formErrors.userName }}</span>
                                    </div>
                                </div>
  
                                <!-- Email -->
                                <div class="form-group" :class="{'has-error': formErrors.email}">
                                    <div class="col-sm-3">
                                        <label class="control-label">Email</label>
                                    </div>
                                    <div class="col-sm-6">
                                        <input 
                                            type="email" 
                                            v-model="formData.email" 
                                            class="form-control" 
                                            required
                                            placeholder="Enter email address">
                                        <span class="help-block" v-if="formErrors.email" style="color: #d9534f; font-size: 12px;">{{ formErrors.email }}</span>
                                    </div>
                                </div>
  
                                <!-- Role -->
                                <div class="form-group">
                                    <div class="col-sm-3">
                                        <label class="control-label">Role</label>
                                    </div>
                                    <div class="col-sm-6">
                                        <select v-model="formData.role" class="form-control" required @change="onRoleChange">
                                            <option value="">Select Role</option>
                                            <option value="admin">Admin</option>
                                            <option value="user">User</option>
                                        </select>
                                    </div>
                                </div>
  
                                <!-- Company Field (only shown when role is 'user') -->
                                <div class="form-group" v-show="showCompanyField">
                                    <div class="col-sm-3">
                                        <label class="control-label">Company</label>
                                    </div>
                                  <div class="col-sm-4">
                                      <company-autocomplete 
                                        :companies="companies" 
                                        v-model="formData.companyId"
                                      ></company-autocomplete>
                                  </div>
                              </div>

                              <!-- Password -->
                              <div class="form-group" :class="{'has-error': formErrors.password}">
                                  <div class="col-sm-3">
                                      <label class="control-label">Choose Password</label>
                                  </div>
                                  <div class="col-sm-6">
                                      <input 
                                          type="password" 
                                          v-model="formData.password" 
                                          class="form-control" 
                                          required
                                          placeholder="Enter password (min 6 characters)">
                                      <span class="help-block" v-if="formErrors.password" style="color: #d9534f; font-size: 12px;">{{ formErrors.password }}</span>
                                  </div>
                              </div>

                              <!-- Confirm Password -->
                              <div class="form-group" :class="{'has-error': formErrors.confirmPassword}">
                                  <div class="col-sm-3">
                                      <label class="control-label">Confirm Password</label>
                                  </div>
                                  <div class="col-sm-6">
                                      <input 
                                          type="password" 
                                          v-model="formData.confirmPassword" 
                                          class="form-control" 
                                          required
                                          placeholder="Confirm your password">
                                      <span class="help-block" v-if="formErrors.confirmPassword" style="color: #d9534f; font-size: 12px;">{{ formErrors.confirmPassword }}</span>
                                  </div>
                              </div>

                              <!-- Submit Button -->
                              <div class="form-group">
                                  <div class="col-sm-3"></div>
                                  <div class="col-sm-6">
                                      <button 
                                          type="submit" 
                                          class="request-button form-control" 
                                          :disabled="!isFormValid || loading">
                                          <span v-if="loading">
                                              <i class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></i> Creating User...
                                          </span>
                                          <span v-else>Create User</span>
                                      </button>
                                  </div>
                              </div>

                          </form>
                      </div>
                      <div class="col-sm-2"></div>
                  </div>
              </div>
          </div>
      </div>

      <!-- Success Modal -->
      <div class="modal fade" id="createSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Success</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                      <p>User created successfully!</p>
                  </div>
              </div>
          </div>
      </div>

      <!-- Error Modal -->
      <div class="modal fade" id="createErrorModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Error</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                      <p>Failed to create user. Please try again.</p>
                  </div>
              </div>
          </div>
      </div>

  </div>
  `,
});

// Initialize the Vue application
new Vue({
  el: "#createUser",
  components: {
    "create-user-component": CreateUserComponent,
  },
  template: `
    <div class="wrapper">
      <create-user-component></create-user-component>
    </div>
  `,
});
