var PaginationStrip = Vue.component("pagination-strip", {
  props: ["currentPage", "pages", "maxPerPage", "itemCount"],
  methods: {
    async onChange(event) {
      const numberToGet = parseInt(event.target.value);
      this.$emit("update:maxPerPage", numberToGet);
      this.$emit("refresh");
    },
    async onNext() {
      if (this.currentPage < this.pages) {
        this.$emit("update:currentPage", this.currentPage + 1);
        this.$emit("refresh");
      }
    },
    async onPrev() {
      if (this.currentPage > 1) {
        this.$emit("update:currentPage", this.currentPage - 1);
        this.$emit("refresh");
      }
    },
    async onFirst() {
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onLast() {
      this.$emit("update:currentPage", this.pages);
      this.$emit("refresh");
    },
  },
  template: `
    <th class="tableHeader" colspan="9" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
      <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
      <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
        <span>Page {{currentPage}} of {{pages}}</span>
        <ul class="pagination pagination-sm" style="margin: 0; border: none;">
          <li :class="{disabled: currentPage === 1}" style="border: none;">
            <span @click="onFirst()" style="border: none;">
              <i class="glyphicon glyphicon-step-backward"></i>
            </span>
          </li>
          <li :class="{disabled: currentPage === 1}" style="border: none;">
            <span @click="onPrev()" style="border: none;">
              <i class="glyphicon glyphicon-chevron-left"></i>
            </span>
          </li>
          <li :class="{disabled: currentPage === pages}" style="border: none;">
            <span @click="onNext()" style="border: none;">
              <i class="glyphicon glyphicon-chevron-right"></i>
            </span>
          </li>
          <li :class="{disabled: currentPage === pages}" style="border: none;">
            <span @click="onLast()" style="border: none;">
              <i class="glyphicon glyphicon-step-forward"></i>
            </span>
          </li>
        </ul>
        <span style="white-space: nowrap;">Results per page : </span>
        <select class="pages-form-control" @change="onChange($event)" style="margin-left: 5px;">
  <option value="10" :selected="maxPerPage === 10">10</option>
  <option value="25" :selected="maxPerPage === 25">25</option>
  <option value="50" :selected="maxPerPage === 50">50</option>
  <option value="100" :selected="maxPerPage === 100">100</option>
</select></div></th>

  `,
});

var displayCompanies = Vue.component("display-companies", {
  components: {
    "pagination-strip": PaginationStrip,
  },
  data() {
    return {
      companies: [],
      countries: [],
      hasDeleted: false,
      isFetching: false,
      currentPage: 1,
      companies: [],

      allCompanies: [], // Store all companies for filtering
      filters: {
        name: "",
        country: "",
        status: "",
      },
      pages: 1,
      viewCompany: {},
      itemCount: 0,
      maxPerPage: 10,
      searchQuery: "",
      maxVisibleButtons: 3,
      activeMainTab: localStorage.getItem("activeCompanyTab") || "list",
      activeTab: "contacts",
      did: null,
      editFormData: {
        id: null,
        name: "",
        city: "",
        state: "",
        postCode: "",
        tin: "",
        industryType: "",
        status: 0,
        countryId: 0,
        telephone: "",
        address: "",
        email: "",
      },
      formErrors: {
        telephone: "",
      },
      formData: {
        name: "",
        city: "",
        state: "",
        postCode: "",
        tin: "",
        industryType: "",
        status: 0,
        countryId: "",
        telephone: "",
        address: "",
      },
    };
  },
  methods: {
    async fetchContacts() {
      this.isFetching = true;
      try {
        const response = await axios.get(`${this.apiBaseUrl}/contacts`);
        if (response.status === 200) {
          this.allContacts = response.data;
          this.itemCount = response.data.length;
          this.pages = Math.ceil(this.itemCount / this.maxPerPage);

          // Apply filters if any are set
          if (
            this.filters.name ||
            this.filters.company ||
            this.filters.email ||
            this.filters.phone
          ) {
            this.applyFilters();
          } else {
            // Calculate pagination slice
            const startIndex = (this.currentPage - 1) * this.maxPerPage;
            const endIndex = startIndex + this.maxPerPage;
            // Set paginated results
            this.contacts = this.allContacts.slice(startIndex, endIndex);
          }
        }
      } catch (error) {
        console.error("Error fetching contacts:", error);
      } finally {
        this.isFetching = false;
      }
    },

    // Method to filter companies
    applyFilters() {
      if (!this.allCompanies.length) return;

      let filtered = [...this.allCompanies];

      // Filter by name
      if (this.filters.name) {
        const searchTerm = this.filters.name.toLowerCase();
        filtered = filtered.filter((company) =>
          company.name.toLowerCase().includes(searchTerm)
        );
      }

      // Filter by country
      if (this.filters.country) {
        filtered = filtered.filter(
          (company) => company.countryId === parseInt(this.filters.country)
        );
      }

      // Filter by status
      if (this.filters.status !== "") {
        filtered = filtered.filter(
          (company) => company.status === parseInt(this.filters.status)
        );
      }

      // Update pagination
      this.itemCount = filtered.length;
      this.pages = Math.ceil(this.itemCount / this.maxPerPage);

      // Apply pagination to filtered results
      const startIndex = (this.currentPage - 1) * this.maxPerPage;
      const endIndex = startIndex + this.maxPerPage;
      this.companies = filtered.slice(startIndex, endIndex);
    },

    validateForm() {
      let isValid = true;
      this.formErrors = { telephone: "" };

      // Phone validation if provided
      const phoneRegex =
        /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
      if (
        this.formData.telephone.trim() &&
        !phoneRegex.test(this.formData.telephone)
      ) {
        this.formErrors.telephone = "Please enter a valid phone number";
        isValid = false;
      }

      // Check required fields
      if (
        !this.formData.name ||
        !this.formData.city ||
        !this.formData.state ||
        !this.formData.postCode ||
        !this.formData.countryId ||
        !this.formData.address
      ) {
        isValid = false;
      }

      return isValid;
    },

    getStatusStyle(status) {
      return (
        {
          1: "background-color: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;",
          2: "background-color: #ffa500; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;",
          3: "background-color: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;",
          0: "background-color: #6c757d; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;",
        }[parseInt(status)] || ""
      );
    },
    getStatusString(status) {
      return (
        {
          0: "Inactive",
          1: "Active",
          2: "Pending",
          3: "Suspended",
        }[status] || "Unknown"
      );
    },

    handleSearch() {
      if (this.searchQuery.length > 2) {
        axios
          .get(`${this.apiBaseUrl}/companies/search/${this.searchQuery}`)
          .then((response) => {
            this.companies = response.data;
            this.itemCount = response.data.length;
            this.pages = Math.ceil(this.itemCount / this.maxPerPage);
          })
          .catch((error) => console.log("Search error:", error));
      } else if (this.searchQuery === "") {
        this.displayData();
      }
    },
    submitCompany() {
      if (!this.validateForm()) {
        return; // Stop submission if validation fails
      }

      const formData = {
        name: this.formData.name,
        city: this.formData.city,
        state: this.formData.state,
        postCode: this.formData.postCode,
        country: this.formData.country,
        email: this.formData.email,
        tin: this.formData.tin,
        industryType: this.formData.industryType,
        status: this.formData.status,
        countryId: this.formData.countryId,
        telephone: this.formData.telephone,
        address: this.formData.address,
      };

      axios
        .post(`${this.apiBaseUrl}/companies`, formData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          if (response.status === 201) {
            // First switch to list view
            this.activeMainTab = "list";

            // Then fetch fresh data
            this.displayData().then(() => {
              $("#onSuccessModal").modal("show");
              this.resetForm();

              setTimeout(() => {
                $("#onSuccessModal").modal("hide");
              }, 2000);
            });
          }
        })
        .catch(() => {
          $("#errorModal").modal("show");
          setTimeout(() => {
            $("#errorModal").modal("hide");
          }, 2000);
        });
    },
    getCountryName(countryId) {
      const country = this.countries.find((c) => c.id === countryId);
      return country ? country.name : "";
    },
    async fetchCountries() {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/countries`);
        if (response.status === 200) {
          this.countries = response.data;
        }
      } catch (error) {
        console.log("Error fetching countries:", error);
      }
    },

    updateCompany() {
      // Validate form data
      if (
        !this.editFormData.name ||
        !this.editFormData.city ||
        !this.editFormData.state ||
        !this.editFormData.postCode ||
        !this.editFormData.countryId
      ) {
        alert("Please fill in all required fields");
        return;
      }

      // Send update request to the server
      axios
        .put(`${this.apiBaseUrl}/companies`, this.editFormData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          if (response.status === 200) {
            $("#editCompanyModal").modal("hide");
            $("#editCompanySuccess").modal("show");

            // Refresh the data
            this.displayData();
            setTimeout(() => {
              $("#editCompanySuccess").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error updating company:", error);
          $("#editCompanyModal").modal("hide");
          $("#errorModal").modal("show");

          setTimeout(() => {
            $("#errorModal").modal("hide");
          }, 2000);
        });
    },
    async displayData() {
      this.isFetching = true;
      try {
        const [res, resCount] = await Promise.all([
          axios.get(`${this.apiBaseUrl}/companies`),
          axios.get(`${this.apiBaseUrl}/companies/count`),
        ]);

        if (res.status === 200 && resCount.status === 200) {
          this.allCompanies = res.data; // Store all companies
          this.itemCount = resCount.data;
          this.pages = Math.ceil(this.itemCount / this.maxPerPage);
          if (
            this.filters.name ||
            this.filters.country ||
            this.filters.status !== ""
          ) {
            this.applyFilters();
          } else {
            // Calculate pagination slice
            const startIndex = (this.currentPage - 1) * this.maxPerPage;
            const endIndex = startIndex + this.maxPerPage;
            // Set paginated results
            this.companies = this.allCompanies.slice(startIndex, endIndex);
          }
        }
      } catch (error) {
        console.error("Error fetching companies:", error);
      } finally {
        this.isFetching = false;
      }
    },

    // for companies filter reset
    resetFilters() {
      this.filters = {
        name: "",
        country: "",
        status: "",
      };
      this.displayData();
    },
    resetForm() {
      this.formData = {
        name: "",
        city: "",
        state: "",
        postCode: "",
        tin: "",
        industryType: "",
        status: 0,
        countryId: 0,
        telephone: "",
        address: "",
      };
    },
    editCompany(id) {
      const company = this.companies.find((c) => c.id === id);
      if (company) {
        this.editFormData = { ...company };
        $("#editCompanyModal").modal("show");
      }
    },

    displaySingleCompany(id) {
      // Find the company in the array
      const company = this.companies.find((c) => c.id === id);
      if (company) {
        // Set the view company data
        this.viewCompany = { ...company };
        // Show the view modal
        $("#viewCompanyModal").modal("show");
      } else {
        // If company not found in local array, fetch it from the server
        axios
          .get(`${this.apiBaseUrl}/companies/${id}`)
          .then((response) => {
            if (response.status === 200) {
              this.viewCompany = response.data;
              $("#viewCompanyModal").modal("show");
            }
          })
          .catch((error) => {
            console.error("Error fetching company details:", error);
            $("#errorModal").modal("show");

            setTimeout(() => {
              $("#errorModal").modal("hide");
            }, 2000);
          });
      }
    },
    deleteCompany(id) {
      axios
        .delete(`${this.apiBaseUrl}/companies/delete/${id}`)
        .then((response) => {
          if (response.status === 200) {
            // Remove from local array
            this.companies = this.companies.filter(
              (company) => company.id !== id
            );
            $("#deleteCompanyModal").modal("hide");
            $("#deleteCompanySuccess").modal("show");

            // Refresh data from server
            this.displayData();

            // Auto-hide success message after 2 seconds
            setTimeout(() => {
              $("#deleteCompanySuccess").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error deleting company:", error);
          $("#deleteCompanyModal").modal("hide");
          $("#errorModal").modal("show");

          setTimeout(() => {
            $("#errorModal").modal("hide");
          }, 2000);
        });
    },
    deleteModal(id) {
      this.did = id;
    },
    searchCompanies(query) {
      if (query === "") {
        this.displayData();
      }
    },
    setActiveTab(tab) {
      this.activeTab = tab;
    },
    saveLicense() {
      // Handle form submission
      console.log("Form data:", this.formData);
    },
  },
  watch: {
    activeMainTab(newTab) {
      localStorage.setItem("activeCompanyTab", newTab);
    },
    "filters.name": function () {
      this.applyFilters();
    },
    "filters.country": function () {
      this.applyFilters();
    },
    "filters.status": function () {
      this.applyFilters();
    },
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },
  },
  mounted() {
    this.displayData();
    this.fetchCountries();
  },
  template: `
   <div class="home-request" style="margin-top: 10px; font-family: 'Open Sans', sans-serif;">
      <ul class="nav nav-tabs">
      
        <li :class="{ active: activeMainTab === 'list' }">
          <a @click="activeMainTab = 'list'" href="#listCompanies">List Companies</a>
        </li>
        <li :class="{ active: activeMainTab === 'add' }">
          <a @click="activeMainTab = 'add'" href="#addCompany">Add Company</a>
        </li>
      </ul>

      <div class="tab-content">
        <div id="listCompanies" :class="['tab-pane', { active: activeMainTab === 'list' }]">
          <div class="row mb-3">
          </div>
          <br>
          <div class="row mb-3">
  <div class="col-md-4">
    <div class="input-group">
      <input 
        type="text" 
        class="form-control" 
        v-model="searchQuery" 
        placeholder="Search companies..."
        @input="handleSearch"
      >
      <span class="input-group-btn">
        <button class="btn btn-default" type="button">
          <i class="glyphicon glyphicon-search"></i>
        </button>
      </span>
    </div>
  </div>
</div>

<div class="row mb-3">
  <div class="col-md-12">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h4 class="panel-title">
          <a data-toggle="collapse" href="#filterCollapse">
            <i class="glyphicon glyphicon-filter"></i> Filters
          </a>
        </h4>
      </div>
      <div id="filterCollapse" class="panel-collapse collapse">
        <div class="panel-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label>Company Name</label>
                <input type="text" class="form-control" v-model="filters.name" placeholder="Filter by name">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>Country</label>
                <select class="form-control" v-model="filters.country">
                  <option value="">All Countries</option>
                  <option v-for="country in countries" :key="country.id" :value="country.id">
                    {{country.name}}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>Status</label>
                <select class="form-control" v-model="filters.status">
                  <option value="">All Statuses</option>
                  <option value="0">Inactive</option>
                  <option value="1">Active</option>
                  <option value="2">Pending</option>
                  <option value="3">Suspended</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 text-right">
              <button class="btn btn-default" @click="resetFilters">
                <i class="glyphicon glyphicon-refresh"></i> Reset Filters
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

  

          <table id="table" class="table table-hover table-striped tableevent">
            <thead>
              <tr>
                <pagination-strip
                  :current-page.sync="currentPage"
                  :pages="pages"
                  :max-per-page.sync="maxPerPage"
                  :item-count="itemCount"
                  @refresh="displayData"
                ></pagination-strip>
              </tr>
              <tr>
                <th class="tableHeader">Company Name</th>
                <th class="tableHeader">Country</th>
                <th class="tableHeader">Email</th>
                <th class="tableHeader">Phone</th>
                <th class="tableHeader">Status</th>
                <th class="tableHeader">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="company in companies" :key="company.id">
                <td>
                  <a :href="'companiesData.html?id=' + company.id">{{ company.name }}</a>
                </td>
                <td>{{getCountryName(company.countryId)}}</td>
               
                <td>{{company.email}}</td>
                <td>{{company.telephone}}</td>
                <td><span :style="getStatusStyle(company.status)">{{getStatusString(company.status)}}</span></td>

<td style="width: 164px; text-align: right;">
  <!-- View Button -->
  <button class="btn btn-light" @click="displaySingleCompany(company.id)" title="View">
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
    <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z"/>
    <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>
  </svg>
</button>

  <!-- Edit Button -->
  <button class="btn btn-light" @click="editCompany(company.id)" title="Edit">
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
    <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
  </svg>
</button>

  <!-- Delete Button -->
  <button class="btn btn-light" @click="deleteModal(company.id)" data-toggle="modal" data-target="#deleteCompanyModal" title="Delete">
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
    <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
    <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
  </svg>
</button>
</td>



              </tr>
            </tbody>
            <tfoot>
              <tr>
                <pagination-strip
                  :current-page.sync="currentPage"
                  :pages="pages"
                  :max-per-page.sync="maxPerPage"
                  :item-count="itemCount"
                  @refresh="displayData"
                ></pagination-strip>
              </tr>
            </tfoot>
          </table>
        </div>

        
          <!--- Add Company Tab -->
        <div id="addCompany" :class="['tab-pane', { active: activeMainTab === 'add' }]">
          <div style="margin-top: 20px;">
            <div class="row">
              <div class="col-sm-2"></div>
              <div class="col-sm-8">
                <form class="form-horizontal" @submit.prevent="submitCompany">
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Name *</label>
                    </div>
                    <div class="col-sm-6">
                      <input type="text" v-model="formData.name" class="form-control" required>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Address *</label>
                    </div>
                    <div class="col-sm-6">
                      <textarea v-model="formData.address" class="form-control" rows="3" required></textarea>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Email</label>
                    </div>
                    <div class="col-sm-6">
                      <input type="text" v-model="formData.email" class="form-control" required>
                    </div>
                  </div>

                    <div class="form-group" :class="{'has-error': formErrors.telephone}">
  <div class="col-sm-3">
    <label class="control-label">Phone</label>
  </div>
  <div class="col-sm-6">
    <input type="text" v-model="formData.telephone" class="form-control" required>
    <span class="help-block" v-if="formErrors.telephone">{{ formErrors.telephone }}</span>
  </div>
</div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">City *</label>
                    </div>
                    <div class="col-sm-6">
                      <input type="text" v-model="formData.city" class="form-control" required>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">State *</label>
                    </div>
                    <div class="col-sm-6">
                      <input type="text" v-model="formData.state" class="form-control" required>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Post Code *</label>
                    </div>
                    <div class="col-sm-6">
                      <input type="text" v-model="formData.postCode" class="form-control" required>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Country *</label>
                    </div>
                    <div class="col-sm-6">
                 <select v-model.number="formData.countryId" class="form-control" required>
  <option value="0">Select Country</option>
  <option v-for="country in countries" :key="country.id" :value="country.id">
    {{country.name}}
  </option>
</select>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3"></div>
                    <div class="col-sm-6">
                     <button type="submit" class="request-button form-control">Add</button>

                    </div>
                  </div>
                </form>
              </div>
              <div class="col-sm-2"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Success Modal -->
      <div class="modal fade" id="onSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Success</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                      <p>Company added successfully!</p>
                  </div>
              </div>
          </div>
      </div>

      <!-- View Company Modal -->
<div class="modal fade" id="viewCompanyModal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Company Details</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-12">
            <table class="table table-bordered">
              <tbody>
                <tr>
                  <th style="width: 30%">Company Name</th>
                  <td>{{ viewCompany.name }}</td>
                </tr>
                <tr>
                  <th>Address</th>
                  <td>{{ viewCompany.address }}</td>
                </tr>
                <tr>
                  <th>Email</th>
                  <td>{{ viewCompany.email || 'N/A' }}</td>
                </tr>
                <tr>
                  <th>Phone</th>
                  <td>{{ viewCompany.telephone || 'N/A' }}</td>
                </tr>
                <tr>
                  <th>City</th>
                  <td>{{ viewCompany.city }}</td>
                </tr>
                <tr>
                  <th>State</th>
                  <td>{{ viewCompany.state }}</td>
                </tr>
                <tr>
                  <th>Post Code</th>
                  <td>{{ viewCompany.postCode }}</td>
                </tr>
                <tr>
                  <th>Country</th>
                  <td>{{ getCountryName(viewCompany.countryId) }}</td>
                </tr>
                <tr>
                  <th>TIN</th>
                  <td>{{ viewCompany.tin || 'N/A' }}</td>
                </tr>
                <tr>
                  <th>Industry Type</th>
                  <td>{{ viewCompany.industryType || 'N/A' }}</td>
                </tr>
                <tr>
                  <th>Status</th>
                  <td><span :style="getStatusStyle(viewCompany.status)">{{ getStatusString(viewCompany.status) }}</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="request-button" @click="editCompany(viewCompany.id)" data-dismiss="modal">Edit Company</button>
      </div>
    </div>
  </div>
</div>

      <!-- Edit Company Modal -->
<div class="modal fade" id="editCompanyModal" tabindex="-1" role="dialog">
  <div class="modal-dialog ">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit Company</h5>
        <button type="button" class="close" style="
    margin-top: -25px"  data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form class="form-horizontal">
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Name *</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="editFormData.name" class="form-control" required>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Address *</label>
            </div>
            <div class="col-sm-9">
              <textarea v-model="editFormData.address" class="form-control" rows="3" required></textarea>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Email</label>
            </div>
            <div class="col-sm-9">
              <input type="email" v-model="editFormData.email" class="form-control">
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Phone</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="editFormData.telephone" class="form-control">
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">City *</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="editFormData.city" class="form-control" required>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">State *</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="editFormData.state" class="form-control" required>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Post Code *</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="editFormData.postCode" class="form-control" required>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Country *</label>
            </div>
            <div class="col-sm-9">
              <select v-model.number="editFormData.countryId" class="form-control" required>
                <option value="0">Select Country</option>
                <option v-for="country in countries" :key="country.id" :value="country.id">
                  {{country.name}}
                </option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Status *</label>
            </div>
            <div class="col-sm-9">
              <select v-model.number="editFormData.status" class="form-control" required>
                <option value="0">Inactive</option>
                <option value="1">Active</option>
                <option value="2">Pending</option>
                <option value="3">Suspended</option>
              </select>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
  <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
  <button type="button" class="request-button" @click="updateCompany">Save Changes</button>
</div>
    </div>
  </div>
</div>

<!-- Edit Success Modal -->
<div class="modal fade" id="editCompanySuccess" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>Company updated successfully!</p>
      </div>
    </div>
  </div>
</div>

      <!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteCompanyModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Confirm Delete</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
        <p>Are you sure you want to delete this company?</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" @click="deleteCompany(did)">Delete</button>
      </div>
    </div>
  </div>
</div>

<!-- Delete Success Modal -->
<div class="modal fade" id="deleteCompanySuccess" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>Company deleted successfully!</p>
      </div>
    </div>
  </div>
</div>

      <!-- Error Modal -->
      <div class="modal fade" id="errorModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title">Error</h5>
                      <button type="button" class="close" data-dismiss="modal">
                          <span>&times;</span>
                      </button>
                  </div>
                  <div class="modal-body text-center">
                      <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                      <p>An error occurred. Please try again.</p>
                  </div>
              </div>
          </div>
      </div>
   </div>
`,
});

new Vue({
  el: "#companies",
  components: {
    "display-companies": displayCompanies,
  },
});
