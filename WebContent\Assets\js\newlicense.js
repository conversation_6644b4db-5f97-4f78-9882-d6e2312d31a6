var newLicenseForm = Vue.component("new-license-form", {
  data() {
    const today = new Date();
    const dd = String(today.getDate()).padStart(2, "0");
    const mm = String(today.getMonth() + 1).padStart(2, "0");
    const yyyy = today.getFullYear();
    const formattedDate = yyyy + "-" + mm + "-" + dd;

    return {
      keyid: "",
      newLicense: {
        name: "",
        status: "Valid",
        creationDate: formattedDate,
        startDate: "",
        expiryDate: "",
        floatExp: "",
        hardwareLock: "",
        signKey: [{}],
        properties: [{}],
      },
      rowCount: 1,
      disableBtn: false,
      propName: "",
      propValue: "",
      pickKey: [
        {
          id: 0,
          name: "",
          private_key: "",
          public_key: "",
          type: "",
        },
      ],
    };
  },
  methods: {
    async submitLicense() {
      var myJSON = JSON.stringify(this.newLicense);
      try {
        const response = await axios.post(
          `${this.apiBaseUrl}/licenses/create`,
          myJSON,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        // Show success modal only when API returns 200 OK
        if (response.status === 200 && response.data) {
          $("#successModal").modal("show");
          $("#successModal").on("hidden.bs.modal", () => {
            window.location.reload();
          });
        }
      } catch (error) {
        // Show error modal for any API failures
        $("#errorModal").modal("show");
        console.log("Error creating license:", error);
      }
    },
    async getSigningKey() {
      const res = await axios
        .get(`${this.apiBaseUrl}/keys/all`)
        .then((response) => {
          console.log(response.data);
          this.pickKey = response.data;
        });
    },
    removeProp(row) {
      if (this.rowCount == 1) {
        console.log(this.rowCount);
        this.disableBtn = true;
      } else if (this.rowCount > 1) {
        console.log(this.rowCount);
        this.newLicense.properties.splice(row, 1);
        this.rowCount--;
      }
    },
    addProp() {
      if (this.rowCount >= 1) {
        console.log(this.newLicense.properties);
        this.newLicense.properties.push({
          name: "",
          value: "",
        });
        this.disableBtn = false;
        this.rowCount++;
      } else if (this.rowCount == 0) {
        this.newLicense.properties.push({
          name: "",
          value: "",
        });
        this.rowCount++;
      }
    },
    pushKey() {
      var keyIdx = this.pickKey.findIndex((x) => x.name === this.keyid);
      this.newLicense.signKey = this.pickKey[keyIdx];
      // console.log(this.newLicense.signKey);
      // console.log(Object.keys(this.newLicense.signKey).length);
      // if (this.newLicense.signKey.length == 0) {
      //     // console.log(res.data);
      //     // this.newLicense.signKey.push(res.data);
      // } else if (this.newLicense.signKey.length == 1) {
      //     // console.log(res.data);
      //     // this.newLicense.signKey.splice(0, this.newLicense.signKey.length);
      //     // this.newLicense.signKey.push(res.data);
      // }
    },
    reloadPage() {
      window.location.reload(true);
    },
  },
  computed: {
    choosenKey(index) {
      this.keyid = index;
      return this.pushKey(this.keyid);
    },

    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1]; // Gets the first segment after domain
      return `${baseUrl}/${projectName}/api`;
    },
  },
  mounted() {
    this.getSigningKey();
  },
  template: `
<div>
  <div class="row">
    <div class="col-lg-7">
      <div class="card shadow mb-4">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">New License</h6>
        </div>
        <div class="card-body">
          <form class="form-horizontal" v-on:submit.prevent="submitLicense()">
            <div class="form-group row">
              <label class="col-sm-4 text-left control-label col-form-label">License Name</label>
              <div class="col-sm-6">
                <input autocomplete="off" type="text" class="form-control" v-model="newLicense.name" required>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-sm-4 text-left control-label col-form-label">Start Date</label>
              <div class="col-sm-6">
                <input type="date" class="form-control" v-model="newLicense.startDate" required>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-sm-4 text-left control-label col-form-label">Expiration Date</label>
              <div class="col-sm-6">
                <input type="date" class="form-control" v-model="newLicense.expiryDate" required>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-sm-4 text-left control-label col-form-label">Floating Days</label>
              <div class="col-sm-6 mt-2">
                <input type="number" class="form-control" v-model="newLicense.floatExp" required>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-sm-4 text-left control-label col-form-label">Hardware Locking</label>
              <div class="col-sm-6 mt-2">
                <select class="form-control" v-model="newLicense.hardwareLock" required>
                  <option value="false">False</option>
                  <option value="true">True</option>
                </select>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-sm-4 text-left control-label col-form-label">Signing Key</label>
              <div class="col-sm-6">
                <select class="form-control" id="autoSizingSelect" v-model="keyid" @change="pushKey()">
                  <option>Choose a signing key...</option>
                  <option v-for="key in pickKey">{{key.name}}</option>
                </select>
              </div>
            </div>
            <div class="border-top">
              <div class="card-body">
                <button type="submit" class="btn btn-primary pull-right" data-toggle="modal" data-target="#successModal">
  <i class="fas fa-save"></i> Save License & Properties
</button>

              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div class="col-lg-5">
      <div class="card shadow mb-4">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">License Properties</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-sm-3"></div>
            <table class="table table-hover table-bordered border-dark col-sm-12 text-center" style="width: 100% !important;">
              <thead>
                <tr class="table-secondary">
                  <th scope="col">Property</th>
                  <th scope="col">Value</th>
                  <th scope="col"></th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(property, index) in newLicense.properties">
                  <td>
                    <input v-model="newLicense.properties[index].name" type="text" class="form-control" placeholder="<add a property>">
                  </td>
                  <td>
                    <input v-model="newLicense.properties[index].value" type="text" class="form-control" placeholder="<add a value>">
                  </td>
                  <td>
                    <div class="btn-group">
                      <button :disabled='disableBtn' type="button" class="btn btn-danger btn-sm" @click="removeProp(index)" title="Remove Property">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                      <button type="button" class="btn btn-primary btn-sm" @click='addProp()' title="Add Property">
                        <i class="fa fa-plus-square" aria-hidden="true"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

          <!-- Success Modal -->
            <div class="modal fade" id="successModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="onSuccessModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-body text-center">
                            <i class="fa fa-check bg-success align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
                            <p class="lead text-success mb-5">
                                <strong>License Successfully Updated...</strong>
                            </p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal" @click="reloadPage()">Close</button>
                        </div>
                    </div>
                </div>
            </div>


  <!-- Error Modal -->
  <div class="modal fade" id="errorModal" tabindex="-1" role="dialog">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title text-danger">Error</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <p>Failed to create license. Please try again.</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>
`,
});

new Vue({
  el: "#new-license",
  components: {
    "new-license-form": newLicenseForm,
  },
  template: `
    <div>
    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="index.html">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-lock"></i>
                </div>
                <div class="sidebar-brand-text mx-3">License Manager <sup></sup></div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item ">
                <a class="nav-link" href="index.html">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>
            </li>
            <!-- Nav Item - Pages Collapse Menu -->
            <li class="nav-item">
                <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseTwo"
                    aria-expanded="true" aria-controls="collapseTwo">
                    <i class="fas fa-fw fa-folder-open"></i>
                    <span>License</span>
                </a>
                <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <a class="collapse-item " href="licenses.html">List License</a>
                        <a class="collapse-item " href="blanklicense.html">New Blank License</a>
                        <a class="collapse-item" href="templatelicense.html">License From Template</a>
                         <a class="collapse-item" href="importlicense.html"
                >Import License</a
              >
                    </div>
                </div>
            </li>

         <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseThree"
          >
            <i class="fas fa-fw fa-lock"></i>
            <span>Key Manager</span>
          </a>
          <div
            id="collapseThree"
            class="collapse"
            aria-labelledby="headingThree"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
            <a class="collapse-item" href="listkeys.html"
                >List Signing Keys</a
              >
              <a class="collapse-item " href="keymanager.html"
                >New Signing Key</a
              >
              
              <a class="collapse-item" href="importkey.html">Import Key</a>
            </div>
          </div>
        </li>

            
        <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseOne"
          >
            <i class="fas fa-fw fa-folder"></i>
            <span>Template Manager</span>
          </a>
          <div
            id="collapseOne"
            class="collapse"
            aria-labelledby="headingTwo"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
              <a class="collapse-item" href="templates.html">List Template</a>
              <a class="collapse-item" href="templatemanager.html"
                >New Template</a
              >
              <a class="collapse-item" href="importtemplate.html"
                >Import Template</a
              >
            </div>
          </div>
        </li>
        
         <!-- companies section -->
        <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseFour"
          >
            <i class="fas fa-fw fa-building"></i>
            <span>Companies</span>
          </a>
          <div
            id="collapseFour"
            class="collapse"
            aria-labelledby="headingFour"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
              <a class="collapse-item" href="companies.html">List Companies</a>
              <a class="collapse-item" href="company-new.html">New Company</a>
            </div>
          </div>
        </li>
            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>
        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">License From Blank</h1>
                    </div>
                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">


                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">User</span>
                                <img class="img-profile rounded-circle"
                                    src="Assets/img/undraw_profile.svg">
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profile
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Activity Log
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <new-license-form></new-license-form>



                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; Accolm License Manager 2021</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="#">Logout</a>
                </div>
            </div>
        </div>
    </div>
    </div>
    `,
});
