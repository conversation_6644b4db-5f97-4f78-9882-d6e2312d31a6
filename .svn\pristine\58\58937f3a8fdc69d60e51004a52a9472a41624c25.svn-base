package com.accolm.licenseManager.Services;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.accolm.licenseManager.DAO.LicenseDAOImpl;
import com.accolm.licenseManager.Entities.License;
import com.accolm.licenseManager.Entities.LicenseProperty;
import com.accolm.licenseManager.Entities.Template;
import com.accolm.licenseManager.Utils.LicenseKeyGenerator;

public class LicenseServiceImpl implements LicenseSI {

	private LicenseDAOImpl impl = new LicenseDAOImpl();
	Logger logger = LogManager.getLogger(LicenseServiceImpl.class);

	@Override
	public List<License> getAllLicenses() {
		return impl.getAllLicenses();
	}

	@Override
    public String createLicense(License license) {
        if (license == null || license.getName() == null || license.getName().trim().isEmpty()) {
            return "Invalid license data provided.";
        }
        
                
       //  What you need to do is send an empty expiration date to the back-end when the expiration is Never.
        String expirationDate = license.getExpiryDate();
        switch (expirationDate) {
		case "": 
			//expirationDate = null; // store "" as null for Never occurences.
			logger.info("creating Never licenses with an automatic failover to database default null paramater");
		   // license.setExpiryDate(expirationDate); // Never
        break;	
		default : // Wrapper to existing implementation.
		// Calculate expiration date if needed
		String calculatedExpiryDate = calculateLicenseExpiry(license);
		if(calculatedExpiryDate!=null)
		{
			license.setExpiryDate(calculatedExpiryDate);
		}
		break;
		
		}
   
// existing dates operands for licenses creation code
//	// Calculate expiration date if needed
//	String calculatedExpiryDate = calculateLicenseExpiry(license);if(calculatedExpiryDate!=null)
//	{
//		license.setExpiryDate(calculatedExpiryDate);
//	}

	return impl.create(license);
	}

	@Override
	public License getLicense(int id) {
		if (id <= 0) {
			License err = new License();
			err.setId(id);
			err.setName("Error in request, the ID does not exist.");
			return err;
		}
		return impl.getById(id);
	}

	@Override
	public String deleteLicense(int id) {
		if (id <= 0) {
			return "ID passed does not exist.";
		}
		return impl.remove(id);
	}

	@Override
	public long getLicenseCount() {
		return impl.countLicenses();
	}

	@Override
	public String updateLicense(License license) {
		if (license == null || license.getName() == null || license.getName().trim().isEmpty()) {
			return "Invalid license data provided.";
		}
		return impl.updateLicense(license);
	}

	@Override
	public Response downloadLicense(int id) {
		if (id <= 0) {
			return Response.status(404).entity("Invalid license ID.").build();
		}

		License lic = impl.getById(id);
		if (lic == null) {
			return Response.status(404).entity("License with ID " + id + " not found.").build();
		}

		String fileName = lic.getName() + ".lic";
		String licenseContent = generateLicenseContent(lic);

		// Write content to file
		File file = new File(fileName);
		try {
			if (file.createNewFile()) {
				try (FileWriter writer = new FileWriter(file)) {
					writer.write(licenseContent);
				}
			}
		} catch (IOException e) {
			logger.error("Error creating license file.", e);
			return Response.status(500).entity("Error creating license file.").build();
		}

		ResponseBuilder rb = Response.ok(file);
		rb.header("Content-Disposition", "attachment; filename=" + file.getName());
		return rb.build();
	}

	@Override
	public List<License> getLicenses(String input) {
		if (input == null || input.trim().isEmpty()) {
			return new ArrayList<>();
		}
		return impl.searchLicenses(input);
	}

	@Override
	public List<License> getLicensePage(int page) {
		return impl.licensePagination(page);
	}

	@Override
	public String createLicenseFromTemplate(int templateId, License license) {
		// Fetch the template
		Template template = new TemplateService().getTemplate(templateId);
		if (template == null || template.getId() <= 0) {
			return "Template with ID " + templateId + " not found.";
		}

		// Populate template properties into the license properties
		template.getTemplateProperties().forEach(prop -> {
			LicenseProperty licenseProperty = new LicenseProperty();
			licenseProperty.setName(prop.getName());
			licenseProperty.setValue(prop.getValue());
			license.getProperties().add(licenseProperty);
		});

		// Save the license with the populated properties
		return createLicense(license);
	}

	@Override
	public String validateLicenseStatus(int id) {
		License license = impl.getById(id);
		if (license == null) {
			return "License with ID " + id + " not found.";
		}

		// Validate license based on expiration date
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
			Date expiryDate = sdf.parse(license.getExpiryDate());
			Date currentDate = new Date();

			if (currentDate.after(expiryDate)) {
				license.setStatus("Expired");
			} else {
				license.setStatus("Valid");
			}

			impl.updateLicense(license); // Update the license status in the database
			return "License status updated to: " + license.getStatus();
		} catch (ParseException e) {
			logger.error("Error validating license status.", e);
			return "Error validating license status.";
		}
	}

	/**
	 * Calculate the expiration date based on start date and floating expiry.
	 */
	@Override
	public String calculateLicenseExpiry(int id) { // Fetch License by ID
		License license = impl.getById(id);
		if (license == null) {
			logger.error("License with ID {} not found.", id);
			return null;
		}
		return calculateLicenseExpiry(license); // Delegate to overloaded method
	}

	public String calculateLicenseExpiry(License license) { // Overloaded method
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
			Date startDate = sdf.parse(license.getStartDate());
			long floatingMillis = (long) license.getFloatExp() * 24L * 60L * 60L * 1000L; // Days to milliseconds
			Date expiryDate = new Date(startDate.getTime() + floatingMillis);
			return sdf.format(expiryDate);
		} catch (ParseException e) {
			logger.error("Error calculating license expiration date.", e);
			return null;
		}
	}

	/**
	 * Generate license content for the download.
	 */
	private String generateLicenseContent(License lic) {
		StringBuilder licenseContent = new StringBuilder();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);

		licenseContent.append("# License Generated on: ").append(new Date()).append("\n");
		licenseContent.append("licenseKey = ").append(new LicenseKeyGenerator().generateKey(16, 4, '-')).append("\n");
		licenseContent.append("signature = ").append(lic.getSignKey().getPublic_key()).append("\n");
		licenseContent.append("version = 1\n");
		licenseContent.append("startDate = ").append(lic.getStartDate()).append("\n");
		licenseContent.append("expiryDate = ").append(lic.getExpiryDate()).append("\n");

		if (lic.getProperties() != null && !lic.getProperties().isEmpty()) {
			for (LicenseProperty prop : lic.getProperties()) {
				licenseContent.append("property_").append(prop.getName()).append(" = ").append(prop.getValue())
						.append("\n");
			}
		} else {
			licenseContent.append("property = No properties available.\n");
		}

		return licenseContent.toString();
	}

	/**
	 * Populate template properties into license properties.
	 */
	public List<LicenseProperty> populateLicenseProperties(int licenseId) {
		License license = impl.getById(licenseId);
		if (license == null) {
			logger.error("License with ID {} not found.", licenseId);
			return null;
		}
		return license.getProperties();
	}

	/**
	 * Save multiple license properties to the database.
	 */
	public String saveLicenseProperties(List<LicenseProperty> properties) {
		if (properties == null || properties.isEmpty()) {
			return "Invalid properties.";
		}
		try {
			properties.forEach(prop -> impl.createProperty(prop)); // Ensure `createProperty` exists in DAO.
			return "License properties saved successfully.";
		} catch (Exception e) {
			logger.error("Error saving license properties.", e);
			return "Error saving license properties.";
		}
	}

	/**
	 * Retrieve all license properties from the database.
	 */
	public List<LicenseProperty> getAllLicenseProperties() {
		return impl.getAllProperties(); // Ensure `getAllProperties` exists in DAO.
	}

	/**
	 * Update an existing license property.
	 */
	public String updateLicenseProperty(LicenseProperty property) {
		if (property == null || property.getId() <= 0) {
			return "Invalid property data provided.";
		}
		return impl.updateProperty(property); // Ensure `updateProperty` exists in DAO.
	}

	/**
	 * Delete a specific license property by ID.
	 */
	public String deleteLicenseProperty(int id) {
		if (id <= 0) {
			return "Invalid property ID.";
		}
		return impl.deleteProperty(id); // Ensure `deleteProperty` exists in DAO.
	}

}
