var PaginationStrip = Vue.component("pagination-strip", {
  props: ["currentPage", "pages", "maxPerPage", "itemCount"],
  methods: {
    async onChange(event) {
      const numberToGet = parseInt(event.target.value);
      this.$emit("update:maxPerPage", numberToGet);
      this.$emit("refresh");
    },
    async onNext() {
      if (this.currentPage < this.pages) {
        this.$emit("update:currentPage", this.currentPage + 1);
        this.$emit("refresh");
      }
    },
    async onPrev() {
      if (this.currentPage > 1) {
        this.$emit("update:currentPage", this.currentPage - 1);
        this.$emit("refresh");
      }
    },
    async onFirst() {
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onLast() {
      this.$emit("update:currentPage", this.pages);
      this.$emit("refresh");
    },
  },
  template: `
    <th class="tableHeader" colspan="9" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
      <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
      <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
        <span>Page {{currentPage}} of {{pages}}</span>
        <ul class="pagination pagination-sm" style="margin: 0; border: none;">
          <li :class="{disabled: currentPage === 1}">
            <span @click="onFirst()"><i class="glyphicon glyphicon-step-backward"></i></span>
          </li>
          <li :class="{disabled: currentPage === 1}">
            <span @click="onPrev()"><i class="glyphicon glyphicon-chevron-left"></i></span>
          </li>
          <li :class="{disabled: currentPage === pages}">
            <span @click="onNext()"><i class="glyphicon glyphicon-chevron-right"></i></span>
          </li>
          <li :class="{disabled: currentPage === pages}">
            <span @click="onLast()"><i class="glyphicon glyphicon-step-forward"></i></span>
          </li>
        </ul>
        <select class="pages-form-control" @change="onChange($event)">
          <option value="10" :selected="maxPerPage === 10">10</option>
          <option value="25" :selected="maxPerPage === 25">25</option>
          <option value="50" :selected="maxPerPage === 50">50</option>
          <option value="100" :selected="maxPerPage === 100">100</option>
        </select>
      </div>
    </th>
  `,
});

var keyList = Vue.component("key-list", {
  components: {
    "pagination-strip": PaginationStrip,
  },
  data() {
    return {
      keys: [],
      allKeys: [], // Store all keys fetched from API
      noShow: false,
      isFetching: true,
      hasDeleted: false,
      hasUpdated: false,
      did: 0,
      newKey: {
        id: 0,
        name: "",
        private_key: "",
        public_key: "",
        type: "",
        value: "",
      },
      currentPage: 1,
      pages: 0,
      itemCount: 0,
      maxPerPage: 10,
      search: "",
      sortOrder: "asc",
      sortField: "name",
    };
  },
  methods: {
    validateForm(key) {
      // Basic validation - check if required fields are filled
      if (!key.name || key.name.trim() === "") {
        alert("Key name is required");
        return false;
      }

      if (!key.type || key.type === "") {
        alert("Key type is required");
        return false;
      }

      return true;
    },

    async displayData() {
      this.isFetching = true;
      try {
        const res = await axios.get(`${this.apiBaseUrl}/keys`);

        if (res.status === 200) {
          // Store all keys
          this.allKeys = res.data;

          // Update count and pagination info
          this.itemCount = this.allKeys.length;
          this.pages = Math.ceil(this.itemCount / this.maxPerPage);

          // Apply pagination to the displayed keys
          this.applyPagination();
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        this.isFetching = false;
      }
    },

    async createKey() {
      console.log("=== CREATE KEY DEBUG START ===");
      console.log("newKey data:", JSON.stringify(this.newKey, null, 2));
      console.log("API URL:", `${this.apiBaseUrl}/keys/save`);

      if (!this.validateForm(this.newKey)) {
        console.log("VALIDATION FAILED");
        return;
      }

      console.log("VALIDATION PASSED");

      try {
        const response = await fetch(`${this.apiBaseUrl}/keys/save`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(this.newKey),
        });

        console.log("Raw response:", response);
        console.log("Response status:", response.status);
        console.log("Response ok:", response.ok);

        if (response.ok) {
          console.log("SUCCESS - clearing form and switching tabs");
          this.clearForm();
          await this.displayData();
          this.switchToListTab();

          // Show success modal
          setTimeout(() => {
            $("#onSuccessModal").modal("show");
          }, 300);
        } else {
          console.log("RESPONSE NOT OK");
          const errorText = await response.text();
          console.log("Error response:", errorText);

          // Set custom error message and show modal
          this.errorMessage =
            errorText || "An error occurred while creating the key.";
          $("#customErrorModal").modal("show");
        }
      } catch (error) {
        console.log("CATCH BLOCK - ERROR:", error);

        // Set custom error message and show modal
        this.errorMessage = "Network error: " + error.message;
        $("#customErrorModal").modal("show");
      }

      console.log("=== CREATE KEY DEBUG END ===");
    },

    // Add a method to show temporary success message as backup
    showSuccessMessage() {
      // Create a temporary success alert
      const alertHtml = `
        <div class="alert alert-success alert-dismissible" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
          <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
          <strong>Success!</strong> Key created successfully.
        </div>
      `;

      $("body").append(alertHtml);

      // Auto-dismiss after 3 seconds
      setTimeout(() => {
        $(".alert-success").fadeOut();
      }, 3000);
    },

    applyPagination() {
      const startIndex = (this.currentPage - 1) * this.maxPerPage;
      const endIndex = startIndex + this.maxPerPage;

      // Apply pagination to the keys
      this.keys = this.allKeys.slice(startIndex, endIndex);
    },

    // Toggle sort order
    toggleSort(field) {
      if (this.sortField === field) {
        this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
      } else {
        this.sortField = field;
        this.sortOrder = "asc";
      }

      // Sort and re-apply pagination
      this.sortKeys();
      this.applyPagination();
    },

    // Sort keys
    sortKeys() {
      this.allKeys.sort((a, b) => {
        const modifier = this.sortOrder === "asc" ? 1 : -1;
        if (a[this.sortField] < b[this.sortField]) return -1 * modifier;
        if (a[this.sortField] > b[this.sortField]) return 1 * modifier;
        return 0;
      });
    },

    exportKey(id) {
      axios
        .get(`${this.apiBaseUrl}/keys/export/${id}`, {
          responseType: "blob",
        })
        .then((response) => {
          if (response.status == 200) {
            const fileURL = window.URL.createObjectURL(response.data);
            const a = document.createElement("a");
            a.href = fileURL;
            const fileName =
              this.keys.find((key) => key.id === id)?.name + ".lic";
            a.setAttribute("download", fileName);
            document.body.appendChild(a);
            a.click();
            $("#exportSuccessModal").modal("show");
          }
        })
        .catch(() => {
          $("#exportErrorModal").modal("show");
        });
    },

    deleteKey(id) {
      axios
        .delete(`${this.apiBaseUrl}/keys/delete/${id}`)
        .then((response) => {
          if (response.status === 200) {
            $("#deleteKeyModal").modal("hide");
            $("#deleteKeySuccess").modal("show");
            this.displayData();
          }
        })
        .catch(() => {
          $("#onFailureModal").modal("show");
        });
    },

    update(id) {
      axios.get(`${this.apiBaseUrl}/keys/get/${id}`).then((res) => {
        this.newKey = res.data;
      });
    },

    importKey(event) {
      const fileInput = this.$refs.keyFileInput;
      const file = fileInput.files[0];

      if (!file) {
        $("#importErrorModal").modal("show");
        return;
      }

      const formData = new FormData();
      formData.append("file", file);

      axios
        .post(`${this.apiBaseUrl}/keys/import`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((response) => {
          if (response.status === 200) {
            $("#importSuccessModal").modal("show");
            this.displayData();
            fileInput.value = ""; // Clear the file input

            // Switch to list tab and refresh
            this.switchToListTab();
          }
        })
        .catch((error) => {
          console.error("Error importing key:", error);
          $("#importErrorModal").modal("show");
        });
    },

    async createNewKey(newKey) {
      try {
        const myJSON = JSON.stringify(newKey);
        const res = await axios.post(`${this.apiBaseUrl}/keys/save`, myJSON, {
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (res.status === 200) {
          console.log("Key created successfully"); // Debug log

          // Clear the form first
          this.clearForm();

          // Refresh the data to include the new key
          await this.displayData();

          // Switch to list tab
          this.switchToListTab();

          // Show success modal after a slight delay
          setTimeout(() => {
            console.log("Showing success modal"); // Debug log
            $("#onSuccessModal").modal("show");
          }, 300);
        }
      } catch (error) {
        console.error("Error creating key:", error);
        $("#onFailureModal").modal("show");
      }
    },

    switchToListTab() {
      // Use jQuery to properly switch tabs
      $('.nav-tabs a[href="#list"]').tab("show");

      // Alternative method if the above doesn't work
      setTimeout(() => {
        // Remove active class from all tabs and tab panes
        $(".nav-tabs li").removeClass("active");
        $(".tab-pane").removeClass("active");

        // Add active class to list tab and pane
        $(".nav-tabs li:first-child").addClass("active");
        $("#list").addClass("active");
      }, 100);
    },

    // Helper method to clear the form
    clearForm() {
      this.newKey = {
        id: 0,
        name: "",
        private_key: "",
        public_key: "",
        type: "",
        value: "",
      };
    },

    async validateKey(key) {
      try {
        // Determine if this is a new key (no ID) or existing key
        if (key.id === 0 || !key.id) {
          // This is handled in createKey() now
          await this.createNewKey(key);
        } else {
          // Check for duplicates when updating existing key
          const res = await axios.get(`${this.apiBaseUrl}/keys`);
          const result = res.data.find(
            (x) => x.name === key.name && x.id !== key.id
          );
          if (result) {
            $("#editFormModal").modal("hide");
            $("#onDuplicateModal").modal("show");
          } else {
            await this.keyUpdate(key);
          }
        }
      } catch (error) {
        $("#onFailureModal").modal("show");
      }
    },

    async keyUpdate(existingKey) {
      try {
        const myJSON = JSON.stringify(existingKey);
        const res = await axios.post(`${this.apiBaseUrl}/keys/save`, myJSON, {
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (res.status === 200) {
          $("#editFormModal").modal("hide");
          await this.displayData();
          $("#onSuccessModal").modal("show");
        }
      } catch (error) {
        console.error("Error updating key:", error);
        $("#onFailureModal").modal("show");
      }
    },

    async searchKeys(query) {
      if (query !== "") {
        if (query.startsWith(" ")) {
          this.search = "";
        } else {
          const res = await axios.get(
            `${this.apiBaseUrl}/keys/search/${query}`
          );
          if (res.status == 200) {
            this.allKeys = res.data;
            this.itemCount = this.allKeys.length;
            this.pages = Math.ceil(this.itemCount / this.maxPerPage);
            this.currentPage = 1; // Reset to first page on new search
            this.applyPagination();
          }
        }
      } else {
        this.displayData();
      }
    },

    deleteModal(id) {
      this.did = id;
    },

    reloadPage() {
      window.location.reload(true);
    },
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },
  },
  watch: {
    // Watch for changes in pagination parameters
    currentPage() {
      this.applyPagination();
    },
    maxPerPage() {
      this.pages = Math.ceil(this.itemCount / this.maxPerPage);
      this.currentPage = 1; // Reset to first page when changing items per page
      this.applyPagination();
    },
  },
  mounted() {
    this.displayData();
  },
  template: `
 <div class="main-section" style="font-family: 'Open Sans', sans-serif;">
        <div class="container">
            <div class="row">
                <div class="col-sm-12" style="text-align: center">
                    <h1>Key Manager</h1>
                </div>
                <div class="col-sm-12">
                    <!-- Tabs Navigation -->
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="active"><a href="#list" role="tab" data-toggle="tab">List Keys</a></li>
                        <li><a href="#create" role="tab" data-toggle="tab">Create Key</a></li>
                        <li><a href="#import" role="tab" data-toggle="tab">Import Key</a></li>
                    </ul>

                    <!-- Tabs Content -->
                    <div class="tab-content">
                        <!-- List Tab -->
                        <div class="tab-pane active" id="list">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <pagination-strip
                                                :current-page.sync="currentPage"
                                                :pages="pages"
                                                :max-per-page.sync="maxPerPage"
                                                :item-count="itemCount"
                                                @refresh="applyPagination"
                                            ></pagination-strip>
                                        </tr>
                                        <tr>
                                            <th @click="toggleSort('name')" style="cursor: pointer;">
                                                Name 
                                                <i v-if="sortField === 'name'" :class="['fas', sortOrder === 'asc' ? 'fa-sort-up' : 'fa-sort-down']"></i>
                                            </th>
                                            <th @click="toggleSort('type')" style="cursor: pointer;">
                                                Type
                                                <i v-if="sortField === 'type'" :class="['fas', sortOrder === 'asc' ? 'fa-sort-up' : 'fa-sort-down']"></i>
                                            </th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody v-show='!isFetching'>
                                        <tr v-for="key in keys">
                                            <td style="cursor: pointer;" @click="update(key.id)" data-toggle="modal" data-target="#viewModal">{{key.name}}</td>
                                            <td>{{key.type}}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button @click="update(key.id)" class="btn btn-light" data-toggle="modal" data-target="#editFormModal">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                                                        </svg>
                                                    </button>
                                                    <button @click="exportKey(key.id)" class="btn btn-light">
                                                                                                               <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                                                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                                                        </svg>
                                                    </button>
                                                    <button @click="deleteModal(key.id)" class="btn btn-light" data-toggle="modal" data-target="#deleteKeyModal">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                            <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody v-show="isFetching">
                      <tr>
                        <td colspan="8" class="text-center">
                          <p>Loading signing keys...</p>
                        </td>
                      </tr>
                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <pagination-strip
                                                :current-page.sync="currentPage"
                                                :pages="pages"
                                                :max-per-page.sync="maxPerPage"
                                                :item-count="itemCount"
                                                @refresh="applyPagination"
                                            ></pagination-strip>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                    <!-- Create Tab -->
                    <div class="tab-pane" id="create">
                        <div style="margin-top: 20px;">
                            <div class="row">
                                <div class="col-sm-2"></div>
                                <div class="col-sm-8">
                                    <form class="form-horizontal">
                                        <div class="form-group">
                                            <div class="col-sm-3">
                                                <label class="control-label">Key Name</label>
                                            </div>
                                            <div class="col-sm-6">
                                                <input type="text" v-model="newKey.name" class="form-control">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-3">
                                                <label class="control-label">Key Type</label>
                                            </div>
                                            <div class="col-sm-6">
                                                <select v-model="newKey.type" class="form-control">
                                                    <option value="">Select Type</option>
                                                    <option value="RSA">RSA</option>
                                                    <option value="DSA">DSA</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6">
                                               <button @click.prevent="createKey()" class="request-button form-control">
    Save Key
</button>

                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                  <!-- Import Tab -->
<div class="tab-pane" id="import">
    <div class="row content-container">
        <div class="col-sm-2"></div>
        <div class="col-sm-8">
            <div class="form-horizontal" style="margin-top: 20px;">
                <div class="form-group">
                    <div class="col-sm-3">
                        <label class="control-label">Upload Key File</label>
                    </div>
                    <div class="col-sm-6">
                        <input type="file" class="form-control" accept=".key" ref="keyFileInput">
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3"></div>
                    <div class="col-sm-6">
                        <button class="request-button form-control" @click.prevent="importKey($event)">
                            Import Key
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
                </div> <!-- End of tab-content -->
            </div> <!-- End of col-sm-12 -->
        </div> <!-- End of row -->

        <!-- View Modal -->
        <div class="modal fade" id="viewModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Key Details</h5>
                        <button type="button" class="close" data-dismiss="modal" style="margin-top: -27px;">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">Name</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" v-model="newKey.name" disabled>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">Type</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" v-model="newKey.type" disabled>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">Public Key</label>
                                <div class="col-sm-9">
                                    <textarea class="form-control" v-model="newKey.public_key" disabled rows="3"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

   <!-- Import Success Modal -->
<div class="modal fade" id="importSuccessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" style="margin-top: -27px;">&times;</button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p class="lead mb-0">Key imported successfully!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Import Error Modal -->
<div class="modal fade" id="importErrorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" style="margin-top: -27px;">&times;</button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p class="lead mb-0">Failed to import key. Please ensure you're uploading a valid .key file.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>



        <!-- Edit Modal -->
        <div class="modal fade" id="editFormModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Key</h5>
                        <button type="button" class="close" data-dismiss="modal" style="margin-top: -27px;">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal" @submit.prevent="keyUpdate(newKey)">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">Name</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" v-model="newKey.name">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">Type</label>
                                <div class="col-sm-9">
                                    <select class="form-control" v-model="newKey.type">
                                          <option value="RSA">RSA</option>
                                        <option value="DSA">DSA</option>
                                    </select>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Modal -->
        <div class="modal fade" id="deleteKeyModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Delete Key</h5>
                        <button type="button" class="close" data-dismiss="modal" style="margin-top: -27px;">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete this key?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" @click="deleteKey(did)">Delete</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Modal -->
        <div class="modal fade" id="onSuccessModal" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Success</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body text-center">
                                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                                <p>Operation completed successfully!</p>
                            </div>
                        </div>
                    </div>
        </div>

        <!-- Error Modal -->
        <div class="modal fade" id="onFailureModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                        <p class="lead mb-0">An error occurred. Please try again.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Success Modal -->
        <div class="modal fade" id="exportSuccessModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <p class="lead mb-0">Key exported successfully!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Error Modal -->
        <div class="modal fade" id="exportErrorModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                        <p class="lead mb-0">Failed to export key.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Success Modal -->
        <div class="modal fade" id="deleteKeySuccess" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <p class="lead mb-0">Key deleted successfully!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Duplicate Key Modal -->
        <div class="modal fade" id="onDuplicateModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                        <p class="lead mb-0">A key with this name already exists.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div> <!-- End of container -->
</div> <!-- End of main-section -->
  `,
});

new Vue({
  el: "#keylist",
  components: {
    "key-list": keyList,
  },
  template: `
    <div class="wrapper">
      <key-list></key-list>
    </div>
  `,
});
