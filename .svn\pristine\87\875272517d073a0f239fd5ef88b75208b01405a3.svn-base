package com.accolm.licenseManager.DAO;

import java.util.List;

/**
 * Generic DAO interface for License entity operations.
 *
 * @param <T> The type of entity (e.g., License).
 * @param <P> The secondary entity type (e.g., LicenseProperty).
 */
public interface LicenseDAO<T, P> {

    /**
     * Creates a new license in the database.
     *
     * @param key The license object to be created.
     * @return A string indicating the result of the operation.
     */
    String create(T key);

    /**
     * Retrieves a license by its ID.
     *
     * @param id The ID of the license.
     * @return The license object if found, null otherwise.
     */
    T getById(int id);

    /**
     * Removes a license by its ID.
     *
     * @param id The ID of the license to remove.
     * @return A string indicating the result of the operation.
     */
    String remove(int id);

    /**
     * Counts the total number of licenses in the database.
     *
     * @return The total number of licenses.
     */
    long countLicenses();

    /**
     * Retrieves all licenses from the database.
     *
     * @return A list of all licenses.
     */
    List<T> getAllLicenses();

    /**
     * Updates an existing license in the database.
     *
     * @param key The license object with updated information.
     * @return A string indicating the result of the update operation.
     */
    String updateLicense(T key);

    /**
     * Searches for licenses based on a given search term.
     *
     * @param search The search term (e.g., license name).
     * @return A list of licenses that match the search term.
     */
    List<T> searchLicenses(String search);

    /**
     * Retrieves a paginated list of licenses.
     *
     * @param page The page number to retrieve.
     * @return A list of licenses for the specified page.
     */
    List<T> licensePagination(int page);

    // New Methods for LicenseProperty Operations

    /**
     * Creates a new license property in the database.
     *
     * @param property The license property object to be created.
     * @return A string indicating the result of the operation.
     */
    String createProperty(P property);

    /**
     * Updates an existing license property in the database.
     *
     * @param property The license property object with updated information.
     * @return A string indicating the result of the update operation.
     */
    String updateProperty(P property);

    /**
     * Deletes a license property by its ID.
     *
     * @param id The ID of the license property to delete.
     * @return A string indicating the result of the operation.
     */
    String deleteProperty(int id);

    /**
     * Retrieves all license properties from the database.
     *
     * @return A list of all license properties.
     */
    List<P> getAllProperties();
}
