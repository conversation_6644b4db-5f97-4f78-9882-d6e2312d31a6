var PaginationStrip = Vue.component("pagination-strip", {
  props: ["currentPage", "pages", "maxPerPage", "itemCount"],
  methods: {
    async onChange(event) {
      const numberToGet = parseInt(event.target.value);
      this.$emit("update:maxPerPage", numberToGet);
      this.$emit("refresh");
    },
    async onNext() {
      if (this.currentPage < this.pages) {
        this.$emit("update:currentPage", this.currentPage + 1);
        this.$emit("refresh");
      }
    },
    async onPrev() {
      if (this.currentPage > 1) {
        this.$emit("update:currentPage", this.currentPage - 1);
        this.$emit("refresh");
      }
    },
    async onFirst() {
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onLast() {
      this.$emit("update:currentPage", this.pages);
      this.$emit("refresh");
    },
  },
  template: `
    <th class="tableHeader" colspan="9" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
      <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
      <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
        <span>Page {{currentPage}} of {{pages}}</span>
        <ul class="pagination pagination-sm" style="margin: 0; border: none;">
          <li :class="{disabled: currentPage === 1}">
            <span @click="onFirst()"><i class="glyphicon glyphicon-step-backward"></i></span>
          </li>
          <li :class="{disabled: currentPage === 1}">
            <span @click="onPrev()"><i class="glyphicon glyphicon-chevron-left"></i></span>
          </li>
          <li :class="{disabled: currentPage === pages}">
            <span @click="onNext()"><i class="glyphicon glyphicon-chevron-right"></i></span>
          </li>
          <li :class="{disabled: currentPage === pages}">
            <span @click="onLast()"><i class="glyphicon glyphicon-step-forward"></i></span>
          </li>
        </ul>
        <select class="pages-form-control" @change="onChange($event)">
          <option value="10" :selected="maxPerPage === 10">10</option>
          <option value="25" :selected="maxPerPage === 25">25</option>
          <option value="50" :selected="maxPerPage === 50">50</option>
          <option value="100" :selected="maxPerPage === 100">100</option>
        </select>
      </div>
    </th>
  `,
});

var displayTemplates = Vue.component("display-templates", {
  components: {
    "pagination-strip": PaginationStrip,
  },
  data() {
    return {
      allTemplates: [],
      templates: [],
      hasDeleted: false,
      templateToDelete: "",
      search: "",
      did: 0,
      newTemp: {
        id: 0,
        name: "",
        licenseExpiry: " ",
        floatExpiry: 0,
        expiryType: "never",
        templateProperties: [{}],
      },
      rowCount: 1,
      disableBtn: true,
      noShow: false,
      isFetching: true,
      currentPage: 1,
      pages: 0,
      itemCount: 0,
      maxPerPage: 10,
      sortOrder: "asc",
      sortField: "id",
      selectedFile: null,
    };
  },
  methods: {
    submitChanges() {
      this.updateTemplate(this.newTemp);
    },
    addProp() {
      this.newTemp.templateProperties.push({
        name: "",
        value: "",
      });
      this.rowCount++;
    },

    removeProp(index) {
      if (this.newTemp.templateProperties.length > 1) {
        this.newTemp.templateProperties.splice(index, 1);
        this.rowCount--;
      }
    },
    formatExpiryDate(expiry) {
      if (!expiry) return "Never";
      if (expiry === "None" || expiry === " ") return "Never";

      if (expiry.includes("-")) {
        const date = new Date(expiry);
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        const year = date.getFullYear();
        return `${month}/${day}/${year}`;
      }
      return expiry;
    },

    async displayData() {
      this.isFetching = true;
      try {
        const [res, resCount] = await Promise.all([
          axios.get(
            `${this.apiBaseUrl}/templates/pagination/${this.currentPage}?perPage=${this.maxPerPage}`
          ),
          axios.get(`${this.apiBaseUrl}/templates/count`),
        ]);

        if (res.status === 200 && resCount.status === 200) {
          this.itemCount = resCount.data;
          this.pages = Math.ceil(resCount.data / this.maxPerPage);

          // Check if the server returned all results instead of just the page
          if (res.data.length > this.maxPerPage) {
            console.log(
              "Server returned all results, applying client-side pagination"
            );
            // Store all templates
            this.allTemplates = res.data;
            // Apply client-side pagination
            const startIndex = (this.currentPage - 1) * this.maxPerPage;
            const endIndex = startIndex + this.maxPerPage;
            this.templates = this.allTemplates.slice(startIndex, endIndex);
          } else {
            // Server handled pagination correctly
            this.templates = res.data;
          }

          console.log("Templates loaded for page:", this.templates.length);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        this.isFetching = false;
      }

      return Promise.resolve();
    },
    toggleSort() {
      this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
    },
    async deleteTemp(id) {
      try {
        const response = await axios.delete(
          `${this.apiBaseUrl}/templates/delete/${id}`
        );

        if (response.status === 200) {
          // Hide the delete confirmation modal first
          $("#deleteTempModal").modal("hide");
          $(".modal-backdrop").remove();

          // Wait a moment for the first modal to fully close
          setTimeout(async () => {
            // Refresh the template list
            await this.displayData();

            // Then show the success modal
            const successModal = $("#successModal");
            successModal.removeAttr("aria-hidden");
            successModal.modal("show");

            // Auto-hide after 3 seconds
            setTimeout(() => {
              successModal.modal("hide");
            }, 3000);
          }, 300);
        }
      } catch (error) {
        console.error("Error deleting template:", error);
        $("#deleteTempModal").modal("hide");
        $("#errorModal").modal("show");
      }
    },

    update(id) {
      axios.get(`${this.apiBaseUrl}/templates/get/${id}`).then((res) => {
        this.newTemp = res.data;

        // Determine the expiry type based on the template data
        if (
          !this.newTemp.licenseExpiry ||
          this.newTemp.licenseExpiry === " " ||
          this.newTemp.licenseExpiry === "None" ||
          this.newTemp.licenseExpiry === "Never"
        ) {
          this.newTemp.expiryType = "none";
        } else if (this.newTemp.floatExpiry > 0) {
          // Determine if it's floating from creation or first use
          this.newTemp.expiryType = "floating-creation"; // Default to creation
        } else {
          this.newTemp.expiryType = "fixed";
        }

        if (res.data.templateProperties.length === 0) {
          this.newTemp.templateProperties.push({
            name: "",
            value: "",
          });
        }
      });
    },

    async updateTemplate(newTemp) {
      // Prepare the template data based on the expiry type
      const templateData = { ...newTemp };

      if (templateData.expiryType === "none") {
        templateData.licenseExpiry = "Never"; // Changed from "None" to "Never"
        templateData.floatExpiry = 0;
      } else if (templateData.expiryType === "fixed") {
        // For fixed expiry, set a placeholder value that indicates it's a fixed type
        templateData.licenseExpiry = "Fixed";
        templateData.floatExpiry = 0;
      } else {
        // For floating options
        if (templateData.expiryType === "floating-firstuse") {
          templateData.floatFromFirstUse = true;
        }
        templateData.licenseExpiry = "Never"; // Changed from "None" to "Never"
      }

      console.log("Submitting template data:", JSON.stringify(templateData));
      var myJSON = JSON.stringify(templateData);
      try {
        const response = await axios.post(
          `${this.apiBaseUrl}/templates/add`,
          myJSON,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        console.log("API response:", response);
        if (
          (response.status === 200 || response.status === 201) &&
          response.data
        ) {
          console.log("Success! Showing modal...");

          // Reset form fields
          this.resetFormFields();

          // Refresh the template list
          this.displayData();

          // Switch to the list tab
          $('a[href="#list"]').tab("show");

          // Completely remove all modal-related elements and classes
          $(".modal").modal("hide");
          $(".modal-backdrop").remove();
          $("body").removeClass("modal-open").css("padding-right", "");

          // Force the modal to be visible with inline styles
          const successModal = $("#onSuccessModal");

          // Check if modal exists
          if (successModal.length > 0) {
            console.log("Modal element found, applying direct styles");

            // Apply direct styles to ensure visibility
            successModal.css({
              display: "block",
              "z-index": "9999",
              "background-color": "rgba(0,0,0,0.5)",
              position: "fixed",
              top: "0",
              right: "0",
              bottom: "0",
              left: "0",
              overflow: "auto",
              outline: "0",
            });

            // Also style the modal dialog
            successModal.find(".modal-dialog").css({
              margin: "10% auto",
              "z-index": "10000",
            });

            // Add the modal-open class to body
            $("body").addClass("modal-open");

            // Try both methods of showing the modal
            successModal.modal("show");

            // Add a close handler after a delay
            setTimeout(() => {
              // Auto-hide after 3 seconds
              successModal.modal("hide");
            }, 3000);
          } else {
            console.error("Modal element not found, creating one dynamically");

            // Create a modal dynamically if it doesn't exist
            const modalHTML = `
          <div class="modal fade" id="dynamicSuccessModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Success</h5>
                  <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                  </button>
                </div>
                <div class="modal-body text-center">
                  <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                  <p>Operation completed successfully!</p>
                </div>
              </div>
            </div>
          </div>
        `;

            $("body").append(modalHTML);
            $("#dynamicSuccessModal").modal("show");

            // Auto-hide after 3 seconds
            setTimeout(() => {
              $("#dynamicSuccessModal").modal("hide");
              $("#dynamicSuccessModal").remove();
            }, 3000);
          }
        }
      } catch (error) {
        console.error("Error creating template:", error);
        // Show error modal
        $("#errorModal").modal("show");
      }
    },
    resetFormFields() {
      // Reset to default values
      this.newTemp = {
        id: 0,
        name: "",
        licenseExpiry: " ",
        floatExpiry: 0,
        expiryType: "never",
        templateProperties: [{ name: "", value: "" }],
      };
      this.rowCount = 1;
    },
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file && file.name.endsWith(".ltp")) {
        this.selectedFile = file;
        const fileLabel = document.getElementById("selectedFileName");
        if (fileLabel) {
          fileLabel.textContent = file.name;
        }
      } else {
        const modal = document.getElementById("importErrorModal");
        if (modal) {
          const bootstrapModal = new bootstrap.Modal(modal);
          bootstrapModal.show();
        } else {
          console.error("Error modal not found");
        }
        this.resetImportField();
      }
    },

    importTemplate() {
      if (!this.selectedFile) return;

      const formData = new FormData();
      formData.append("file", this.selectedFile);

      axios
        .post(`${this.apiBaseUrl}/templates/file/import`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((response) => {
          // Check if the response indicates success
          if (response.status === 200) {
            // Reset the import field
            this.resetImportField();

            //  small delay to ensure the server has processed the import
            setTimeout(() => {
              // Refresh the data to show the newly imported template
              this.currentPage = 1; // Reset to first page to ensure we see the new template
              this.displayData().then(() => {
                // Show success modal only after data is refreshed
                $("#importSuccessModal").modal("show");
              });
            }, 500);
          } else {
            // If response status is not 200, show error
            $("#importErrorModal").modal("show");
            this.resetImportField();
          }
        })
        .catch((error) => {
          console.error("Import error:", error);
          $("#importErrorModal").modal("show");
          this.resetImportField();
        });
    },
    resetImportField() {
      this.selectedFile = null;
      const input = document.getElementById("templateFile");
      if (input) {
        input.value = "";
        const label = input.nextElementSibling;
        if (label) {
          label.textContent = "Choose template file...";
        }
      }
    },
    deleteModal(id) {
      this.did = id;
      // Find the template with this ID to get its name
      const template = this.templates.find((temp) => temp.id === id);
      this.templateToDelete = template ? template.name : "Unknown template";
    },
    reloadPage() {
      window.location.reload(true);
    },
    closeModal() {
      document.getElementById("closeBtn").click();
    },
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },
    sortedTemplates() {
      // Sort by ID by default to ensure new templates appear at the end
      if (this.sortField === "name") {
        // Only apply name sorting if user explicitly clicks on name column
        const sorted = [...this.templates].sort((a, b) => {
          const modifier = this.sortOrder === "asc" ? 1 : -1;
          if (a[this.sortField] < b[this.sortField]) return -1 * modifier;
          if (a[this.sortField] > b[this.sortField]) return 1 * modifier;
          return 0;
        });
        return sorted;
      } else {
        // Default sort by ID (ascending)
        return [...this.templates].sort((a, b) => a.id - b.id);
      }
    },
  },

  mounted() {
    this.displayData();

    // Global fix for modal accessibility issues
    $(document).on("show.bs.modal", ".modal", function () {
      $(this).removeAttr("aria-hidden");
    });

    // Ensure modals are properly cleaned up when hidden
    $(document).on("hidden.bs.modal", ".modal", function () {
      if ($(".modal:visible").length) {
        $("body").addClass("modal-open");
      } else {
        $(".modal-backdrop").remove();
        $("body").removeClass("modal-open").css("padding-right", "");
      }
    });
  },
  template: `
    <div class="main-section" style="font-family: 'Open Sans', sans-serif;">
        <div class="container">
            <div class="row">
                <div class="col-sm-12" style="text-align: center">
                    <h1>Templates</h1>
                </div>
                <div class="col-sm-12">
                    <!-- Tabs Navigation -->
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="active"><a href="#list" role="tab" data-toggle="tab">List Templates</a></li>
                        <li><a href="#create" role="tab" data-toggle="tab">Create Template</a></li>
                        <li><a href="#import" role="tab" data-toggle="tab">Import Template</a></li>
                    </ul>

                    <!-- Tabs Content -->
                    <div class="tab-content">
                        <!-- List Tab -->
                        <div class="tab-pane active" id="list">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <pagination-strip
                                                :current-page.sync="currentPage"
                                                :pages="pages"
                                                :max-per-page.sync="maxPerPage"
                                                :item-count="itemCount"
                                                @refresh="displayData"
                                            ></pagination-strip>
                                        </tr>
                                        <tr>
                                            <th @click="toggleSort" style="cursor: pointer;">
                                                Name 
                                                <i :class="['fas', sortOrder === 'asc' ? 'fa-sort-up' : 'fa-sort-down']"></i>
                                            </th>
                                            <th>License Expiration</th>
                                            <th>Floating Expiration</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody v-show='!isFetching'>
                                        <tr v-for='temp in sortedTemplates' :key="temp.id">
                                            <td style="cursor: pointer;" @click="update(temp.id)" data-toggle="modal" data-target="#viewModal">
                                                <a href="#">{{ temp.name }}</a>
                                            </td>
                                            <td>{{ formatExpiryDate(temp.licenseExpiry) }}</td>
                                            <td>{{ temp.floatExpiry }}</td>
                                            <td>
                                               <div class="btn-group">
    <button @click="update(temp.id)" class="btn btn-light" data-toggle="modal" data-target="#editFormModal">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
        </svg>
    </button>
    <button data-toggle="modal" data-target="#deleteTempModal" @click="deleteModal(temp.id)" class="btn btn-light">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
            <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
        </svg>
    </button>
</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <pagination-strip
                                                :current-page.sync="currentPage"
                                                :pages="pages"
                                                :max-per-page.sync="maxPerPage"
                                                :item-count="itemCount"
                                                @refresh="displayData"
                                            ></pagination-strip>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                        <!-- Create Template Tab -->
                      
<div class="tab-pane" id="create">
  <div style="margin-top: 20px;">
    <div class="row">
      <div class="col-sm-2"></div>
      <div class="col-sm-8">
        <form class="form-horizontal">
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Template Name</label>
            </div>
            <div class="col-sm-6">
              <input type="text" v-model="newTemp.name" class="form-control">
            </div>
          </div>

          <!-- License Expiration Section -->
          <div class="form-group">
  <div class="col-sm-3">
    <label class="control-label">License Expiration</label>
  </div>
  <div class="col-sm-6">
    <select v-model="newTemp.expiryType" class="form-control">
      <option value="never">Never</option>
      <option value="fixed">Fixed</option>
      <option value="floating-firstuse">Floating days from creation date</option>
    </select>
  </div>
</div>

         

          <!-- Show days input if any Floating option is selected -->
          <div class="form-group" v-if="newTemp.expiryType === 'floating-creation' || newTemp.expiryType === 'floating-firstuse'">
            <div class="col-sm-3">
              <label class="control-label">Floating Days</label>
            </div>
            <div class="col-sm-6">
              <input type="number" v-model="newTemp.floatExpiry" class="form-control">
            </div>
          </div>

          <!-- properties -->
          <div class="form-group">
  <div class="col-sm-12">
    <div class="card shadow mb-4">
      <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary" style="font-family: 'Open Sans';color: #ac2925;font-size: 14px">Template Properties</h6>
      </div>
      <div class="card-body">
        <table class="table table-hover table-bordered">
          <thead>
            <tr>
              <th>Property</th>
              <th>Value</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(prop, index) in newTemp.templateProperties" :key="index">
              <td>
                <input v-model="prop.name" type="text" class="form-control">
              </td>
              <td>
                <input v-model="prop.value" type="text" class="form-control">
              </td>
              <td>
                <button type="button" @click="removeProp(index)" class="btn btn-danger btn-sm">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <button type="button" @click="addProp" class="btn btn-light">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
          Add Property
        </button>
      </div>
    </div>
  </div>
</div>


          <div class="form-group">
            <div class="col-sm-3"></div>
            <div class="col-sm-6">
              <button @click.prevent="updateTemplate(newTemp)" class="request-button form-control">Save Template</button> 
            </div>
          </div>
        </form>
      </div>
      <div class="col-sm-2"></div>
    </div>
  </div>
</div>


                        <!-- Import Tab -->
                        <div class="tab-pane" id="import">
                            <div class="row content-container">
                                <div class="col-sm-2"></div>
                                <div class="col-sm-8">
                                    <div class="form-horizontal" style="margin-top: 20px; padding: 20px;">
                                        <div class="form-group">
                                            <div class="col-sm-3">
                                                <label class="control-label">Upload Template File</label>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="input">
                                                    <input type="file" 
                                                        id="templateFile" 
                                                        @change="handleFileSelect" 
                                                        accept=".ltp" 
                                                        required 
                                                        class="form-control">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6">
                                                <button @click.prevent="importTemplate" class="request-button form-control" :disabled="!selectedFile">Import Template</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Modals -->
                    <!-- View Modal -->
                    <div class="modal fade" id="viewModal" tabindex="-1" role="dialog">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">Template Details</h4>
                                    <button type="button" data-dismiss="modal" class="close" style="
    margin-top: -27px;
">×</button>
                                </div>
                                <div class="modal-body">
                                    <form class="form-horizontal">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">Template Name</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" v-model="newTemp.name" disabled>
                                            </div>
                                        </div>
                                        <!-- In the view modal, update the License Expiry field -->
<div class="form-group">
    <label class="col-sm-4 control-label">License Expiry</label>
    <div class="col-sm-8">
        <input type="text" class="form-control" 
               :value="!newTemp.licenseExpiry || newTemp.licenseExpiry === 'None' || newTemp.licenseExpiry === ' ' ? 'Never' : newTemp.licenseExpiry" 
               disabled>
    </div>
</div>

                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">Floating Days</label>
                                            <div class="col-sm-8">
                                                <input type="number" class="form-control" v-model="newTemp.floatExpiry" disabled>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading">Template Properties</div>
                                                    <div class="panel-body">
                                                        <table class="table table-hover table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th>Property</th>
                                                                    <th>Value</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr v-for="prop in newTemp.templateProperties">
                                                                    <td>{{prop.name}}</td>
                                                                    <td>{{prop.value}}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>

                   <!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteTempModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" style="margin-top: -27px;">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <p>Are you sure you want to delete the template "{{ templateToDelete }}"?</p>
                <div class="mt-3">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" @click="deleteTemp(did)">Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>

                      <!-- Error Modal -->
                    <div class="modal fade" id="importErrorModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Error</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
        <p>Invalid file format. Please select a .ltp file.</p>
      </div>
    </div>
  </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="importSuccessModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>Template imported successfully!</p>
      </div>
    </div>
  </div>
</div>

<!-- Error Modal -->
<div class="modal fade" id="importErrorModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Error</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
        <p>Failed to import template. Please try again.</p>
      </div>
    </div>
  </div>
</div>

<!-- Success Modal for created templates -->
<div class="modal fade" id="onSuccessModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>Operation completed successfully!</p>
      </div>
    </div>
  </div>
</div>



                    <!-- Success Modal -->
                    <div class="modal fade" id="successModal" tabindex="-1" role="dialog">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Success</h5>
                                    <button type="button" class="close" data-dismiss="modal">
                                        <span>&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body text-center">
                                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                                    <p>Operation completed successfully!</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Modal -->
                    <div class="modal fade" id="errorModal" tabindex="-1" role="dialog">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Error</h5>
                                    <button type="button" class="close" data-dismiss="modal">
                                        <span>&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body text-center">
                                    <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                                    <p>An error occurred. Please try again.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
  `,
});

new Vue({
  el: "#template",
  components: {
    "display-templates": displayTemplates,
  },
  template: `
    <div class="wrapper">
      <display-templates></display-templates>
    </div>
  `,
});
