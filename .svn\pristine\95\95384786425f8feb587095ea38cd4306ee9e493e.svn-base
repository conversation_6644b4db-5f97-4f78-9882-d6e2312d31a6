package com.accolm.licenseManager.DAO;

import java.time.ZonedDateTime;

import java.util.List;
import java.util.UUID;

import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.Query;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.accolm.licenseManager.Entities.Template;
import com.accolm.licenseManager.Entities.TemplateProperty;
import com.accolm.licenseManager.Utils.LocalEntityManagerFactory;

public class TemplateDao implements IDao<Template> {

    // Logger Instance
    Logger logger = LogManager.getLogger(TemplateDao.class);

    // Retrieve all templates
    @Override
    public List<Template> all() {
        logger.info("Retrieving all templates...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            Query query = em.createQuery("SELECT t FROM Template t", Template.class);
            @SuppressWarnings("unchecked")
            List<Template> templates = query.getResultList();
            em.getTransaction().commit();
            return templates;
        } catch (Exception e) {
            logger.error("Error retrieving templates: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return null;
    }

    // Retrieve all template properties
    public List<TemplateProperty> allProperties() {
        logger.info("Retrieving all template properties...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            Query query = em.createQuery("SELECT p FROM TemplateProperty p", TemplateProperty.class);
            @SuppressWarnings("unchecked")
            List<TemplateProperty> properties = query.getResultList();
            em.getTransaction().commit();
            return properties;
        } catch (Exception e) {
            logger.error("Error retrieving template properties: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return null;
    }

    @Override
    public String add(Template template) {
        logger.info("Adding template...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            em.merge(template);
            em.getTransaction().commit();
            return "ALLGOOD";
        } catch (Exception e) {
            logger.error("Error adding template: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return null;
    }

    public String addProperty(TemplateProperty property) {
        logger.info("Adding template property...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            em.merge(property);
            em.getTransaction().commit();
            return "ALLGOOD";
        } catch (Exception e) {
            logger.error("Error adding template property: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return null;
    }

    @Override
    public String update(Template template) {
        logger.info("Updating template...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            Template temp = em.find(Template.class, template.getId());
            if (temp != null) {
                temp.setName(template.getName());
                temp.setFloatExpiry(template.getFloatExpiry());
                temp.setLicenseExpiry(template.getLicenseExpiry());
                em.getTransaction().commit();
                return "ALLGOOD";
            }
            return "Template not found";
        } catch (Exception e) {
            logger.error("Error updating template: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return null;
    }

    public String updateProperty(TemplateProperty property) {
        logger.info("Updating template property...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            TemplateProperty temp = em.find(TemplateProperty.class, property.getId());
            if (temp != null) {
                temp.setName(property.getName());
                temp.setValue(property.getValue());
                em.getTransaction().commit();
                return "ALLGOOD";
            }
            return "Template property not found";
        } catch (Exception e) {
            logger.error("Error updating template property: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return null;
    }

    @Override
    public String delete(int id) {
        logger.info("Deleting template...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            Template temp = em.find(Template.class, id);
            if (temp != null) {
                em.remove(temp);
                em.getTransaction().commit();
                return "ALLGOOD";
            }
            return "Template not found";
        } catch (Exception e) {
            logger.error("Error deleting template: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return null;
    }

    public String deleteProperty(int id) {
        logger.info("Deleting template property...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            TemplateProperty temp = em.find(TemplateProperty.class, id);
            if (temp != null) {
                em.remove(temp);
                em.getTransaction().commit();
                return "ALLGOOD";
            }
            return "Template property not found";
        } catch (Exception e) {
            logger.error("Error deleting template property: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return null;
    }

    @Override
    public Template get(int id) {
        logger.info("Getting template...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            return em.find(Template.class, id);
        } catch (Exception e) {
            logger.error("Error getting template: ", e);
        } finally {
            em.close();
        }
        return null;
    }

    @Override
    public long count() {
        logger.info("Counting templates...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            Query query = em.createQuery("SELECT COUNT(t) FROM Template t");
            return (long) query.getSingleResult();
        } catch (Exception e) {
            logger.error("Error counting templates: ", e);
        } finally {
            em.close();
        }
        return 0;
    }

    @Override
    public List<Template> search(String search) {
        logger.info("Searching templates...");
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            Query query = em.createQuery("SELECT t FROM Template t WHERE t.name LIKE :search", Template.class);
            query.setParameter("search", "%" + search + "%");
            @SuppressWarnings("unchecked")
            List<Template> templates = query.getResultList();
            em.getTransaction().commit();
            return templates;
        } catch (Exception e) {
            logger.error("Error searching templates: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return null;
    }

    @Override
    public List<Template> pagination(int page) {
        logger.info("Paginating templates - Page: {}", page);
        final int pageSize = 10; // Define a constant or make it configurable
        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            em.getTransaction().begin();
            Query query = em.createQuery("SELECT t FROM Template t", Template.class);
           // query.setFirstResult((page - 1) * pageSize); // Correct offset calculation
           // query.setMaxResults(pageSize); // Set maximum results to page size
            @SuppressWarnings("unchecked")
            List<Template> templates = query.getResultList();
            em.getTransaction().commit();
            return templates;
        } catch (Exception e) {
            logger.error("Error paginating templates: ", e);
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.close();
        }
        return List.of(); // Return an empty list instead of null for better handling
    }
    
 // Check if a template with the same name exists (case-insensitive, trimmed)
    public boolean existsByName(String templateName) {
        logger.info("Checking if template name exists: {}", templateName);
        if (templateName == null || templateName.trim().isEmpty()) {
            return false;
        }

        EntityManager em = LocalEntityManagerFactory.createEntityManager();
        try {
            Query query = em.createQuery(
                "SELECT COUNT(t) FROM Template t WHERE LOWER(TRIM(t.name)) = :name"
            );
            query.setParameter("name", templateName.trim().toLowerCase());
            Long count = (Long) query.getSingleResult();
            return count > 0;
        } catch (Exception e) {
            logger.error("Error checking template name existence: {}", e.getMessage(), e);
            return false;
        } finally {
            em.close();
        }
    }



    }
