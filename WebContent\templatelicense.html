<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <meta name="description" content="" />
    <meta name="author" content="" />

    <title>License Manager</title>

    <!-- Custom fonts for this template-->
    <link
      href="Assets/vendor/fontawesome-free/css/all.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <link
      href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
      rel="stylesheet"
    />

    <!-- Custom styles for this template-->
    <link href="Assets/css/sb-admin-2.min.css" rel="stylesheet" />
    <link
      href="Assets/vendor/datatables/dataTables.bootstrap4.min.css"
      rel="stylesheet"
    />

    <style>
      [v-cloak] {
        display: none;
      }
    </style>
  </head>

  <body id="page-top">
    <div id="app" v-cloak>
      <!-- Page Wrapper -->
      <div id="wrapper">
        <!-- Sidebar -->
        <headbar></headbar>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
          <!-- Main Content -->
          <div id="content">
            <!-- Topbar -->
            <toolbar></toolbar>
            <!-- End of Topbar -->

            <!-- Begin Page Content -->
            <div class="container-fluid">
              <div class="row">
                <div class="col-lg-7">
                  <div class="card shadow mb-4">
                    <div class="card-header py-3">
                      <h6 class="m-0 font-weight-bold text-primary">
                        New License from Template
                      </h6>
                    </div>
                    <div class="card-body">
                      <form
                        class="form-horizontal"
                        v-on:submit.prevent="onSubmitLicense"
                      >
                        <div class="form-group row">
                          <label
                            class="col-sm-4 text-left control-label col-form-label"
                            >Templates</label
                          >
                          <div class="col-sm-6">
                            <select
                              class="form-control"
                              id="autoSizingSelect"
                              @change="onTemplateChange($event)"
                              required
                            >
                              <option value="0" selected>
                                Select a template
                              </option>
                              <option
                                v-for="template in templates"
                                v-bind:value="template.id"
                              >
                                {{ template.name }}
                              </option>
                            </select>
                          </div>
                        </div>
                        <div class="form-group row">
                          <label
                            class="col-sm-4 text-left control-label col-form-label"
                            >License Name</label
                          >
                          <div class="col-sm-6">
                            <input
                              type="text"
                              v-model="newLicense.name"
                              class="form-control"
                              required
                            />
                          </div>
                        </div>
                        <div class="form-group row">
                          <label
                            class="col-sm-4 text-left control-label col-form-label"
                            >Start Date</label
                          >
                          <div class="col-sm-6">
                            <input
                              type="date"
                              v-model="newLicense.startDate"
                              class="form-control"
                              required
                            />
                          </div>
                        </div>
                        <div class="form-group row">
                          <label
                            class="col-sm-4 text-left control-label col-form-label"
                            >Expiration Date</label
                          >
                          <div class="col-sm-6">
                            <input
                              type="date"
                              v-model="newLicense.expiryDate"
                              class="form-control"
                              required
                            />
                          </div>
                        </div>
                        <div class="form-group row">
                          <label
                            class="col-sm-4 text-left control-label col-form-label"
                            >Floating Days</label
                          >
                          <div class="col-sm-6 mt-2">
                            <input
                              type="number"
                              v-model="newLicense.floatExp"
                              class="form-control"
                            />
                          </div>
                        </div>
                        <div class="form-group row">
                          <label
                            class="col-sm-4 text-left control-label col-form-label"
                            >Hardware Locking</label
                          >
                          <div class="col-sm-6 mt-2">
                            <select
                              class="form-control"
                              v-model="newLicense.hardwareLock"
                              required
                            >
                              <option value="false">False</option>
                              <option value="true">True</option>
                            </select>
                          </div>
                        </div>
                        <div class="form-group row">
                          <label
                            class="col-sm-4 text-left control-label col-form-label"
                            >Signing Key</label
                          >
                          <div class="col-sm-6">
                            <select
                              class="form-control"
                              id="autoSizingSelect"
                              @change="onSigningKeyChange($event)"
                              required
                            >
                              <option value="0" selected>Select a key</option>
                              <option
                                v-for="signingKey in signingKeys"
                                v-bind:value="signingKey.id"
                              >
                                {{ signingKey.name }}
                              </option>
                            </select>
                          </div>
                        </div>

                        <div class="border-top">
                          <div class="card-body">
                            <button
                              class="btn btn-primary"
                              @click="onSubmitLicense"
                            >
                              Save License & Properties
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
                <div class="col-lg-5">
                  <div class="card shadow mb-4">
                    <div class="card-header py-3">
                      <h6 class="m-0 font-weight-bold text-primary">
                        License Properties
                      </h6>
                    </div>
                    <div class="card-body">
                      <div class="row">
                        <div class="col-sm-3"></div>
                        <table
                          class="table table-hover table-bordered border-dark col-sm-12 text-center"
                          style="width: 100% !important"
                        >
                          <thead>
                            <tr class="table-secondary">
                              <th scope="col">Property</th>
                              <th scope="col">Value</th>
                              <th scope="col">Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            <template v-if="newLicense.properties.length == 0">
                              <tr>
                                <!-- <th scope="row">1</th> -->
                                <td>
                                  <input
                                    type="text"
                                    class="form-control"
                                    placeholder="<add a property>"
                                  />
                                </td>
                                <td>
                                  <input
                                    type="text"
                                    class="form-control"
                                    placeholder="<add a value>"
                                  />
                                </td>
                                <td>
                                  <div class="btn-group">
                                    <button
                                      type="button"
                                      class="btn btn-danger btn-sm"
                                    >
                                      <i class="fas fa-trash-alt"></i>
                                    </button>
                                    <button
                                      type="button"
                                      class="btn btn-primary btn-sm"
                                    >
                                      <i
                                        class="fa fa-plus-square"
                                        aria-hidden="true"
                                      ></i>
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            </template>
                            <template v-else>
                              <tr v-for="props in newLicense.properties">
                                <!-- <th scope="row">1</th> -->
                                <td>
                                  <input
                                    type="text"
                                    class="form-control"
                                    v-bind:value="props.name"
                                    placeholder="<add a property>"
                                  />
                                </td>
                                <td>
                                  <input
                                    type="text"
                                    class="form-control"
                                    v-bind:value="props.value"
                                    placeholder="<add a value>"
                                  />
                                </td>
                                <td>
                                  <div class="btn-group">
                                    <button
                                      type="button"
                                      v-on:click(editProp(props.id))
                                      class="btn btn-danger btn-sm"
                                    >
                                      <i class="fas fa-trash-alt"></i>
                                    </button>
                                    <button
                                      type="button"
                                      class="btn btn-primary btn-sm"
                                      v-on:click(deleteProp(props.id))
                                    >
                                      <i
                                        class="fa fa-plus-square"
                                        aria-hidden="true"
                                      ></i>
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            </template>
                          </tbody>
                        </table>

                        <hr style="background-color: black; padding: 0 1px" />
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-12">
                    <div class="card shadow mb-4">
                      <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                          License Status
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="row">
                          <div class="col-sm-3"></div>
                          <table
                            class="table table-hover table-bordered border-dark col-sm-12 text-center"
                            style="width: 100% !important"
                          >
                            <thead>
                              <tr class="table-secondary">
                                <th scope="col">Test Name</th>
                                <th scope="col">Result</th>
                                <th scope="col">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <!-- <th scope="row">1</th> -->
                                <td></td>
                                <td></td>
                                <td></td>
                              </tr>
                            </tbody>
                          </table>

                          <hr style="background-color: black; padding: 0 1px" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- /.container-fluid -->
          </div>
          <!-- End of Main Content -->

          <!-- Footer -->
          <footbar></footbar>
          <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
      </div>
      <!-- End of Page Wrapper -->

      <!-- Scroll to Top Button-->
      <scrolltotop></scrolltotop>

      <!-- Modals -->
      <success-modal></success-modal>
      <error-modal></error-modal>

      <!-- Logout Modal-->
      <div
        class="modal fade"
        id="logoutModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="exampleModalLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="exampleModalLabel">
                Ready to Leave?
              </h5>
              <button
                class="close"
                type="button"
                data-dismiss="modal"
                aria-label="Close"
              >
                <span aria-hidden="true">×</span>
              </button>
            </div>
            <div class="modal-body">
              Select "Logout" below if you are ready to end your current
              session.
            </div>
            <div class="modal-footer">
              <button
                class="btn btn-secondary"
                type="button"
                data-dismiss="modal"
              >
                Cancel
              </button>
              <a class="btn btn-primary" href="#">Logout</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Bootstrap core JavaScript-->
    <script src="Assets/vendor/jquery/jquery.min.js"></script>
    <script src="Assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="Assets/vendor/jquery-easing/jquery.easing.min.js"></script>
    <script src="Assets/js/vue.js"></script>
    <script src="Assets/js/axios.min.js"></script>
    <script src="Assets/js/licenseFromTemplate.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="Assets/js/sb-admin-2.min.js"></script>

    <!-- Page level plugins -->
    <script src="Assets/vendor/chart.js/Chart.min.js"></script>

    <!-- Page level custom scripts -->
    <script src="Assets/js/demo/chart-area-demo.js"></script>
    <script src="Assets/js/demo/chart-pie-demo.js"></script>

    <script src="Assets/vendor/datatables/jquery.dataTables.min.js"></script>
    <script src="Assets/vendor/datatables/dataTables.bootstrap4.min.js"></script>

    <!-- Page level custom scripts -->
    <script src="Assets/js/demo/datatables-demo.js"></script>
  </body>
</html>
