package com.accolm.licenseManager.DAO;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.accolm.licenseManager.Entities.License;
import com.accolm.licenseManager.Entities.LicenseProperty;
import com.accolm.licenseManager.Utils.LocalEntityManagerFactory;

public class LicenseDAOImpl implements LicenseDAO<License, LicenseProperty> {

	// Logger instance
	Logger logger = LogManager.getLogger(LicenseDAOImpl.class);

	@Override
	public String create(License key) {
		logger.info("Creating a new license...");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();

		try {
			em.getTransaction().begin();
			// sample failover query on Never licenses.
			switch (key.getExpiryDate()) {
			case "": // Never license
				logger.info("NEVER license");
				em.createNamedQuery("createNeverLicenses").setParameter(1, key.getCompanyId())
						.setParameter(2, key.getCreationDate()).setParameter(3, key.getFloatExp())
						.setParameter(4, key.getHardwareLock()).setParameter(5, key.getName())
						.setParameter(6, key.getStartDate()).setParameter(7, key.getStatus())
						.setParameter(8, key.getSignKey().getId()).setParameter(9, key.getLicenseKey()).executeUpdate();
				break;

			default:
				em.merge(key);
				break;
			}

			// em.merge(key); // all licenses
			em.getTransaction().commit();
			logger.info("License created successfully.");
			return "Status: License created successfully";
		} catch (Exception e) {
			logger.error("Error creating license: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}
		return null;
	}

	@Override
	public License getById(int id) {
		logger.info("Retrieving license by ID: {}", id);
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			License license = em.find(License.class, id);
			if (license == null) {
				logger.warn("License with ID {} does not exist.", id);
				license = new License();
				license.setId(id);
				license.setName("License not found.");
			}
			return license;
		} catch (Exception e) {
			logger.error("Error retrieving license by ID: ", e);
		} finally {
			em.close();
		}
		return null;
	}

	@Override
	public String remove(int id) {
		logger.info("Removing license with ID: {}", id);
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			License license = em.find(License.class, id);
			if (license != null) {
				em.remove(license);
				em.getTransaction().commit();
				logger.info("License removed successfully.");
				return "Status: License removed successfully";
			} else {
				logger.warn("License with ID {} not found.", id);
			}
		} catch (Exception e) {
			logger.error("Error removing license: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}
		return "Error: License not found or could not be removed.";
	}

	@Override
	public long countLicenses() {
		logger.info("Counting licenses...");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			Query query = em.createQuery("SELECT COUNT(l) FROM License l");
			return (long) query.getSingleResult();
		} catch (Exception e) {
			logger.error("Error counting licenses: ", e);
		} finally {
			em.close();
		}
		return 0;
	}

	@Override
	public List<License> getAllLicenses() {
		logger.info("Retrieving all licenses...");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			TypedQuery<License> query = em.createQuery("SELECT l FROM License l", License.class);
			return query.getResultList();
		} catch (Exception e) {
			logger.error("Error retrieving all licenses: ", e);
		} finally {
			em.close();
		}
		return new ArrayList<>();
	}

	@Override
	public String updateLicense(License key) {
		logger.info("Updating license with ID: {}", key.getId());
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			License license = em.find(License.class, key.getId());
			if (license != null) {

				license.setCompanyId(key.getCompanyId()); // save the updated companyId
				license.setName(key.getName());
				license.setCreationDate(key.getCreationDate());
				license.setStartDate(key.getStartDate());
				license.setExpiryDate(key.getExpiryDate());
				license.setFloatExp(key.getFloatExp());
				license.setHardwareLock(key.getHardwareLock());
				license.setStatus(key.getStatus());
				license.setProperties(key.getProperties()); // save the properties
				license.setSignKey(key.getSignKey()); // save the signing key

				validateLicense(license); // validate the bean.

				em.merge(license); // save the license to the db.
				em.getTransaction().commit(); // commit the transaction
				logger.info("License updated successfully.");
				return "Status: License updated successfully";
			} else {
				logger.warn("License with ID {} not found.", key.getId());
			}
		} catch (Exception e) {
			logger.error("Error updating license: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}
		return "Error: License not found or could not be updated.";
	}

	private void validateLicense(License license) {
		// validate bean.
		ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
		Validator validator = factory.getValidator();

		// do validation.
		Set<ConstraintViolation<License>> beanViolations = validator.validate(license);

		System.err.println("@@@ violating the bean");
		for (ConstraintViolation<License> violation : beanViolations) {

			System.err.println("propertyName:" + violation.getPropertyPath());
			System.err.println("violation message:" + violation.getMessage());
		}

		if (!beanViolations.isEmpty()) {
			throw new ConstraintViolationException(beanViolations);
		}

	}

	@Override
	public List<License> searchLicenses(String search) {
		logger.info("Searching licenses with term: {}", search);
		EntityManager em = LocalEntityManagerFactory.createEntityManager();

		try {
			// Use LOWER to make the search case-insensitive
			String queryString = "SELECT l FROM License l WHERE LOWER(l.name) LIKE LOWER(:search)";
			logger.info("Executing query: {}", queryString);

			TypedQuery<License> query = em.createQuery(queryString, License.class);

			// Ensure the wildcard characters (%) are included here, not in the query itself
			query.setParameter("search", "%" + search.toLowerCase() + "%");

			List<License> resultList = query.getResultList();
			logger.info("Query returned {} results", resultList.size());

			return resultList;
		} catch (Exception e) {
			logger.error("Error searching licenses: ", e);
		} finally {
			em.close();
		}

		return new ArrayList<>();
	}

// Deprecate
//    @Override
//    public List<License> licensePagination(int page) {
//        logger.info("Paginating licenses for page: {}", page);
//        EntityManager em = LocalEntityManagerFactory.createEntityManager();
//        try {
//            TypedQuery<License> query = em.createQuery("SELECT l FROM License l ORDER BY l.id ASC", License.class);
//            query.setFirstResult(page * 10);
//            query.setMaxResults(10);
//            return query.getResultList();
//        } catch (Exception e) {
//            logger.error("Error paginating licenses: ", e);
//        } finally {
//            em.close();
//        }
//        return new ArrayList<>();
//    }

	// Reuse
	@Override
	public List<License> licensePagination(int page) {
		logger.info("Paginating licenses for page: {}", page);
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			TypedQuery<License> query = em.createQuery("SELECT l FROM License l", License.class);
			// query.setFirstResult(page * 10);
			// query.setMaxResults(10);
			return query.getResultList();
		} catch (Exception e) {
			logger.error("Error paginating licenses: ", e);
		} finally {
			em.close();
		}
		return new ArrayList<>();
	}

	// Create a new LicenseProperty
	public String createProperty(LicenseProperty property) {
		logger.info("Creating a new license property...");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			em.merge(property);
			em.getTransaction().commit();
			logger.info("License property created successfully.");
			return "Status: License property created successfully";
		} catch (Exception e) {
			logger.error("Error creating license property: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}
		return null;
	}

	// Update an existing LicenseProperty
	public String updateProperty(LicenseProperty property) {
		logger.info("Updating license property with ID: {}", property.getId());
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			LicenseProperty existingProperty = em.find(LicenseProperty.class, property.getId());
			if (existingProperty != null) {
				existingProperty.setName(property.getName());
				existingProperty.setValue(property.getValue());
				em.getTransaction().commit();
				logger.info("License property updated successfully.");
				return "Status: License property updated successfully";
			} else {
				logger.warn("License property with ID {} not found.", property.getId());
			}
		} catch (Exception e) {
			logger.error("Error updating license property: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}
		return "Error: License property not found or could not be updated.";
	}

	// Delete a LicenseProperty
	public String deleteProperty(int id) {
		logger.info("Deleting license property with ID: {}", id);
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			LicenseProperty property = em.find(LicenseProperty.class, id);
			if (property != null) {
				em.remove(property);
				em.getTransaction().commit();
				logger.info("License property deleted successfully.");
				return "Status: License property deleted successfully";
			} else {
				logger.warn("License property with ID {} not found.", id);
			}
		} catch (Exception e) {
			logger.error("Error deleting license property: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}
		return "Error: License property not found or could not be deleted.";
	}

	// Retrieve all LicenseProperties
	public List<LicenseProperty> getAllProperties() {
		logger.info("Retrieving all license properties...");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			TypedQuery<LicenseProperty> query = em.createQuery("SELECT p FROM LicenseProperty p",
					LicenseProperty.class);
			return query.getResultList();
		} catch (Exception e) {
			logger.error("Error retrieving license properties: ", e);
		} finally {
			em.close();
		}
		return new ArrayList<>();
	}

	// getLicensekey
	public String getLicenseKey(String licenseKey) {

		logger.info("Retrieving licensekey ...");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			TypedQuery<License> query = em.createQuery("SELECT l FROM License l WHERE l.licenseKey =: licenseKey",
					License.class);

			query.setParameter("licenseKey", licenseKey);

			licenseKey = query.getSingleResult().getLicenseKey();
			;

			return licenseKey;
		} catch (Exception e) {
			logger.error("Error retrieving all licenses: ", e);
		} finally {
			em.close();
		}

		licenseKey = null;

		return licenseKey;

	}
}
