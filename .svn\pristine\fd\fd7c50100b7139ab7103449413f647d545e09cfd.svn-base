var importTemplate = Vue.component("import-template", {
  data() {
    return {
      selectedFile: null,
    };
  },
  methods: {
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file && file.name.endsWith(".tlp")) {
        this.selectedFile = file;
        const label = event.target.nextElementSibling;
        label.textContent = file.name;
      } else if (file) {
        $("#importErrorModal").modal("show");
        this.resetImportField();
      }
    },

    importTemplate() {
      if (!this.selectedFile) return;

      const formData = new FormData();
      formData.append("file", this.selectedFile);

      axios
        .post(`${this.apiBaseUrl}/templates/import`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((response) => {
          $("#importSuccessModal").modal("show");
          this.resetImportField();
          $("#importSuccessModal").on("hidden.bs.modal", () => {
            window.location.href = "templates.html";
          });
        })
        .catch((error) => {
          $("#importErrorModal").modal("show");
          console.error("Import failed:", error);
          this.resetImportField();
        });
    },

    resetImportField() {
      this.selectedFile = null;
      const input = document.getElementById("templateFile");
      const label = input.nextElementSibling;
      input.value = "";
      label.textContent = "Choose template file (.tlp)...";
    },
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },
  },
  template: `
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Import Template</h6>
                        </div>
                        <div class="card-body">
                            <div class="row justify-content-center">
                                <div class="col-lg-6">
                                    <div class="p-5">
                                        <form class="user" @submit.prevent="importTemplate">
                                            <div class="form-group">
                                                <div class="custom-file hover-highlight" style="cursor: pointer;">
                                                    <input type="file" class="custom-file-input" id="templateFile" 
                                                        @change="handleFileSelect" accept=".tlp" required>
                                                    <label class="custom-file-label text-truncate" for="templateFile">
                                                        Choose template file (.tlp)...
                                                    </label>
                                                </div>
                                            </div>
                                            <button type="submit" class="btn btn-primary btn-user btn-block" 
                                                :disabled="!selectedFile">
                                                <i class="fas fa-file-import mr-2"></i>Import Template
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success Modal -->
            <div class="modal fade" id="importSuccessModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-body text-center">
                            <i class="fa fa-check bg-success text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
                            <p class="lead text-success mb-5">
                                <strong>Template Imported Successfully</strong>
                            </p>
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Modal -->
            <div class="modal fade" id="importErrorModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-body text-center">
                            <i class="fa fa-times bg-danger text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
                            <p class="lead text-danger mb-5">
                                <strong>Failed to Import Template</strong>
                            </p>
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .hover-highlight:hover .custom-file-label {
                    background-color: #f8f9fc;
                    border-color: #4e73df;
                }
            </style>
        </div>
    `,
});

new Vue({
  el: "#importtemplate",
  components: {
    "import-template": importTemplate,
  },
  template: `
        <div id="wrapper">
            <!-- Sidebar -->
            <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">
                <a class="sidebar-brand d-flex align-items-center justify-content-center" href="index.html">
                    <div class="sidebar-brand-icon rotate-n-15">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="sidebar-brand-text mx-3">License Manager</div>
                </a>

                <hr class="sidebar-divider my-0">

                <li class="nav-item">
                    <a class="nav-link" href="index.html">
                        <i class="fas fa-fw fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseTwo">
                        <i class="fas fa-fw fa-folder-open"></i>
                        <span>License</span>
                    </a>
                    <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionSidebar">
                        <div class="bg-white py-2 collapse-inner rounded">
                            <a class="collapse-item" href="licenses.html">List License</a>
                            <a class="collapse-item" href="blanklicense.html">New Blank License</a>
                            <a class="collapse-item" href="templatelicense.html">License From Template</a>
                            <a class="collapse-item" href="importlicense.html">Import License</a>
                        </div>
                    </div>
                </li>

                <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseThree"
          >
            <i class="fas fa-fw fa-lock"></i>
            <span>Key Manager</span>
          </a>
          <div
            id="collapseThree"
            class="collapse"
            aria-labelledby="headingThree"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
            <a class="collapse-item" href="listkeys.html"
                >List Signing Keys</a
              >
              <a class="collapse-item " href="keymanager.html"
                >New Signing Key</a
              >
              
              <a class="collapse-item" href="importkey.html">Import Key</a>
            </div>
          </div>
        </li>
                
        <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseOne"
          >
            <i class="fas fa-fw fa-folder"></i>
            <span>Template Manager</span>
          </a>
          <div
            id="collapseOne"
            class="collapse"
            aria-labelledby="headingTwo"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
              <a class="collapse-item" href="templates.html">List Template</a>
              <a class="collapse-item" href="templatemanager.html"
                >New Template</a
              >
              <a class="collapse-item" href="importtemplate.html"
                >Import Template</a
              >
            </div>
          </div>
        </li>
        
         <!-- companies section -->
        <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseFour"
          >
            <i class="fas fa-fw fa-building"></i>
            <span>Companies</span>
          </a>
          <div
            id="collapseFour"
            class="collapse"
            aria-labelledby="headingFour"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
              <a class="collapse-item" href="companies.html">List Companies</a>
              <a class="collapse-item" href="company-new.html">New Company</a>
            </div>
          </div>
        </li>

                <hr class="sidebar-divider d-none d-md-block">

                <div class="text-center d-none d-md-inline">
                    <button class="rounded-circle border-0" id="sidebarToggle"></button>
                </div>
            </ul>

            <!-- Content Wrapper -->
            <div id="content-wrapper" class="d-flex flex-column">
                <div id="content">
                     <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Import Template</h1>
                    </div>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">



                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">User</span>
                                <img class="img-profile rounded-circle" src="Assets/img/undraw_profile.svg">
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profile
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Activity Log
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

                    <!-- Begin Page Content -->
                    <import-template></import-template>
                </div>

                <!-- Footer -->
                <footer class="sticky-footer bg-white">
                    <div class="container my-auto">
                        <div class="copyright text-center my-auto">
                            <span>Copyright &copy; License Manager 2024</span>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
    `,
});
