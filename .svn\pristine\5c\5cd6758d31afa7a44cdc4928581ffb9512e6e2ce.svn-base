package com.accolm.licenseManager.RestInterface;

import java.util.List;

import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.accolm.licenseManager.Entities.Contact;
import com.accolm.licenseManager.Entities.License;
import com.accolm.licenseManager.Entities.User;
import com.accolm.licenseManager.Services.ContactService;
import com.accolm.licenseManager.Services.ContactServiceImpl;
import com.accolm.licenseManager.Services.UserService;
import com.accolm.licenseManager.Services.UserServiceImpl;


@Path("users")
public class UserRI {

	// a contact service layer for separation of concerns.
	private UserService impl = new UserServiceImpl();

	// Endpoint to Create a new user, /api/users
	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response createUser(User user) {
 		User result = impl.createUser(user);
		return Response.status(Response.Status.CREATED).entity(result).build();
	}
	
	
    @GET
    @Path("all")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAllUsers() {
        List<User> users = impl.getAllUsers();
        if (users == null || users.isEmpty()) {
            return Response.status(Response.Status.NO_CONTENT).entity("No users found.").build();
        }
        return Response.ok(users).build();
    }
    
    
	// count the total number of companies in the list
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Path("count")
	public Response getCountOfUsers() {
		long result = impl.getCountOfUsers();
		return Response.status(Response.Status.OK).entity(result).build();
	}
	
	// Delete a license by ID
    @DELETE
    @Path("delete/{userName}")
    @Produces(MediaType.TEXT_PLAIN)
    public Response deleteUser(@PathParam("userName") String userName) {
        String result = impl.deleteUser(userName);
        if ("username passed does not exist.".equals(result)) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
        return Response.ok(result).build();
    }

    // Update a license
    @PUT
    @Path("update")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public Response updateUser(User user) {
        String result = impl.updateUser(user);
        if ("Invalid user data provided.".equals(result)) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
        return Response.ok(result).build();
    }
    


}
