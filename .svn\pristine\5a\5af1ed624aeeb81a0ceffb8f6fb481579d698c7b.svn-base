new Vue({
  el: "#company-details",
  data() {
    return {
      selectedLicense: "",
      selectedProduct: "",
      productVersion: "",
      selectedLicenseType: "",
      extendedSupport: false,
      maintenanceFormData: {
        startDate: "",
        endDate: "",
      },
      editMaintenanceData: {
        id: null,
        companyName: "",
        licenseKey: "",
        product: "",
        productVersion: "",
        licenseType: "",
        startDate: "",
        endDate: "",
        status: "",
        extendedSupport: false,
      },
      editMaintenanceValidationErrors: {},
      maintenanceToEdit: null,
      singleLicense: null,
      oneLicense: null,
      expiryCurrentPage: 1,
      expiryMaxPerPage: 10,
      extendedFilters: {
        licenseKey: "",
        product: "",
        expiryDate: "",
      },
      isFilteringExtended: false,
      filteredExtendedItems: [],
      maintenanceFilters: {
        licenseKey: "",
        product: "",
        expiryDate: "",
      },
      extendedSupport: false,
      isFilteringMaintenance: false,
      filteredMaintenanceItems: [],
      isFetchingMaintenance: false,
      isFetchingExtendedSupport: false,
      currentLicensePage: 1,
      licenseMaxPerPage: 10,
      companyLicenseCount: 0,
      licensePages: 1,
      allCompanyLicenses: [], // Store all licenses
      paginatedCompanyLicenses: [], // Store current page licenses
      contactData: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        mobilePhone: "",
        contactType: "",
      },
      maintenanceFormData: {
        startDate: "",
        endDate: "",
      },
      extendedSupportFormData: {
        companyId: null,
        licenseKey: "",
        product: "",
        productVersion: "",
        licenseType: "",
        startDate: "",
        endDate: "",
        status: "",
      },
      validationErrors: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
      },
      editContactData: {
        id: null,
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        mobilePhone: "",
        contactType: "",
        companyId: 0,
      },
      editValidationErrors: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
      },
      allContacts: [],
      filters: {
        firstName: "",
        company: "",
        email: "",
        phone: "",
      },
      did: 0,
      isFetchingLicenses: false,
      licenseCurrentPage: 1,
      licensePages: 0,
      licenseItemCount: 0,
      licenseMaxPerPage: 10,
      licenseFilters: {
        name: "",
        licenseCode: "",
        expiryDate: "",
      },
      isLicenseFiltering: false,
      filteredCompanyLicenses: [],
      companyLicenses: [],
      contactTypeLabels: {
        primary: "Primary",
        secondary: "Secondary",
        technical: "Technical",
        billing: "Billing",
        PRIMARY: "Primary",
        SECONDARY: "Secondary",
        TECHNICAL: "Technical",
        BILLING: "Billing",
      },
      company: {
        id: null,
        name: "",
        countryId: null,
        email: "",
        telephone: "",
        status: 0,
        address: "",
        city: "",
        state: "",
        postCode: "",
      },
      deleteContactId: null,
      contacts: [],
      activeTab: "contacts",
      activeContactTab: "list",
      activeLicenseTab: "list",
      activeMaintenanceTab: "list",
      activeExtendedTab: "list",
      licenses: [],
      isFetching: false,
      currentPage: 1,
      maxPerPage: 10,
      itemCount: 0,
      pages: 1,
      expirationEditable: true,
      licenseProperties: [],
      templates: [],
      companies: [],
      pickKey: [],
      selectedTemplate: null,
      templateFields: [],
      formValues: {},
      formData: {
        company: null,
        templateId: null,
        licenseName: "",
        startDate: "",
        expirationDate: "",
        signingKey: "",
        hardwareLocking: "",
      },
      oneLicense: {
        id: "",
        name: "",
        status: "Valid",
        creationDate: "",
        startDate: "",
        expiryDate: "",
        floatExp: "",
        hardwareLock: "",
        properties: [{}],
        signKey: [{}],
      },
      singleLicense: {
        id: "",
        name: "",
        status: "Valid",
        creationDate: "",
        startDate: "",
        expiryDate: "",
        floatExp: "",
        hardwareLock: "",
        properties: [],
        signKey: {
          id: "",
          name: "",
        },
      },
      maintenanceToDelete: null,
      editFormData: {},
      editFormData: {
        id: null,
        name: "",
        status: "",
        creationDate: "",
        startDate: "",
        expiryDate: "",
        floatExp: "",
        hardwareLock: false,
        properties: [],
        signKey: {
          id: 0,
          name: "",
        },
        companyId: 0,
      },
      deleteLicenseId: null,
      // Add maintenance and extended support data
      maintenanceItems: [],
      extendedSupportItems: [],
      activeMaintenanceTab: "list",
      activeExtendedTab: "list",
    };
  },

  mounted() {
    this.fetchCompanyData().then(() => {
      // Fetch all data after company data is loaded
      this.fetchContacts();
      this.fetchCompanyLicensesPaginated();
      this.fetchTemplates();
      this.fetchSigningKeys();

      // Fetch maintenance and extended support data on page load
      this.fetchMaintenanceData();
      this.fetchExtendedSupportData();
    });

    $("#editMaintenanceModal").on("hidden.bs.modal", () => {
      this.onEditMaintenanceModalClose();
    });

    $("#deleteMaintenanceModal").on("hidden.bs.modal", () => {
      this.maintenanceToDelete = null;
    });

    this.$nextTick(() => {
      if (this.company && this.company.id) {
        this.formData.company = this.company.id;
      }
    });
  },

  methods: {
    onProductSelected() {
      // This method should be called when user selects a product from dropdown
      this.maintenanceFormData.product = this.selectedProduct;
      console.log("Product selected:", this.selectedProduct);
    },

    onProductVersionSelected() {
      // This method should be called when user selects a product version from dropdown
      this.maintenanceFormData.productVersion = this.productVersion;
      console.log("Product version selected:", this.productVersion);
    },

    onLicenseTypeSelected() {
      // This method should be called when user selects a license type from dropdown
      this.maintenanceFormData.licenseType = this.selectedLicenseType;
      console.log("License type selected:", this.selectedLicenseType);
    },

    deleteMaintenanceModal(maintenanceId) {
      // Find the maintenance item to delete
      const item = this.maintenanceItems.find((m) => m.id === maintenanceId);
      if (!item) {
        console.error("Maintenance item not found");
        return;
      }

      // Store reference to the item being deleted
      this.maintenanceToDelete = item;

      // Show the delete confirmation modal
      $("#deleteMaintenanceModal").modal("show");
    },

    // Delete maintenance item
    deleteMaintenanceItem() {
      if (!this.maintenanceToDelete) {
        console.error("No maintenance item selected for deletion");
        return;
      }

      const maintenanceId = this.maintenanceToDelete.id;

      // Make API call to delete the maintenance item
      fetch(`${this.apiBaseUrl}/maintenance-supports/${maintenanceId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(
              `Failed to delete maintenance item: ${response.status} ${response.statusText}`
            );
          }

          // Remove the item from the local array
          const index = this.maintenanceItems.findIndex(
            (item) => item.id === maintenanceId
          );
          if (index !== -1) {
            this.maintenanceItems.splice(index, 1);
          }

          // Hide the delete confirmation modal
          $("#deleteMaintenanceModal").modal("hide");

          // Show success modal
          $("#maintenanceDeleteSuccessModal").modal("show");
          this.autoHideSuccessModal("maintenanceDeleteSuccessModal");

          // Clear the reference
          this.maintenanceToDelete = null;

          // Refresh the maintenance data to ensure consistency
          this.fetchMaintenanceItems();
        })
        .catch((error) => {
          console.error("Error deleting maintenance item:", error);
          $("#maintenanceDeleteErrorModal").modal("show");
        });
    },

    // Cancel delete operation
    cancelDeleteMaintenance() {
      this.maintenanceToDelete = null;
      $("#deleteMaintenanceModal").modal("hide");
    },

    async fetchCompanyLicensesForDropdown() {
      if (!this.company.id) {
        console.error("No company ID available");
        return;
      }

      try {
        const response = await axios.get(
          `${this.apiBaseUrl}/licenses/company/${this.company.id}`
        );

        if (response.status === 200) {
          // Filter to only include licenses that already have license keys
          this.companyLicenses = (response.data || []).filter(
            (license) => license.licenseKey && license.licenseKey !== ""
          );

          console.log(
            `Loaded ${this.companyLicenses.length} licenses with existing keys for company: ${this.company.name}`
          );

          // Log the license keys for debugging
          console.log(
            "Available license keys:",
            this.companyLicenses.map((l) => l.licenseKey)
          );
        }
      } catch (error) {
        console.error("Error fetching company licenses for dropdown:", error);
        this.companyLicenses = [];
      }
    },

    setActiveMaintenanceTab(tab) {
      this.activeMaintenanceTab = tab;
      if (tab === "add") {
        // Load company licenses for the dropdown
        // this.fetchCompanyLicensesForDropdown(); // Make sure this method exists and is called
        this.resetMaintenanceForm();
      } else if (tab === "list") {
        this.fetchMaintenanceData();
      } else if (tab === "expiry") {
        if (!this.maintenanceItems || this.maintenanceItems.length === 0) {
          //this.fetchMaintenanceData();
        }
      }
    },

    setActiveExtendedTab(tab) {
      this.activeExtendedTab = tab;
      if (tab === "add") {
        // Load company licenses for the dropdown
        this.fetchCompanyLicenses(this.company.id);
        this.resetExtendedSupportForm();
      } else if (tab === "list") {
        this.fetchExtendedSupportData();
      }
    },

    // Expiry page size change handler
    onExpiryPageSizeChange(event) {
      const numberToGet = parseInt(event.target.value);
      this.expiryMaxPerPage = numberToGet;
      this.expiryCurrentPage = 1; // Reset to first page when changing page size
    },

    // Unified status class method
    getStatusClass(item) {
      // If the API provides a status, use it directly
      if (item.status) {
        switch (item.status.toUpperCase()) {
          case "EXPIRED":
            return "text-danger"; // Red text for expired
          case "ACTIVE":
            return "text-success"; // Green text for active
          case "EXPIRING SOON":
          case "EXPIRING_SOON":
            return "text-warning"; // Orange text for expiring soon
          default:
            return "text-muted"; // Gray text for unknown status
        }
      }

      // Fallback: calculate status based on end date if no status provided
      if (
        !item.endDate ||
        item.endDate === "Never" ||
        item.endDate === "null" ||
        item.endDate === ""
      ) {
        return "text-success"; // Green text for never expires
      }

      try {
        const endDate = new Date(item.endDate);
        const today = new Date();

        if (endDate < today) {
          return "text-danger"; // Red text for expired
        } else {
          const timeDiff = endDate - today;
          const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));

          if (daysRemaining <= 30) {
            return "text-warning"; // Orange text for expiring soon
          } else {
            return "text-success"; // Green text for active
          }
        }
      } catch (error) {
        console.error("Error calculating status:", error);
        return "text-muted";
      }
    },

    // Unified status text method
    getStatusText(item) {
      // If the API provides a status, use it directly
      if (item.status) {
        return item.status;
      }

      // Fallback: calculate status based on end date if no status provided
      if (
        !item.endDate ||
        item.endDate === "Never" ||
        item.endDate === "null" ||
        item.endDate === ""
      ) {
        return "Active";
      }

      try {
        const endDate = new Date(item.endDate);
        const today = new Date();

        // Normalize to start of day
        endDate.setHours(0, 0, 0, 0);
        today.setHours(0, 0, 0, 0);

        if (endDate < today) {
          return "Expired";
        } else {
          const timeDiff = endDate - today;
          const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));

          if (daysRemaining <= 30) {
            return "Expiring Soon";
          } else {
            return "Active";
          }
        }
      } catch (error) {
        console.error("Error calculating status for item:", item, error);
        return "Unknown";
      }
    },

    applyMaintenanceFilters() {
      this.isFilteringMaintenance = true;
      let filtered = [...this.maintenanceItems];

      // Filter by license key
      if (this.maintenanceFilters.licenseKey) {
        const keySearch = this.maintenanceFilters.licenseKey.toLowerCase();
        filtered = filtered.filter(
          (item) =>
            item.licenseKey && item.licenseKey.toLowerCase().includes(keySearch)
        );
      }

      // Filter by product
      if (this.maintenanceFilters.product) {
        const productSearch = this.maintenanceFilters.product.toLowerCase();
        filtered = filtered.filter(
          (item) =>
            item.product && item.product.toLowerCase().includes(productSearch)
        );
      }

      // Filter by expiry date
      if (this.maintenanceFilters.expiryDate) {
        const filterDate = new Date(this.maintenanceFilters.expiryDate);
        filterDate.setHours(23, 59, 59, 999);
        filtered = filtered.filter((item) => {
          if (item.endDate === "Never" || !item.endDate) {
            return false;
          }
          const expiryDate = new Date(item.endDate);
          return expiryDate <= filterDate;
        });
      }

      this.filteredMaintenanceItems = filtered;
    },

    applyExtendedFilters() {
      this.isFilteringExtended = true;
      let filtered = [...this.extendedSupportItems];

      // Filter by license key
      if (this.extendedFilters.licenseKey) {
        const keySearch = this.extendedFilters.licenseKey.toLowerCase();
        filtered = filtered.filter(
          (item) =>
            item.licenseKey && item.licenseKey.toLowerCase().includes(keySearch)
        );
      }

      // Filter by product
      if (this.extendedFilters.product) {
        const productSearch = this.extendedFilters.product.toLowerCase();
        filtered = filtered.filter(
          (item) =>
            item.product && item.product.toLowerCase().includes(productSearch)
        );
      }

      // Filter by expiry date
      if (this.extendedFilters.expiryDate) {
        const filterDate = new Date(this.extendedFilters.expiryDate);
        filterDate.setHours(23, 59, 59, 999);
        filtered = filtered.filter((item) => {
          if (item.endDate === "Never" || !item.endDate) {
            return false;
          }
          const expiryDate = new Date(item.endDate);
          return expiryDate <= filterDate;
        });
      }

      this.filteredExtendedItems = filtered;
    },

    clearMaintenanceFilters() {
      this.maintenanceFilters = {
        licenseKey: "",
        product: "",
        expiryDate: "",
      };
      this.isFilteringMaintenance = false;
      this.fetchMaintenanceData();
    },

    clearExtendedFilters() {
      this.extendedFilters = {
        licenseKey: "",
        product: "",
        expiryDate: "",
      };
      this.isFilteringExtended = false;
      this.fetchExtendedSupportData();
    },

    applyLicenseFilters() {
      console.log("Applying license filters:", this.licenseFilters);
      this.isLicenseFiltering = true;
      this.licenseCurrentPage = 1;

      let filtered = [...this.companyLicenses];

      // Filter by license name (not company name)
      if (this.licenseFilters.name) {
        const nameSearch = this.licenseFilters.name.toLowerCase();
        filtered = filtered.filter((license) => {
          return (
            license.name && license.name.toLowerCase().includes(nameSearch)
          );
        });
      }

      if (this.licenseFilters.licenseCode) {
        const codeSearch = this.licenseFilters.licenseCode.toLowerCase();
        filtered = filtered.filter((license) => {
          return (
            license.licenseKey &&
            license.licenseKey.toLowerCase().includes(codeSearch)
          );
        });
      }

      if (this.licenseFilters.expiryDate) {
        const filterDate = new Date(this.licenseFilters.expiryDate);
        filterDate.setHours(23, 59, 59, 999);

        filtered = filtered.filter((license) => {
          if (license.expiryDate === "Never" || !license.expiryDate) {
            return false;
          }
          const expiryDate = new Date(license.expiryDate);
          return expiryDate <= filterDate;
        });
      }

      this.filteredCompanyLicenses = filtered;
      this.licenseItemCount = filtered.length;
      this.licensePages = Math.ceil(
        this.licenseItemCount / this.licenseMaxPerPage
      );
    },

    clearLicenseFilters() {
      console.log("Clearing license filters");
      this.licenseFilters = {
        name: "",
        licenseCode: "",
        expiryDate: "",
      };
      this.isLicenseFiltering = false;
      this.licenseCurrentPage = 1;
      this.licenseItemCount = this.companyLicenses.length;
      this.licensePages = Math.ceil(
        this.licenseItemCount / this.licenseMaxPerPage
      );
    },

    // Unified license page size change handler
    onLicensePageSizeChange(event) {
      this.licenseMaxPerPage = parseInt(event.target.value);
      this.licenseCurrentPage = 1;

      // Handle both filtered and unfiltered scenarios
      const totalItems = this.isLicenseFiltering
        ? this.filteredCompanyLicenses.length
        : this.companyLicenses.length;

      this.licensePages = Math.ceil(totalItems / this.licenseMaxPerPage);

      // Update pagination if using client-side pagination
      if (this.updateLicensePagination) {
        this.updateLicensePagination();
      }
    },

    // Main data display method
    async displayCompanyLicenses() {
      this.isFetchingLicenses = true;
      try {
        // Use company-specific endpoint if available
        const response = await axios.get(
          `${this.apiBaseUrl}/licenses/company/${this.company.id}`
        );

        if (response.status === 200) {
          const companyLicenses = response.data;

          // Process each license
          for (const license of companyLicenses) {
            // Handle empty expiration dates as "Never"
            if (
              !license.expiryDate ||
              license.expiryDate === "" ||
              license.expiryDate === "null"
            ) {
              license.expiryDate = "Never";
            }

            // Calculate validation status
            license.validationStatus = this.isLicenseValid(license);

            // Set company name
            license.companyName = this.company.name;

            // Set license key to "N/A" if missing instead of generating
            if (!license.licenseKey || license.licenseKey === "") {
              license.licenseKey = "N/A";
            }
          }

          this.companyLicenses = companyLicenses;
          this.licenseItemCount = companyLicenses.length;
          this.licensePages = Math.ceil(
            this.licenseItemCount / this.licenseMaxPerPage
          );

          console.log(
            `Loaded ${companyLicenses.length} licenses for company: ${this.company.name}`
          );
        }
      } catch (error) {
        console.error("Error fetching company licenses:", error);
        // Fallback to filtering all licenses
        this.displayCompanyLicensesFromAll();
      } finally {
        this.isFetchingLicenses = false;
      }
    },

    // Fallback method
    async displayCompanyLicensesFromAll() {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/licenses/all`);
        if (response.status === 200) {
          const allLicenses = response.data;
          const companyLicenses = allLicenses.filter(
            (license) => license.companyId === this.company.id
          );

          // Process licenses as above...
          // (same processing logic as in the main method)
        }
      } catch (error) {
        console.error("Error in fallback method:", error);
        this.companyLicenses = [];
        this.licenseItemCount = 0;
        this.licensePages = 0;
      }
    },

    // License validation method
    isLicenseValid(license) {
      if (
        !license.expiryDate ||
        license.expiryDate === "Never" ||
        license.expiryDate === "null" ||
        license.expiryDate === ""
      ) {
        return true; // No expiry date means it's valid
      }

      try {
        const expiryDate = new Date(license.expiryDate);
        const today = new Date();

        // Set both dates to start of day for accurate comparison
        expiryDate.setHours(0, 0, 0, 0);
        today.setHours(0, 0, 0, 0);

        return expiryDate >= today;
      } catch (e) {
        console.error("Error validating license date:", e);
        return false;
      }
    },

    // Date formatting method
    formatDate(dateString) {
      if (
        !dateString ||
        dateString === "" ||
        dateString === "Never" ||
        dateString === "null"
      ) {
        return "Never";
      }
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return "Never";
        }
        return date.toISOString().split("T")[0];
      } catch (e) {
        return "Never";
      }
    },

    // License action methods
    displaySingleLicense(id) {
      axios
        .get(`${this.apiBaseUrl}/licenses/get/${id}`)
        .then((response) => {
          this.oneLicense = response.data;
          this.singleLicense = response.data;
          if (!this.oneLicense.signKey) {
            this.oneLicense.signKey = { id: "", name: "" };
          }
        })
        .catch((error) => {
          console.error("Error fetching license details:", error);
          this.oneLicense = {
            id: "",
            name: "",
            status: "Valid",
            creationDate: "",
            startDate: "",
            expiryDate: "",
            floatExp: "",
            hardwareLock: "",
            properties: [],
            signKey: { id: "", name: "" },
          };
        });
    },

    displayDownloadLicense(id) {
      axios
        .get(`${this.apiBaseUrl}/licenses/download/${id}`, {
          responseType: "blob",
        })
        .then((response) => {
          if (response.status == 200) {
            const fileURL = window.URL.createObjectURL(response.data);
            const a = document.createElement("a");
            a.href = fileURL;
            const fileName =
              this.companyLicenses.find((license) => license.id === id)?.name +
              ".lic";
            a.setAttribute("download", fileName);
            document.body.appendChild(a);
            a.click();
            $("#downloadSuccessModal").modal("show");
            setTimeout(() => {
              $("#downloadSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch(() => {
          $("#licenseErrorModal").modal("show");
          setTimeout(() => {
            $("#licenseErrorModal").modal("hide");
          }, 2000);
        });
    },

    deleteLicenseModal(id) {
      this.did = id;
    },

    fetchCompanyLicenses(companyId) {
      if (!companyId) {
        this.companyLicenses = [];
        return;
      }

      axios
        .get(`${this.apiBaseUrl}/licenses/company/${companyId}`)
        .then((response) => {
          this.companyLicenses = response.data || [];

          // Log license data to verify
          console.log("Fetched company licenses:", this.companyLicenses);

          // Reset selected license when company changes (if applicable)
          this.selectedLicense = "";
        })
        .catch((error) => {
          console.error("Error fetching company licenses:", error);
          this.companyLicenses = [];
        });
    },

    // Unified paginated fetch method

    updateLicensePagination() {
      const startIndex = (this.currentLicensePage - 1) * this.licenseMaxPerPage;
      const endIndex = startIndex + this.licenseMaxPerPage;
      this.paginatedCompanyLicenses = this.allCompanyLicenses.slice(
        startIndex,
        endIndex
      );
    },

    getLicenseStatusClass(license) {
      const today = new Date();
      const startDate = new Date(license.startDate);
      const expiryDate = license.expiryDate
        ? new Date(license.expiryDate)
        : null;

      // Check if license is active based on dates
      if (startDate > today) {
        return "text-info"; // Not yet active
      } else if (expiryDate && expiryDate < today) {
        return "text-danger"; // Expired
      } else if (expiryDate) {
        const daysUntilExpiry = Math.ceil(
          (expiryDate - today) / (1000 * 60 * 60 * 24)
        );
        if (daysUntilExpiry <= 30) {
          return "text-warning"; // Expiring soon
        }
      }

      return "text-success"; // Active
    },

    getLicenseStatusText(license) {
      const today = new Date();
      const startDate = new Date(license.startDate);
      const expiryDate = license.expiryDate
        ? new Date(license.expiryDate)
        : null;

      // Check if license is active based on dates
      if (startDate > today) {
        const daysUntilStart = Math.ceil(
          (startDate - today) / (1000 * 60 * 60 * 24)
        );
        return `Starts in ${daysUntilStart} days`;
      } else if (expiryDate && expiryDate < today) {
        const daysExpired = Math.ceil(
          (today - expiryDate) / (1000 * 60 * 60 * 24)
        );
        return `Expired ${daysExpired} days ago`;
      } else if (expiryDate) {
        const daysUntilExpiry = Math.ceil(
          (expiryDate - today) / (1000 * 60 * 60 * 24)
        );
        if (daysUntilExpiry <= 30) {
          return `Expires in ${daysUntilExpiry} days`;
        } else {
          return "Active";
        }
      } else {
        return "Active (No Expiry)";
      }
    },

    // Update the existing fetchLicenses method to work with company-specific licenses
    async fetchLicenses() {
      await this.fetchCompanyLicensesPaginated();
    },

    // Update displayData method for licenses
    displayData() {
      this.fetchCompanyLicensesPaginated();
    },

    submitMaintenance() {
      // Validate required fields
      if (
        !this.selectedLicense ||
        !this.selectedProduct ||
        !this.productVersion ||
        !this.selectedLicenseType ||
        !this.maintenanceFormData.startDate ||
        !this.maintenanceFormData.endDate
      ) {
        alert("Please fill in all required fields");
        return;
      }

      // Get the selected license object to extract licenseKey
      const selectedLicenseObj = this.companyLicenses.find(
        (license) => license.id == this.selectedLicense
      );

      // Construct the payload matching your expected format
      const payload = {
        companyId: this.company.id,
        endDate: this.maintenanceFormData.endDate,
        extendedSupport: this.extendedSupport ? 1 : 0,
        licenseId: parseInt(this.selectedLicense),
        licenseKey: selectedLicenseObj ? selectedLicenseObj.licenseKey : "",
        licenseType: this.selectedLicenseType,
        product: this.selectedProduct,
        productVersion: this.productVersion,
        startDate: this.maintenanceFormData.startDate,
        status: "ACTIVE",
      };

      // Send the request
      fetch(`${this.apiBaseUrl}/maintenance-supports`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.text();
        })
        .then((data) => {
          console.log("Success:", data);

          // Set up the modal event listener before showing the modal
          $("#maintenanceSuccessModal")
            .off("hidden.bs.modal")
            .on("hidden.bs.modal", () => {
              // Switch to list tab and fetch data
              this.setActiveMaintenanceTab("list");
              //this.fetchMaintenanceItems();
            });

          $("#maintenanceSuccessModal").modal("show");
          this.resetMaintenanceForm();
        })
        .catch((error) => {
          console.error("Error:", error);
          $("#maintenanceErrorModal").modal("show");
        });
    },

    // Add a method to reset the form after successful submission
    resetMaintenanceForm() {
      this.selectedLicense = "";
      this.selectedProduct = "";
      this.productVersion = "";
      this.selectedLicenseType = "";
      this.maintenanceFormData.startDate = "";
      this.maintenanceFormData.endDate = "";
      this.extendedSupport = false;
    },

    // Add method to submit extended support
    async submitExtendedSupport() {
      try {
        const extendedSupportData = {
          ...this.extendedSupportFormData,
          companyId: this.company.id,
          extendedSupport: 1, // Always 1 for extended support
        };

        const response = await axios.post(
          `${this.apiBaseUrl}/maintenance-supports`,
          JSON.stringify(extendedSupportData),
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.status === 200 || response.status === 201) {
          this.activeExtendedTab = "list";
          this.fetchExtendedSupportData();
          this.resetExtendedSupportForm();
          $("#extendedSupportSuccessModal").modal("show");
          setTimeout(() => {
            $("#extendedSupportSuccessModal").modal("hide");
          }, 2000);
        }
      } catch (error) {
        console.error("Error creating extended support:", error);
        $("#extendedSupportErrorModal").modal("show");
        setTimeout(() => {
          $("#extendedSupportErrorModal").modal("hide");
        }, 2000);
      }
    },

    resetMaintenanceForm() {
      this.selectedLicense = "";
      this.selectedProduct = "";
      this.productVersion = "";
      this.selectedLicenseType = "";
      this.extendedSupport = false;
      this.maintenanceFormData = {
        startDate: "",
        endDate: "",
      };
    },

    // Add method to reset extended support form
    resetExtendedSupportForm() {
      this.extendedSupportFormData = {
        companyId: this.company.id,
        licenseKey: "",
        product: "",
        productVersion: "",
        licenseType: "",
        startDate: "",
        endDate: "",
        status: "",
      };
    },

    calculateStatus(endDate) {
      if (!endDate || endDate === "Never") {
        return "Active";
      }

      try {
        const today = new Date();
        const expiry = new Date(endDate);
        today.setHours(0, 0, 0, 0);
        expiry.setHours(0, 0, 0, 0);

        return expiry >= today ? "Active" : "Expired";
      } catch (e) {
        return "Unknown";
      }
    },

    async submitMaintenanceSupport() {
      try {
        // Find the selected license object
        const selectedLicenseObj = this.companyLicenses.find(
          (lic) => lic.id === parseInt(this.selectedLicense)
        );

        // Validate required fields first
        if (!this.selectedLicense) {
          alert("Please select a license");
          return;
        }
        if (!this.selectedProduct) {
          alert("Please select a product");
          return;
        }
        if (!this.productVersion) {
          alert("Please select a product version");
          return;
        }
        if (!this.selectedLicenseType) {
          alert("Please select a license type");
          return;
        }
        if (
          !this.maintenanceFormData.startDate ||
          !this.maintenanceFormData.endDate
        ) {
          alert("Please select start and end dates");
          return;
        }

        // Format the data with all required fields
        const maintenanceData = {
          companyId: parseInt(this.company.id),
          licenseId: parseInt(this.selectedLicense),
          licenseKey: selectedLicenseObj
            ? selectedLicenseObj.licenseKey || ""
            : "",
          product: this.selectedProduct, // This should be the actual product name, not "Fixed"
          productVersion: this.productVersion,
          licenseType: this.selectedLicenseType,
          startDate: this.maintenanceFormData.startDate,
          endDate: this.maintenanceFormData.endDate,
          status: "ACTIVE",
          extendedSupport: this.extendedSupport ? 1 : 0,
        };

        console.log("Sending maintenance data:", maintenanceData);

        // Ensure all modals are hidden
        $(".modal").modal("hide");

        const response = await axios.post(
          `${this.apiBaseUrl}/maintenance-supports`,
          maintenanceData,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.status === 200 || response.status === 201) {
          console.log("Success response:", response);
          this.resetMaintenanceForm();
          $("#createMaintenanceSuccessModal").modal({
            backdrop: "static",
            keyboard: false,
          });

          setTimeout(() => {
            $("#createMaintenanceSuccessModal").modal("hide");
            this.activeMaintenanceTab = "list";
            this.fetchMaintenanceData();
          }, 1500);
        }
      } catch (error) {
        console.error("Error adding maintenance record:", error);
        console.error(
          "Error details:",
          error.response ? error.response.data : "No response data"
        );
        $("#errorMaintenanceModal").modal({
          backdrop: "static",
          keyboard: false,
        });
      }
    },

    async fetchMaintenanceData() {
      this.isFetchingMaintenance = true;
      try {
        const response = await axios.get(
          `${this.apiBaseUrl}/maintenance-supports`
        );
        if (response.status === 200) {
          const allMaintenanceItems = response.data;

          // Filter maintenance items for this specific company
          this.maintenanceItems = allMaintenanceItems.filter(
            (item) => item.companyId === this.company.id
          );

          // Process each maintenance item
          for (const item of this.maintenanceItems) {
            // Handle empty expiration dates as "Never"
            if (
              !item.endDate ||
              item.endDate === "" ||
              item.endDate === "null"
            ) {
              item.endDate = "Never";
            }

            // Keep existing status from backend - don't modify it
            item.companyName = this.company.name;
          }

          // Reset filtered items if not actively filtering
          if (!this.isFilteringMaintenance) {
            this.filteredMaintenanceItems = [...this.maintenanceItems];
          } else {
            this.applyMaintenanceFilters();
          }

          console.log(
            `Found ${this.maintenanceItems.length} maintenance items for company: ${this.company.name}`
          );
        }
      } catch (error) {
        console.error("Error fetching maintenance data:", error);
        this.maintenanceItems = [];
      } finally {
        this.isFetchingMaintenance = false;
      }
    },

    async fetchExtendedSupportData() {
      this.isFetchingExtendedSupport = true;
      try {
        const response = await axios.get(
          `${this.apiBaseUrl}/maintenance-supports`
        );
        if (response.status === 200) {
          const allMaintenanceItems = response.data;

          // Filter for extended support items for this specific company
          this.extendedSupportItems = allMaintenanceItems.filter(
            (item) =>
              item.companyId === this.company.id && item.extendedSupport === 1
          );

          // Process each extended support item
          for (const item of this.extendedSupportItems) {
            if (
              !item.endDate ||
              item.endDate === "" ||
              item.endDate === "null"
            ) {
              item.endDate = "Never";
            }

            // Keep existing status from backend - don't modify it
            item.companyName = this.company.name;
          }

          // Reset filtered items if not actively filtering
          if (!this.isFilteringExtended) {
            this.filteredExtendedItems = [...this.extendedSupportItems];
          } else {
            this.applyExtendedFilters();
          }

          console.log(
            `Found ${this.extendedSupportItems.length} extended support items for company: ${this.company.name}`
          );
        }
      } catch (error) {
        console.error("Error fetching extended support data:", error);
        this.extendedSupportItems = [];
      } finally {
        this.isFetchingExtendedSupport = false;
      }
    },

    setActiveExtendedTab(tab) {
      this.activeExtendedTab = tab;
      if (tab === "add") {
        this.fetchCompanyLicenses();
        this.resetExtendedSupportForm();
      } else if (tab === "list") {
        this.fetchExtendedSupportData();
      }
    },

    // License editing methods
    editLicense(licenseId) {
      axios
        .get(`${this.apiBaseUrl}/licenses/get/${licenseId}`)
        .then((response) => {
          if (response.status === 200) {
            this.editFormData = { ...response.data };
            if (!this.editFormData.signKey) {
              this.editFormData.signKey = { id: "", name: "" };
            }
            $("#editLicenseModal").modal("show");
          }
        })
        .catch((error) => {
          console.error("Error fetching license details for editing:", error);
          $("#licenseErrorModal").modal("show");
        });
    },

    addEditProperty() {
      if (!this.editFormData.properties) {
        this.editFormData.properties = [];
      }
      this.editFormData.properties.push({
        name: "",
        value: "",
      });
    },

    updateLicense() {
      if (
        !this.editFormData.name ||
        !this.editFormData.startDate ||
        !this.editFormData.expiryDate
      ) {
        alert("Please fill in all required fields");
        return;
      }

      axios
        .put(`${this.apiBaseUrl}/licenses/edit`, this.editFormData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          if (response.status === 200) {
            $("#editLicenseModal").modal("hide");
            $("#editLicenseSuccess").modal("show");
            this.fetchLicenses();
            setTimeout(() => {
              $("#editLicenseSuccess").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error updating license:", error);
          $("#editLicenseModal").modal("hide");
          $("#licenseErrorModal").modal("show");
        });
    },

    removeEditProperty(index) {
      if (
        this.editFormData.properties &&
        this.editFormData.properties.length > 0
      ) {
        this.editFormData.properties.splice(index, 1);
      }
    },

    initializeFormValues() {
      this.formValues = {};
      if (this.templateFields) {
        this.templateFields.forEach((field) => {
          this.formValues[field.name] = "";
        });
      }
    },

    async fetchTemplates() {
      console.log("Starting to fetch templates...");
      console.log("API Base URL:", this.apiBaseUrl);

      try {
        const response = await axios.get(
          `${this.apiBaseUrl}/templates/pagination/1`
        );
        console.log("Template response:", response);
        console.log("Template response status:", response.status);
        console.log("Template response data:", response.data);

        if (response.status === 200 && response.data) {
          this.templates = response.data;
          console.log("Templates set to:", this.templates);
          console.log("Templates length:", this.templates.length);
        } else {
          console.warn("No template data received");
          this.templates = [];
        }
      } catch (error) {
        console.error("Error fetching templates:", error);
        console.error("Error details:", error.response);
        this.templates = [];
      }
    },

    async fetchSigningKeys() {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/keys`);
        this.pickKey = response.data;
      } catch (error) {
        console.error("Error fetching signing keys:", error);
      }
    },

    async onTemplateSelect(event) {
      const templateId = event.target.value;
      if (templateId) {
        try {
          const response = await axios.get(
            `${this.apiBaseUrl}/templates/get/${templateId}`
          );
          this.selectedTemplate = response.data;
          this.templateFields = response.data.fields || [];
          this.initializeFormValues();
          this.licenseProperties = [];

          let templateExpiryType = "never";
          const hasFixedExpiry =
            this.selectedTemplate.licenseExpiry &&
            this.selectedTemplate.licenseExpiry !== "Never" &&
            this.selectedTemplate.licenseExpiry !== " " &&
            this.selectedTemplate.licenseExpiry.trim() !== "";
          const hasFloatingExpiry =
            this.selectedTemplate.floatExpiry &&
            parseInt(this.selectedTemplate.floatExpiry) > 0;

          if (hasFixedExpiry) {
            templateExpiryType = "fixed";
          } else if (hasFloatingExpiry) {
            templateExpiryType = "floating";
          }

          this.expirationEditable = templateExpiryType !== "never";

          if (templateExpiryType === "never") {
            this.formData.expirationDate = "";
            this.formData.floatingDays = "";
          } else if (templateExpiryType === "fixed") {
            this.formData.expirationDate = "";
            this.formData.floatingDays = "";
          } else if (templateExpiryType === "floating") {
            this.formData.floatingDays = this.selectedTemplate.floatExpiry;
            const today = new Date();
            const expiryDate = new Date(today);
            expiryDate.setDate(
              today.getDate() + parseInt(this.selectedTemplate.floatExpiry)
            );
            this.formData.expirationDate = expiryDate
              .toISOString()
              .split("T")[0];
          }

          if (
            this.selectedTemplate.templateProperties &&
            this.selectedTemplate.templateProperties.length > 0
          ) {
            this.licenseProperties =
              this.selectedTemplate.templateProperties.map((prop) => ({
                name: prop.name || "",
                value: prop.value || "",
              }));
          }
        } catch (error) {
          console.error("Error fetching template details:", error);
        }
      }
    },

    determineInputType(propertyType) {
      switch (propertyType.toLowerCase()) {
        case "number":
          return "number";
        case "date":
          return "date";
        case "boolean":
          return "checkbox";
        default:
          return "text";
      }
    },

    handleDisabledExpiryClick() {
      $("#expiryWarningModal").modal({
        backdrop: "static",
        keyboard: false,
      });
      $("#expiryWarningModal").modal("show");
    },

    addLicenseProperty() {
      if (!this.licenseProperties) {
        this.licenseProperties = [];
      }
      this.licenseProperties.push({
        name: "",
        value: "",
      });
    },

    removeLicenseProperty(index) {
      if (this.licenseProperties && this.licenseProperties.length > 0) {
        this.licenseProperties.splice(index, 1);
      }
    },

    async submitLicense() {
      try {
        const customProperties = this.licenseProperties.map((prop) => ({
          name: prop.name,
          value: prop.value,
        }));

        const currentDate = new Date().toISOString().split("T")[0];
        let companyId = this.company.id;
        let companyName = this.company.name || "Unknown";

        const selectedTemplate = this.templates.find(
          (template) => template.id === parseInt(this.formData.templateId)
        );
        const templateName = selectedTemplate
          ? selectedTemplate.name
          : "Unknown";

        const randomSuffix = Math.floor(Math.random() * 10000)
          .toString()
          .padStart(4, "0");
        const formattedDate = currentDate.replace(/-/g, "");
        const generatedName = `${companyName}-${templateName}-${formattedDate}-${randomSuffix}`;

        // Handle expiration date
        let expiryDate = null;

        if (
          this.formData.expirationDate &&
          this.formData.expirationDate.trim() !== ""
        ) {
          expiryDate = this.formData.expirationDate.trim();

          // Validate the date format
          const testDate = new Date(expiryDate);
          if (isNaN(testDate.getTime())) {
            alert(
              "Invalid expiration date format. Please select a valid date."
            );
            return;
          }
        } else if (!this.expirationEditable) {
          expiryDate = null;
        } else {
          expiryDate = null;
        }

        const licenseData = {
          companyId: companyId,
          name: generatedName,
          status: "ACTIVE",
          creationDate: currentDate,
          startDate: currentDate,
          expiryDate: expiryDate,
          floatExp: parseInt(this.formData.floatingDays) || 0,
          hardwareLock: this.formData.hardwareLocking === "true",
          signKey: {
            id: parseInt(this.formData.signingKey),
            name:
              this.pickKey.find(
                (key) => key.id === parseInt(this.formData.signingKey)
              )?.name || "",
          },
          properties: customProperties || [],
        };

        const response = await axios.post(
          `${this.apiBaseUrl}/licenses/create`,
          JSON.stringify(licenseData),
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (
          (response.status === 200 || response.status === 201) &&
          response.data
        ) {
          $(".modal").modal("hide");
          $("#createSuccessModal").modal("show");
          $("#createSuccessModal").one("hidden.bs.modal", () => {
            this.resetLicenseForm();
            // Refresh the licenses table
            this.fetchLicenses();
            // Switch to the list tab to show the updated table
            this.activeLicenseTab = "list";
          });
        }
      } catch (error) {
        console.error("Error creating license:", error);
        $("#errorModalcreate").modal("show");
      }
    },

    resetForm() {
      this.formValues = {};
      this.formData.company = "";
      this.formData.templateId = "";
      this.formData.expirationDate = "";
      this.formData.signingKey = "";
      this.formData.hardwareLocking = "";
      this.selectedTemplate = null;
      this.templateFields = [];
    },

    deleteModal(licenseId) {
      this.deleteLicenseId = licenseId;
    },

    async deleteLicense() {
      if (!this.deleteLicenseId) return;

      try {
        const response = await axios.delete(
          `${this.apiBaseUrl}/license/${this.deleteLicenseId}`
        );
        if (response.status === 200) {
          $("#deleteLicenseModal").modal("hide");
          this.fetchLicenses();
          $("#licenseDeleteSuccessModal").modal("show");
          setTimeout(() => {
            $("#licenseDeleteSuccessModal").modal("hide");
          }, 2000);
        }
      } catch (error) {
        console.error("Error deleting license:", error);
        $("#deleteLicenseModal").modal("hide");
        $("#licenseErrorModal").modal("show");
        setTimeout(() => {
          $("#licenseErrorModal").modal("hide");
        }, 2000);
      }
    },

    setActiveLicenseTab(tab) {
      this.activeLicenseTab = tab;
      if (tab === "list") {
        this.displayCompanyLicenses();
      } else if (tab === "create") {
        // Fetch templates when switching to create tab
        this.fetchTemplates();
        this.fetchSigningKeys();
      }
    },

    // Contact management methods
    editContact(contactId) {
      const contact = this.contacts.find((c) => c.id === contactId);
      if (contact) {
        this.editContactData = { ...contact };
        $("#editContactModal").modal("show");
      }
    },

    validateEditContactForm() {
      let isValid = true;
      this.editValidationErrors = {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
      };

      if (!this.editContactData.firstName.trim()) {
        this.editValidationErrors.firstName = "First name is required";
        isValid = false;
      } else if (this.editContactData.firstName.length < 2) {
        this.editValidationErrors.firstName =
          "First name must be at least 2 characters";
        isValid = false;
      }

      if (!this.editContactData.lastName.trim()) {
        this.editValidationErrors.lastName = "Last name is required";
        isValid = false;
      } else if (this.editContactData.lastName.length < 2) {
        this.editValidationErrors.lastName =
          "Last name must be at least 2 characters";
        isValid = false;
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!this.editContactData.email.trim()) {
        this.editValidationErrors.email = "Email is required";
        isValid = false;
      } else if (!emailRegex.test(this.editContactData.email)) {
        this.editValidationErrors.email = "Please enter a valid email address";
        isValid = false;
      }

      const phoneRegex =
        /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
      if (
        this.editContactData.phone.trim() &&
        !phoneRegex.test(this.editContactData.phone)
      ) {
        this.editValidationErrors.phone = "Please enter a valid phone number";
        isValid = false;
      }

      if (!this.editContactData.contactType) {
        this.editValidationErrors.contactType = "Please select a contact type";
        isValid = false;
      }

      return isValid;
    },

    updateContact() {
      if (!this.validateEditContactForm()) {
        return;
      }

      axios
        .put(`${this.apiBaseUrl}/contacts`, this.editContactData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          if (response.status === 200) {
            $("#editContactModal").modal("hide");
            this.fetchContacts();
            $("#contactUpdateSuccessModal").modal("show");
            setTimeout(() => {
              $("#contactUpdateSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error updating contact:", error);
          $("#editContactModal").modal("hide");
          $("#contactErrorModal").modal("show");
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },

    submitContact() {
      if (!this.validateContactForm()) {
        return;
      }

      const contactData = {
        ...this.contactData,
        companyId: this.company.id,
      };

      axios
        .post(`${this.apiBaseUrl}/contacts`, contactData)
        .then((response) => {
          if (response.status === 201) {
            this.activeContactTab = "list";
            this.fetchContacts();
            this.resetContactForm();
            $("#contactSuccessModal").modal("show");
            setTimeout(() => {
              $("#contactSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error adding contact:", error);
          $("#contactErrorModal").modal("show");
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },

    resetLicenseForm() {
      this.formData = {
        company: this.company.id,
        templateId: "",
        licenseName: "",
        startDate: "",
        expirationDate: "",
        floatingDays: "",
        signingKey: "",
        hardwareLocking: "",
      };
      this.selectedTemplate = null;
      this.templateFields = [];
      this.formValues = {};
      this.licenseProperties = [];
    },

    resetContactForm() {
      this.contactData = {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        mobilePhone: "",
        contactType: "",
      };
    },

    getCompanyIdFromUrl() {
      const urlParams = new URLSearchParams(window.location.search);
      const id = urlParams.get("id");
      return id;
    },

    async fetchContacts() {
      const companyId = this.getCompanyIdFromUrl();
      if (!companyId) return;

      try {
        const response = await axios.get(
          `${this.apiBaseUrl}/contacts/company/${companyId}`
        );
        if (response.status === 200) {
          this.allContacts = response.data;
          this.contacts = [...this.allContacts];
        }
      } catch (error) {
        console.error("Error fetching contacts:", error);
      }
    },

    formatContactType(type) {
      if (type == null) return "Unknown";
      if (typeof type === "string" && this.contactTypeLabels[type]) {
        return this.contactTypeLabels[type];
      }
      if (typeof type === "object") {
        if (type.name) return type.name;
        if (type.type && this.contactTypeLabels[type.type])
          return this.contactTypeLabels[type.type];
      }
      return String(type);
    },

    async fetchCompanyData() {
      const id = this.getCompanyIdFromUrl();
      if (id) {
        try {
          const response = await axios.get(
            `${this.apiBaseUrl}/companies/${id}`
          );
          this.company = response.data;
        } catch (error) {
          console.error("Error fetching company:", error);
        }
      }
    },

    setActiveTab(tab) {
      this.activeTab = tab;
      if (tab === "licenses") {
        this.displayCompanyLicenses();
      } else if (tab === "maintenance") {
        this.fetchMaintenanceData();
      } else if (tab === "extended") {
        this.fetchExtendedSupportData();
      }
    },

    setActiveContactTab(tab) {
      this.activeContactTab = tab;
    },

    validateContactForm() {
      let isValid = true;
      this.validationErrors = {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
      };

      if (!this.contactData.firstName.trim()) {
        this.validationErrors.firstName = "First name is required";
        isValid = false;
      } else if (this.contactData.firstName.length < 2) {
        this.validationErrors.firstName =
          "First name must be at least 2 characters";
        isValid = false;
      }

      if (!this.contactData.lastName.trim()) {
        this.validationErrors.lastName = "Last name is required";
        isValid = false;
      } else if (this.contactData.lastName.length < 2) {
        this.validationErrors.lastName =
          "Last name must be at least 2 characters";
        isValid = false;
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!this.contactData.email.trim()) {
        this.validationErrors.email = "Email is required";
        isValid = false;
      } else if (!emailRegex.test(this.contactData.email)) {
        this.validationErrors.email = "Please enter a valid email address";
        isValid = false;
      }

      const phoneRegex =
        /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
      if (
        this.contactData.phone.trim() &&
        !phoneRegex.test(this.contactData.phone)
      ) {
        this.validationErrors.phone = "Please enter a valid phone number";
        isValid = false;
      }

      if (!this.contactData.contactType) {
        this.validationErrors.contactType = "Please select a contact type";
        isValid = false;
      }

      return isValid;
    },

    applyFilters() {
      if (!this.allContacts || !this.allContacts.length) {
        this.allContacts = [...this.contacts];
      }

      let filtered = [...this.allContacts];

      if (this.filters.firstName) {
        const searchTerm = this.filters.firstName.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.firstName &&
            contact.firstName.toLowerCase().includes(searchTerm)
        );
      }

      if (this.filters.lastName) {
        const searchTerm = this.filters.lastName.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.lastName &&
            contact.lastName.toLowerCase().includes(searchTerm)
        );
      }

      if (this.filters.contactType) {
        filtered = filtered.filter((contact) => {
          if (typeof contact.contactType === "string") {
            return contact.contactType === this.filters.contactType;
          } else if (
            typeof contact.contactType === "object" &&
            contact.contactType !== null
          ) {
            if (contact.contactType.type) {
              return contact.contactType.type === this.filters.contactType;
            } else if (contact.contactType.name) {
              return contact.contactType.name === this.filters.contactType;
            }
          }
          return false;
        });
      }

      if (this.filters.email) {
        const searchTerm = this.filters.email.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.email && contact.email.toLowerCase().includes(searchTerm)
        );
      }

      this.contacts = filtered;
    },

    resetFilters() {
      this.filters = {
        firstName: "",
        lastName: "",
        contactType: "",
        email: "",
      };
      this.fetchContacts();
    },

    deleteContact() {
      if (!this.deleteContactId) return;

      axios
        .delete(`${this.apiBaseUrl}/contacts/delete/${this.deleteContactId}`)
        .then((response) => {
          if (response.status === 200) {
            $("#deleteContactModal").modal("hide");
            this.contacts = this.contacts.filter(
              (contact) => contact.id !== this.deleteContactId
            );
            $("#contactDeleteSuccessModal").modal("show");
            setTimeout(() => {
              $("#contactDeleteSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error deleting contact:", error);
          $("#deleteContactModal").modal("hide");
          $("#contactErrorModal").modal("show");
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },

    extractProductFromLicense(license) {
      // Try to extract product from license name
      if (license.name) {
        const nameParts = license.name.split("-");
        if (nameParts.length > 1) {
          const productPart = nameParts[1];
          // Map common product patterns
          if (productPart.toLowerCase().includes("bam")) return "Accolm BAM";
          if (productPart.toLowerCase().includes("ssp")) return "Accolm SSP";
          if (productPart.toLowerCase().includes("accolm")) return productPart;
          return productPart;
        }
      }

      // Check if license has a direct product property
      if (license.product) {
        return license.product;
      }

      // Check license properties for product information
      if (license.properties && Array.isArray(license.properties)) {
        const productProperty = license.properties.find(
          (prop) => prop.name && prop.name.toLowerCase().includes("product")
        );
        if (productProperty && productProperty.value) {
          return productProperty.value;
        }
      }

      // Default fallback
      return "Accolm BAM";
    },

    onLicenseSelected() {
      if (!this.selectedLicense) return;

      // Find the selected license in the array
      const license = this.companyLicenses.find(
        (lic) => lic.id === parseInt(this.selectedLicense)
      );

      if (license) {
        // Set the correct variables that submitMaintenanceSupport() expects
        this.selectedProduct =
          license.product || this.extractProductFromLicense(license);
        this.productVersion = license.productVersion || "";
        this.selectedLicenseType = license.licenseType || "";

        // Also set the maintenanceFormData for consistency
        this.maintenanceFormData.product = this.selectedProduct;
        this.maintenanceFormData.productVersion = this.productVersion;
        this.maintenanceFormData.licenseType = this.selectedLicenseType;

        // Handle dates based on license expiry date
        this.handleLicenseDates(license);

        console.log(
          `Auto-filled fields based on license: Product=${this.selectedProduct}, Version=${this.productVersion}, Type=${this.selectedLicenseType}`
        );
      }
    },

    handleLicenseDates(license, formType = "maintenance") {
      const formData =
        formType === "extended"
          ? this.extendedSupportFormData
          : this.maintenanceFormData;

      if (
        license.expiryDate &&
        license.expiryDate !== "Never" &&
        license.expiryDate !== "null" &&
        license.expiryDate !== ""
      ) {
        try {
          const licenseExpiryDate = new Date(license.expiryDate);

          // Check if the license expiry date is valid
          if (!isNaN(licenseExpiryDate.getTime())) {
            // Set start date to today
            const today = new Date();
            formData.startDate = today.toISOString().split("T")[0];

            // Set end date to the license expiry date
            formData.endDate = licenseExpiryDate.toISOString().split("T")[0];

            console.log(
              `Auto-filled dates based on license expiry: Start=${formData.startDate}, End=${formData.endDate}`
            );
          } else {
            // Invalid date format, prompt user
            this.promptUserForDates(formType);
          }
        } catch (error) {
          console.error("Error parsing license expiry date:", error);
          this.promptUserForDates(formType);
        }
      } else {
        // No expiry date or "Never" expires, prompt user to choose dates
        this.promptUserForDates(formType);
      }
    },

    promptUserForDates(formType = "maintenance") {
      const formData =
        formType === "extended"
          ? this.extendedSupportFormData
          : this.maintenanceFormData;

      // Clear existing dates
      formData.startDate = "";
      formData.endDate = "";

      // Set default dates but highlight that user needs to review them
      const today = new Date();
      formData.startDate = today.toISOString().split("T")[0];

      // Set default end date to 1 year from today
      const nextYear = new Date();
      nextYear.setFullYear(nextYear.getFullYear() + 1);
      formData.endDate = nextYear.toISOString().split("T")[0];

      console.log(
        "License has no expiry date. Default dates set - user should review and modify as needed."
      );
    },

    generateMissingLicenseCodes() {
      // Implementation for generating missing license codes
      // This method was referenced in fetchCompanyLicensesPaginated
      return Promise.resolve();
    },

    async fetchCompanyLicensesPaginated() {
      // This method can be an alias to displayCompanyLicenses
      await this.displayCompanyLicenses();
    },

    updateMaintenanceItem() {
      // Clear previous validation errors
      this.editMaintenanceValidationErrors = {};

      // Validate the form
      if (!this.validateMaintenanceEdit()) {
        return;
      }

      // Prepare the data for the API call
      const updateData = {
        id: this.editMaintenanceData.id,
        companyName: this.editMaintenanceData.companyName,
        licenseKey: this.editMaintenanceData.licenseKey,
        product: this.editMaintenanceData.product,
        productVersion: this.editMaintenanceData.productVersion,
        licenseType: this.editMaintenanceData.licenseType,
        startDate: this.editMaintenanceData.startDate,
        endDate: this.editMaintenanceData.endDate,
        status: this.editMaintenanceData.status,
        extendedSupport: this.editMaintenanceData.extendedSupport,
      };

      // Make API call to update the maintenance item
      fetch(
        `${this.apiBaseUrl}/maintenance-supports/${this.editMaintenanceData.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updateData),
        }
      )
        .then((response) => {
          if (!response.ok) {
            throw new Error("Failed to update maintenance item");
          }
          return response.json();
        })
        .then((data) => {
          // Update the item in the local array
          const index = this.maintenanceItems.findIndex(
            (item) => item.id === this.editMaintenanceData.id
          );
          if (index !== -1) {
            // Update the item with the new data
            this.maintenanceItems[index] = {
              ...this.maintenanceItems[index],
              ...updateData,
            };
          }

          // Hide the edit modal
          $("#editMaintenanceModal").modal("hide");

          // Show success modal
          $("#maintenanceUpdateSuccessModal").modal("show");
          $("#maintenanceUpdateSuccessModal").modal("show");
          this.autoHideSuccessModal("maintenanceUpdateSuccessModal");

          // Refresh the maintenance data
          this.fetchMaintenanceItems();
        })
        .catch((error) => {
          console.error("Error updating maintenance item:", error);
          $("#maintenanceUpdateErrorModal").modal("show");
        });
    },

    // Validate maintenance edit form
    validateMaintenanceEdit() {
      let isValid = true;

      if (!this.editMaintenanceData.licenseKey.trim()) {
        this.editMaintenanceValidationErrors.licenseKey =
          "License Key is required";
        isValid = false;
      }

      if (!this.editMaintenanceData.product.trim()) {
        this.editMaintenanceValidationErrors.product = "Product is required";
        isValid = false;
      }

      if (!this.editMaintenanceData.productVersion.trim()) {
        this.editMaintenanceValidationErrors.productVersion =
          "Product Version is required";
        isValid = false;
      }

      if (!this.editMaintenanceData.licenseType.trim()) {
        this.editMaintenanceValidationErrors.licenseType =
          "License Type is required";
        isValid = false;
      }

      if (!this.editMaintenanceData.startDate) {
        this.editMaintenanceValidationErrors.startDate =
          "Start Date is required";
        isValid = false;
      }

      if (!this.editMaintenanceData.endDate) {
        this.editMaintenanceValidationErrors.endDate = "End Date is required";
        isValid = false;
      }

      // Validate that end date is after start date
      if (
        this.editMaintenanceData.startDate &&
        this.editMaintenanceData.endDate
      ) {
        const startDate = new Date(this.editMaintenanceData.startDate);
        const endDate = new Date(this.editMaintenanceData.endDate);

        if (endDate <= startDate) {
          this.editMaintenanceValidationErrors.endDate =
            "End Date must be after Start Date";
          isValid = false;
        }
      }

      return isValid;
    },

    // Helper method to format date for input field
    formatDateForInput(dateString) {
      if (!dateString || dateString === "Never" || dateString === "null") {
        return "";
      }

      try {
        const date = new Date(dateString);
        return date.toISOString().split("T")[0];
      } catch (error) {
        console.error("Error formatting date:", error);
        return "";
      }
    },

    // Reset edit maintenance form
    resetEditMaintenanceForm() {
      this.editMaintenanceData = {
        id: null,
        companyName: "",
        licenseKey: "",
        product: "",
        productVersion: "",
        licenseType: "",
        startDate: "",
        endDate: "",
        status: "",
        extendedSupport: false,
      };
      this.editMaintenanceValidationErrors = {};
    },

    // Handle modal close event
    onEditMaintenanceModalClose() {
      this.resetEditMaintenanceForm();
      this.maintenanceToEdit = null;
    },

    // Auto-hide success modal after 3 seconds
    autoHideSuccessModal(modalId) {
      setTimeout(() => {
        $(`#${modalId}`).modal("hide");
      }, 3000);
    },

    editMaintenanceItem(maintenanceId) {
      // Find the maintenance item to edit
      const item = this.maintenanceItems.find((m) => m.id === maintenanceId);
      if (!item) {
        console.error("Maintenance item not found");
        return;
      }

      // Store reference to the item being edited
      this.maintenanceToEdit = item;

      // Populate the edit form with current data
      this.editMaintenanceData = {
        id: item.id,
        companyName: item.companyName || this.company.name,
        licenseKey: item.licenseKey || "",
        product: item.product || "",
        productVersion: item.productVersion || "",
        licenseType: item.licenseType || "",
        startDate: this.formatDateForInput(item.startDate),
        endDate: this.formatDateForInput(item.endDate),
        status: item.status || "",
        extendedSupport: item.extendedSupport || false,
      };

      // Clear any previous validation errors
      this.editMaintenanceValidationErrors = {};

      // Show the edit modal
      $("#editMaintenanceModal").modal("show");
    },

    formatLicenseDate(dateString) {
      if (
        !dateString ||
        dateString === "" ||
        dateString === "Never" ||
        dateString === "null"
      ) {
        return "Never";
      }
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return "Never";
        }
        return date.toISOString().split("T")[0];
      } catch (e) {
        return "Never";
      }
    },
  },

  watch: {
    "maintenanceFilters.licenseKey": function () {
      this.applyMaintenanceFilters();
    },
    "maintenanceFilters.product": function () {
      this.applyMaintenanceFilters();
    },
    "maintenanceFilters.expiryDate": function () {
      this.applyMaintenanceFilters();
    },

    "extendedFilters.licenseKey": function () {
      this.applyExtendedFilters();
    },
    "extendedFilters.product": function () {
      this.applyExtendedFilters();
    },
    "extendedFilters.expiryDate": function () {
      this.applyExtendedFilters();
    },
    "filters.firstName": function () {
      this.applyFilters();
    },
    currentLicensePage() {
      this.fetchCompanyLicensesPaginated();
    },
    "filters.lastName": function () {
      this.applyFilters();
    },
    "filters.contactType": function () {
      this.applyFilters();
    },
    "filters.email": function () {
      this.applyFilters();
    },
    "licenseFilters.name": function () {
      this.applyLicenseFilters();
    },
    "licenseFilters.licenseCode": function () {
      this.applyLicenseFilters();
    },
    "licenseFilters.expiryDate": function () {
      this.applyLicenseFilters();
    },
  },

  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },
    companyExpiryItems() {
      const today = new Date();
      const threeMonthsFromNow = new Date();
      threeMonthsFromNow.setMonth(today.getMonth() + 3);

      return this.maintenanceItems.filter((item) => {
        // Skip items with "Never" expiry
        if (
          !item.endDate ||
          item.endDate === "Never" ||
          item.endDate === "null" ||
          item.endDate === ""
        ) {
          return false;
        }

        // Check if status is explicitly "EXPIRED"
        if (item.status && item.status.toUpperCase() === "EXPIRED") {
          return true;
        }

        try {
          const endDate = new Date(item.endDate);

          // Include if expired (past today) or expiring within 3 months
          return endDate <= threeMonthsFromNow;
        } catch (error) {
          console.error("Error parsing end date:", error);
          return false;
        }
      });
    },
    // Count for expiry items
    expiryItemCount() {
      return this.companyExpiryItems.length;
    },

    // Pages for expiry items
    expiryPages() {
      return Math.ceil(this.expiryItemCount / this.expiryMaxPerPage);
    },

    // Paginated expiry items
    paginatedExpiryItems() {
      const startIndex = (this.expiryCurrentPage - 1) * this.expiryMaxPerPage;
      const endIndex = startIndex + this.expiryMaxPerPage;
      return this.companyExpiryItems.slice(startIndex, endIndex);
    },
    displayedCompanyLicenses() {
      const licenses = this.isLicenseFiltering
        ? this.filteredCompanyLicenses
        : this.companyLicenses;
      const startIndex = (this.licenseCurrentPage - 1) * this.licenseMaxPerPage;
      const endIndex = startIndex + this.licenseMaxPerPage;
      return licenses.slice(startIndex, endIndex);
    },
    displayedMaintenanceItems() {
      return this.isFilteringMaintenance
        ? this.filteredMaintenanceItems
        : this.maintenanceItems;
    },

    displayedExtendedItems() {
      return this.isFilteringExtended
        ? this.filteredExtendedItems
        : this.extendedSupportItems;
    },

    availableLicenseKeys() {
      return this.companyLicenses
        .filter((license) => license.licenseKey || license.name)
        .map((license) => ({
          key: license.licenseKey || license.name,
          license: license,
        }));
    },
  },

  template: `
<div id="app-root">
  <div>
  <div class="row">
    <div class="col-sm-12" style="text-align: center">
      <h1>{{company.name}}</h1>
    </div>

    <div class="home-request" style="margin-top: 20px;">
      <ul class="nav nav-tabs" role="tablist">
        <li :class="{active: activeTab === 'contacts'}">
          <a @click="setActiveTab('contacts')" data-toggle="tab" href="#contacts">Contacts</a>
        </li>
        <li :class="{active: activeTab === 'licenses'}">
          <a @click="setActiveTab('licenses')" data-toggle="tab" href="#licenses">Licenses</a>
        </li>
        <li :class="{active: activeTab === 'maintenance'}">
          <a @click="setActiveTab('maintenance')" data-toggle="tab" href="#maintenance">Maintenance & Support</a>
        </li>
        <li :class="{active: activeTab === 'extended'}">
          <a @click="setActiveTab('extended')" data-toggle="tab" href="#extended">Extended Support</a>
        </li>
      </ul>

      <div class="tab-content">
        <div id="contacts" :class="['tab-pane', { active: activeTab === 'contacts' }]">
          <ul class="nav nav-tabs" style="margin-top: 15px;">
            <li :class="{active: activeContactTab === 'list'}">
              <a @click="setActiveContactTab('list')" href="#contactsList">List Contacts</a>
            </li>
            <li :class="{active: activeContactTab === 'add'}">
              <a @click="setActiveContactTab('add')" href="#addContact">Add Contact</a>
            </li>
          </ul>
          

          <!-- filters -->
          <div class="row mb-3" v-show="activeContactTab === 'list'">
            <div class="col-md-12">
              <div class="panel panel-default">
                <div class="panel-heading">
                  <h4 class="panel-title">
                    <a data-toggle="collapse" href="#contactFilterCollapse">
                      <i class="glyphicon glyphicon-filter"></i> Filters
                    </a>
                  </h4>
                </div>
                <div id="contactFilterCollapse" class="panel-collapse collapse">
                  <div class="panel-body">
                    <div class="row">
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>First Name</label>
                          <input type="text" class="form-control" v-model="filters.firstName" placeholder="Filter by first name">
                        </div>
                      </div>
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Last Name</label>
                          <input type="text" class="form-control" v-model="filters.lastName" placeholder="Filter by last name">
                        </div>
                      </div>
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Contact Type</label>
                          <select class="form-control" v-model="filters.contactType">
                            <option value="">All Types</option>
                            <option value="PRIMARY">Primary</option>
                            <option value="SECONDARY">Secondary</option>
                            <option value="TECHNICAL">Technical</option>
                            <option value="BILLING">Billing</option>
                          </select>
                        </div>
                      </div>
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Email</label>
                          <input type="text" class="form-control" v-model="filters.email" placeholder="Filter by email">
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-12 text-right">
                        <button class="btn btn-default" @click="resetFilters">
                          <i class="glyphicon glyphicon-refresh"></i> Reset Filters
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="tab-content">
            <div id="contactsList" :class="['tab-pane', { active: activeContactTab === 'list' }]">
              <table class="table">
                <thead>
                  <tr>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Mobile Phone</th>
                    <th>Contact Type</th>
                    <th>Options</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="contact in contacts" :key="contact.id">
                    <td>{{contact.firstName}}</td>
                    <td>{{contact.lastName}}</td>
                    <td>{{contact.email}}</td>
                    <td>{{contact.phone}}</td>
                    <td>{{contact.mobilePhone}}</td>
                    <td>{{formatContactType(contact.contactType)}}</td>
                    <td>
                      <!-- Edit Button -->
                      <button class="btn btn-light" @click="editContact(contact.id)" title="Edit">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                        </svg>
                      </button>

                      <!-- Delete Button -->
                      <button class="btn btn-light" @click="deleteModal(contact.id)" title="Delete">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                          <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                        </svg>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!--- Add Contact Form -->
            <div id="addContact" :class="['tab-pane', { active: activeContactTab === 'add' }]">
              <div style="margin-top: 20px;">
                <div class="row">
                  <div class="col-sm-2"></div>
                  <div class="col-sm-8">
                    <form class="form-horizontal" @submit.prevent="submitContact">
                      <div class="form-group" :class="{'has-error': validationErrors.firstName}">
                        <div class="col-sm-3">
                          <label class="control-label">First Name</label>
                        </div>
                        <div class="col-sm-6">
                          <input type="text" v-model="contactData.firstName" class="form-control" required>
                          <span class="help-block" v-if="validationErrors.firstName">{{ validationErrors.firstName }}</span>
                        </div>
                      </div>

                      <div class="form-group" :class="{'has-error': validationErrors.lastName}">
                        <div class="col-sm-3">
                          <label class="control-label">Last Name</label>
                        </div>
                        <div class="col-sm-6">
                          <input type="text" v-model="contactData.lastName" class="form-control" required>
                          <span class="help-block" v-if="validationErrors.lastName">{{ validationErrors.lastName }}</span>
                        </div>
                      </div>

                      <div class="form-group" :class="{'has-error': validationErrors.email}">
                        <div class="col-sm-3">
                          <label class="control-label">Email</label>
                        </div>
                        <div class="col-sm-6">
                          <input type="email" v-model="contactData.email" class="form-control" required>
                          <span class="help-block" v-if="validationErrors.email">{{ validationErrors.email }}</span>
                        </div>
                      </div>

                      <div class="form-group" :class="{'has-error': validationErrors.phone}">
                        <div class="col-sm-3">
                          <label class="control-label">Phone</label>
                        </div>
                        <div class="col-sm-6">
                          <input type="tel" v-model="contactData.phone" class="form-control">
                          <span class="help-block" v-if="validationErrors.phone">{{ validationErrors.phone }}</span>
                        </div>
                      </div>

                      <div class="form-group">
                        <div class="col-sm-3">
                          <label class="control-label">Mobile Phone</label>
                        </div>
                        <div class="col-sm-6">
                          <input type="tel" v-model="contactData.mobilePhone" class="form-control">
                        </div>
                      </div>

                      <div class="form-group" :class="{'has-error': validationErrors.contactType}">
                        <div class="col-sm-3">
                          <label class="control-label">Contact Type</label>
                        </div>
                        <div class="col-sm-6">
                          <select v-model="contactData.contactType" class="form-control" required>
                            <option value="">Select Contact Type</option>
                            <option value="PRIMARY">Primary</option>
                            <option value="SECONDARY">Secondary</option>
                            <option value="TECHNICAL">Technical</option>
                            <option value="BILLING">Billing</option>
                          </select>
                          <span class="help-block" v-if="validationErrors.contactType">{{ validationErrors.contactType }}</span>
                        </div>
                      </div>

                      <div class="form-group">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-6">
                          <button type="button" @click="submitContact" class="request-button form-control">Add Contact</button>
                        </div>
                      </div>
                    </form>
                  </div>
                  <div class="col-sm-2"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        

      <!-- Licenses Tab -->
<div id="licenses" :class="['tab-pane', { active: activeTab === 'licenses' }]">
  <ul class="nav nav-tabs" style="margin-top: 15px;">
    <li :class="{active: activeLicenseTab === 'list'}">
      <a @click="setActiveLicenseTab('list')" href="#list">List Licenses</a>
    </li>
    <li :class="{active: activeLicenseTab === 'create'}">
      <a @click="setActiveLicenseTab('create')" href="#create">Create License</a>
    </li>
  </ul>
  
  <!-- Tabs Content -->
  <div class="tab-content">
    <!-- List Tab -->
    <div :class="['tab-pane', { active: activeLicenseTab === 'list' }]" id="list">
      
      <!-- Filter Section (exactly like license.js) -->
      <div class="panel panel-default" style="margin-bottom: 10px;">
        <div class="panel-heading">
          <h3 class="panel-title">
            <a data-toggle="collapse" href="#licenseFilterCollapse">
              <i class="glyphicon glyphicon-filter"></i> Filters
            </a>
          </h3>
        </div>
        <div id="licenseFilterCollapse" class="panel-collapse collapse">
          <div class="panel-body" style="padding: 10px;">
            <div class="row">
             <div class="col-md-4">
  <div class="form-group" style="margin-bottom: 10px;">
    <label for="licenseNameFilter" style="font-weight: 600;">License Name</label>
    <input type="text" id="licenseNameFilter" class="form-control" v-model="licenseFilters.name" placeholder="Filter by license name">
  </div>
</div>
              <div class="col-md-4">
                <div class="form-group" style="margin-bottom: 10px;">
                  <label for="licenseCodeFilter" style="font-weight: 600;">License Code</label>
                  <input type="text" id="licenseCodeFilter" class="form-control" v-model="licenseFilters.licenseCode" placeholder="Filter by license code">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group" style="margin-bottom: 10px;">
                  <label for="licenseExpiryDateFilter" style="font-weight: 600;">Expiring On or Before</label>
                  <input 
                    type="date" 
                    id="licenseExpiryDateFilter" 
                    class="form-control" 
                    v-model="licenseFilters.expiryDate"
                    @change="applyLicenseFilters"
                    min="1900-01-01"
                    max="2100-12-31">
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12" style="text-align: right;">
                <button type="button" class="btn btn-default" @click="clearLicenseFilters">
                  <i class="glyphicon glyphicon-refresh"></i> Reset Filters
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Table (exactly like license.js) -->
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th colspan="7" class="tableHeader" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
                <div class="pull-left" style="line-height: 34px;">Count: {{licenseItemCount}}</div>
                <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
                  <span>Page {{licenseCurrentPage}} of {{licensePages}}</span>
                  <ul class="pagination pagination-sm" style="margin: 0; border: none;">
                    <li :class="{disabled: licenseCurrentPage === 1}" style="border: none;">
                      <span @click="licenseCurrentPage = 1; displayCompanyLicenses()" style="border: none;">
                        <i class="glyphicon glyphicon-step-backward"></i>
                      </span>
                    </li>
                    <li :class="{disabled: licenseCurrentPage === 1}" style="border: none;">
                      <span @click="licenseCurrentPage > 1 ? (licenseCurrentPage--, displayCompanyLicenses()) : null" style="border: none;">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                      </span>
                    </li>
                    <li :class="{disabled: licenseCurrentPage === licensePages}" style="border: none;">
                      <span @click="licenseCurrentPage < licensePages ? (licenseCurrentPage++, displayCompanyLicenses()) : null" style="border: none;">
                        <i class="glyphicon glyphicon-chevron-right"></i>
                      </span>
                    </li>
                    <li :class="{disabled: licenseCurrentPage === licensePages}" style="border: none;">
                      <span @click="licenseCurrentPage = licensePages; displayCompanyLicenses()" style="border: none;">
                        <i class="glyphicon glyphicon-step-forward"></i>
                      </span>
                    </li>
                  </ul>
                  <span style="white-space: nowrap;">Results per page : </span>
                  <select class="pages-form-control" @change="onLicensePageSizeChange($event)" style="margin-left: 5px;">
                    <option value="10" :selected="licenseMaxPerPage === 10">10</option>
                    <option value="25" :selected="licenseMaxPerPage === 25">25</option>
                    <option value="50" :selected="licenseMaxPerPage === 50">50</option>
                    <option value="100" :selected="licenseMaxPerPage === 100">100</option>
                  </select>
                </div>
              </th>
            </tr>
            <tr>
              <th>License Name</th>
              <th>License Code</th>
              <th>Status</th>
              <th>Creation Date</th>
              <th>Expiration Date</th>
              <th>Signing Key</th>
              <th>Action</th>
            </tr>
          </thead>

          <tbody v-show='!isFetchingLicenses'>
            <tr v-for="license in displayedCompanyLicenses" :key="'license-' + license.id">
              <td style="cursor: pointer;" @click="displaySingleLicense(license.id)" data-toggle="modal" data-target="#licenseDetailsModal">
                <a href="#">{{ license.name || 'N/A' }}</a>
              </td>
              <td>{{ license.licenseKey || 'N/A' }}</td>
              <td>
                <span :class="{'text-success': license.validationStatus, 'text-danger': !license.validationStatus}">
                  {{ license.validationStatus ? 'Valid' : 'Invalid' }}
                </span>
              </td>
              <td>{{ formatLicenseDate(license.creationDate || license.creation_date) }}</td>
              <td>{{ formatLicenseDate(license.expiryDate || license.expiry_date) }}</td>
              <td>{{ license.signKey && license.signKey.name ? license.signKey.name : 'N/A' }}</td>
              <td>
                <div class="btn-group">
                  <button @click="editLicense(license.id)" class="btn btn-light" title="Edit">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                    </svg>
                  </button>
                  <button @click="displayDownloadLicense(license.id)" class="btn btn-light" title="Download">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                      <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                    </svg>
                  </button>
                  <button data-toggle="modal" data-target="#deleteLicenseModal" @click="deleteLicenseModal(license.id)" class="btn btn-light" title="Delete">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                      <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
          <tbody v-show="isFetchingLicenses">
            <tr>
              <td colspan="7" class="text-center">
                <p>Loading licenses...</p>
              </td>
            </tr>
          </tbody>
          <tbody v-show="!isFetchingLicenses && displayedCompanyLicenses.length === 0">
            <tr>
              <td colspan="7" class="text-center">
                <p>No licenses found.</p>
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <th colspan="7" class="tableHeader" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
                <div class="pull-left" style="line-height: 34px;">Count: {{licenseItemCount}}</div>
                <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
                  <span>Page {{licenseCurrentPage}} of {{licensePages}}</span>
                  <ul class="pagination pagination-sm" style="margin: 0; border: none;">
                    <li :class="{disabled: licenseCurrentPage === 1}" style="border: none;">
                      <span @click="licenseCurrentPage = 1; displayCompanyLicenses()" style="border: none;">
                        <i class="glyphicon glyphicon-step-backward"></i>
                      </span>
                    </li>
                    <li :class="{disabled: licenseCurrentPage === 1}" style="border: none;">
                      <span @click="licenseCurrentPage > 1 ? (licenseCurrentPage--, displayCompanyLicenses()) : null" style="border: none;">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                      </span>
                    </li>
                    <li :class="{disabled: licenseCurrentPage === licensePages}" style="border: none;">
                      <span @click="licenseCurrentPage < licensePages ? (licenseCurrentPage++, displayCompanyLicenses()) : null" style="border: none;">
                        <i class="glyphicon glyphicon-chevron-right"></i>
                      </span>
                    </li>
                    <li :class="{disabled: licenseCurrentPage === licensePages}" style="border: none;">
                      <span @click="licenseCurrentPage = licensePages; displayCompanyLicenses()" style="border: none;">
                        <i class="glyphicon glyphicon-step-forward"></i>
                         </span>
                    </li>
                  </ul>
                  <span style="white-space: nowrap;">Results per page : </span>
                  <select class="pages-form-control" @change="onLicensePageSizeChange($event)" style="margin-left: 5px;">
                    <option value="10" :selected="licenseMaxPerPage === 10">10</option>
                    <option value="25" :selected="licenseMaxPerPage === 25">25</option>
                    <option value="50" :selected="licenseMaxPerPage === 50">50</option>
                    <option value="100" :selected="licenseMaxPerPage === 100">100</option>
                  </select>
                </div>
              </th>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
            
            <!-- Create License Tab -->
            <div :class="['tab-pane', { active: activeLicenseTab === 'create' }]" id="create">
              <div class="row">
                <div class="col-sm-2"></div>
                <div class="col-sm-8" style="margin-top: 29px;">
                  <form class="form-horizontal" @submit.prevent="submitLicense">
                    <div class="form-group">
                      <div class="col-sm-3">
                        <label class="control-label">Company</label>
                      </div>
                      <div class="col-sm-6">
                        <input type="text" class="form-control" :value="company.name" disabled>
                        <input type="hidden" v-model="formData.company">
                      </div>
                    </div>
                    
                    <div class="form-group">
                      <div class="col-sm-3">
                        <label class="control-label">Template</label>
                      </div>
                      <div class="col-sm-6">
                       <select v-model="formData.templateId" @change="onTemplateSelect" class="form-control">
    <option value="">Select a template...</option>
    <option v-for="template in templates" :key="template.id" :value="template.id">
        {{ template.name }}
    </option>
</select>
                      </div>
                    </div>
                    
                    <!-- Additional fields that appear only when template is selected -->
                    <div v-show="formData.templateId">
                      <div class="form-group">
                        <div class="col-sm-3">
                          <label class="control-label">Expiration Date</label>
                        </div>
                        <div class="col-sm-6">
                          <!-- For disabled state (no expiry templates) -->
                          <input v-if="!expirationEditable"
                                 type="text" 
                                 class="form-control" 
                                 value="Never"
                                 style="background-color: #f2f2f2; color: #999; cursor: not-allowed;"
                                 readonly
                                 @click="handleDisabledExpiryClick">
                          <!-- For enabled state (fixed or floating expiry templates) -->
                          <input v-else
                                 type="date" 
                                 v-model="formData.expirationDate" 
                                 class="form-control" 
                                 required
                                 :placeholder="selectedTemplate && selectedTemplate.licenseExpiry !== 'Never' ? 'Select expiration date' : ''">
                        </div>
                      </div>
                      
                      <div class="form-group">
                        <div class="col-sm-3">
                          <label class="control-label">Signing Key</label>
                        </div>
                        <div class="col-sm-6">
                          <select v-model="formData.signingKey" class="form-control" required>
                            <option value="" disabled selected>Select Signing Key</option>
                            <option v-for="key in pickKey" :key="key.id" :value="key.id">{{key.name}}</option>
                          </select>
                        </div>
                      </div>
                      
                      <div class="form-group">
                        <div class="col-sm-3">
                          <label class="control-label">Hardware Locking</label>
                        </div>
                        <div class="col-sm-6">
                          <select v-model="formData.hardwareLocking" class="form-control" required>
                            <option value="" disabled selected>Select Option</option>
                                                        <option value="true">True</option>
                            <option value="false">False</option>
                          </select>
                        </div>
                      </div>
                      
                      <!-- Dynamic fields based on template -->
                      <template v-if="selectedTemplate">
                        <div class="form-group" v-for="field in templateFields" :key="field.name">
                          <div class="col-sm-3">
                            <label class="control-label">{{field.label}}</label>
                          </div>
                          <div class="col-sm-6">
                            <input 
                              :type="field.type" 
                              class="form-control" 
                              v-model="formValues[field.name]" 
                              :required="field.required">
                          </div>
                        </div>
                      </template>
                    </div>

                    <div class="form-group" v-show="formData.templateId">
                      <div class="col-sm-12">
                        <div class="card shadow mb-4">
                          <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary" style="font-family: 'Open Sans';color: #ac2925;font-size: 14px">
                              Additional License Properties
                            </h6>
                          </div>
                          <div class="card-body">
                            <table class="table table-hover table-bordered">
                              <thead>
                                <tr>
                                  <th>Property</th>
                                  <th>Value</th>
                                  <th>Action</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr v-for="(prop, index) in licenseProperties" :key="'create-prop-' + index">
                                  <td>
                                    <input v-model="prop.name" type="text" class="form-control" readonly>
                                  </td>
                                  <td>
                                    <input v-model="prop.value" type="text" class="form-control">
                                  </td>
                                  <td>
                                    <button type="button" @click="removeLicenseProperty(index)" class="btn btn-danger btn-sm">
                                      <i class="fas fa-trash-alt"></i>
                                    </button>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <button type="button" @click="addLicenseProperty" class="btn btn-light">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                              </svg>
                              Add Property
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
              
                    <div class="form-group">
                      <div class="col-sm-3"></div>
                      <div class="col-sm-6">
                        <button type="submit" class="request-button form-control" :disabled="!formData.templateId">Create License</button>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="col-sm-2"></div>
              </div>
            </div>
          </div>
        </div>

         <!-- Maintenance & Support Tab -->
        <div id="maintenance" :class="['tab-pane', { active: activeTab === 'maintenance' }]">
          <ul class="nav nav-tabs" style="margin-top: 15px;">
            <li :class="{active: activeMaintenanceTab === 'list'}">
              <a @click="setActiveMaintenanceTab('list')" href="#maintenanceList">List M & S</a>
            </li>
            <li :class="{active: activeMaintenanceTab === 'expiry'}">
              <a @click="setActiveMaintenanceTab('expiry')" href="#maintenanceExpiry">M & S Expiry</a>
            </li>
            <li :class="{active: activeMaintenanceTab === 'add'}">
              <a @click="setActiveMaintenanceTab('add')" href="#addMaintenance">Add M & S</a>
            </li>
          </ul>

          <div class="tab-content">
            <!-- Maintenance List Tab -->
            <div id="maintenanceList" :class="['tab-pane', { active: activeMaintenanceTab === 'list' }]">
  <!-- Filter Section -->
  <div class="panel panel-default" style="margin-bottom: 10px; border: none;">
    <div class="panel-heading" style="background-color: #f5f5f5; border: none;">
      <h3 class="panel-title">
        <a data-toggle="collapse" href="#maintenanceFilterCollapse">
          <i class="glyphicon glyphicon-filter"></i> Filters
        </a>
      </h3>
    </div>
    <div id="maintenanceFilterCollapse" class="panel-collapse collapse" style="border: none;">
      <div class="panel-body" style="padding: 10px; border: none;">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group" style="margin-bottom: 10px;">
              <label for="maintenanceLicenseKeyFilter" style="font-weight: 600;">License Key</label>
              <input type="text" id="maintenanceLicenseKeyFilter" class="form-control" v-model="maintenanceFilters.licenseKey" placeholder="Filter by license key">
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group" style="margin-bottom: 10px;">
              <label for="maintenanceProductFilter" style="font-weight: 600;">Product</label>
              <input type="text" id="maintenanceProductFilter" class="form-control" v-model="maintenanceFilters.product" placeholder="Filter by product">
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group" style="margin-bottom: 10px;">
              <label for="maintenanceExpiryDateFilter" style="font-weight: 600;">Expiring On or Before</label>
              <input 
                type="date" 
                id="maintenanceExpiryDateFilter" 
                class="form-control" 
                v-model="maintenanceFilters.expiryDate"
                min="1900-01-01"
                max="2100-12-31">
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12" style="text-align: right;">
            <button type="button" class="btn btn-default" @click="clearMaintenanceFilters">
              <i class="glyphicon glyphicon-refresh"></i> Reset Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="table-responsive" style="margin-top: 20px;">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <th>Company Name</th>
          <th>License Key</th>
          <th>Product</th>
          <th>Product Version</th>
          <th>License Type</th>
          <th>Start Date</th>
          <th>End Date</th>
          <th>M&S Status</th>
          <th>Extended Support</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody v-show="!isFetchingMaintenance">
        <tr v-for="item in displayedMaintenanceItems" :key="'maintenance-' + item.id">
          <td>{{ item.companyName }}</td>
          <td>{{ item.licenseKey }}</td>
          <td>{{ item.product }}</td>
          <td>{{ item.productVersion }}</td>
          <td>{{ item.licenseType }}</td>
          <td>{{ formatDate(item.startDate) }}</td>
          <td>{{ formatDate(item.endDate) }}</td>
          <td>
            <span :class="{
              'text-success': item.status === 'ACTIVE' || item.status === 'Active',
              'text-danger': item.status === 'EXPIRED' || item.status === 'Expired',
              'text-warning': item.status === 'EXPIRING SOON' || item.status === 'Expiring Soon',
              'text-muted': !item.status || (item.status !== 'ACTIVE' && item.status !== 'Active' && item.status !== 'EXPIRED' && item.status !== 'Expired' && item.status !== 'EXPIRING SOON' && item.status !== 'Expiring Soon')
            }">
              {{ item.status }}
            </span>
          </td>
          <td class="text-center">
            <i v-if="item.extendedSupport" class="glyphicon glyphicon-ok text-success" style="font-size: 16px;"></i>
            <i v-else class="glyphicon glyphicon-remove text-danger" style="font-size: 16px;"></i>
          </td>
          <td>
            <div class="btn-group">
              <button class="btn btn-light btn-sm" @click="editMaintenanceItem(item.id)" title="Edit">
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
    <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
  </svg>
</button>
              <button class="btn btn-light btn-sm" @click="deleteMaintenanceModal(item.id)" title="Delete">
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
    <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
    <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
  </svg>
</button>
            </div>
          </td>
        </tr>
      </tbody>
      <tbody v-show="isFetchingMaintenance">
        <tr>
          <td colspan="10" class="text-center">
            <p>Loading maintenance & support data...</p>
          </td>
        </tr>
      </tbody>
      <tbody v-show="!isFetchingMaintenance && displayedMaintenanceItems.length === 0">
        <tr>
          <td colspan="10" class="text-center">
            <p>No maintenance & support items found.</p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!--Expiry tab-->
<div id="maintenanceExpiry" :class="['tab-pane', { active: activeMaintenanceTab === 'expiry' }]">
  <div class="row">
    <div class="col-sm-12" style="margin-bottom: 15px;">
      <div class="alert alert-info">
        <i class="glyphicon glyphicon-info-sign"></i>
        <strong>Note:</strong> This tab shows only maintenance & support items for {{ company.name }} that are expired or expiring within the next 3 months.
      </div>
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-hover">
      <thead>
        <tr>
          <th class="tableHeader" colspan="10" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
            <div class="pull-left" style="line-height: 34px;">Count: {{expiryItemCount}}</div>
            <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
              <span>Page {{expiryCurrentPage}} of {{expiryPages}}</span>
              <ul class="pagination pagination-sm" style="margin: 0; border: none;">
                <li :class="{disabled: expiryCurrentPage === 1}" style="border: none;">
                  <span @click="expiryCurrentPage = 1" style="border: none;">
                    <i class="glyphicon glyphicon-step-backward"></i>
                  </span>
                </li>
                <li :class="{disabled: expiryCurrentPage === 1}" style="border: none;">
                  <span @click="expiryCurrentPage = Math.max(1, expiryCurrentPage - 1)" style="border: none;">
                    <i class="glyphicon glyphicon-chevron-left"></i>
                  </span>
                </li>
                <li :class="{disabled: expiryCurrentPage === expiryPages}" style="border: none;">
                  <span @click="expiryCurrentPage = Math.min(expiryPages, expiryCurrentPage + 1)" style="border: none;">
                    <i class="glyphicon glyphicon-chevron-right"></i>
                  </span>
                </li>
                <li :class="{disabled: expiryCurrentPage === expiryPages}" style="border: none;">
                  <span @click="expiryCurrentPage = expiryPages" style="border: none;">
                    <i class="glyphicon glyphicon-step-forward"></i>
                  </span>
                </li>
              </ul>
              <span style="white-space: nowrap;">Results per page : </span>
              <select class="pages-form-control" @change="onExpiryPageSizeChange($event)" style="margin-left: 5px;">
                <option value="10" :selected="expiryMaxPerPage === 10">10</option>
                <option value="25" :selected="expiryMaxPerPage === 25">25</option>
                <option value="50" :selected="expiryMaxPerPage === 50">50</option>
                <option value="100" :selected="expiryMaxPerPage === 100">100</option>
              </select>
            </div>
          </th>
        </tr>
        <tr>
          <th>Company</th>
          <th>License Key</th>
          <th>Product</th>
          <th>Product Version</th>
          <th>License Type</th>
          <th>Start Date</th>
          <th>End Date</th>
          <th>M&S Status</th>
          <th>Extended Support</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody v-show='!isFetchingMaintenance'>
        <tr v-for="item in paginatedExpiryItems" :key="'company-expiry-' + item.id">
          <td>{{ item.companyName || company.name }}</td>
          <td>{{ item.licenseKey }}</td>
          <td>{{ item.product }}</td>
          <td>{{ item.productVersion }}</td>
          <td>{{ item.licenseType }}</td>
          <td>{{ formatDate(item.startDate) }}</td>
          <td>{{ formatDate(item.endDate) }}</td>
          <td>
            <span :class="getStatusClass(item)">{{ getStatusText(item) }}</span>
          </td>
          <td class="text-center">
            <i v-if="item.extendedSupport" class="glyphicon glyphicon-ok text-success" style="font-size: 16px;"></i>
            <i v-else class="glyphicon glyphicon-remove text-danger" style="font-size: 16px;"></i>
          </td>
          <td>
            <div class="btn-group">
  <button class="btn btn-light btn-sm" @click="editMaintenanceItem(item.id)" title="Edit">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
      <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
    </svg>
  </button>
  <button class="btn btn-light btn-sm" @click="deleteMaintenanceModal(item.id)" title="Delete">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
      <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
      <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
    </svg>
  </button>
</div>
          </td>
        </tr>
      </tbody>
      <tbody v-show="isFetchingMaintenance">
        <tr>
          <td colspan="10" class="text-center">
            <p>Loading expiry data...</p>
          </td>
        </tr>
      </tbody>
      <tbody v-show="!isFetchingMaintenance && paginatedExpiryItems.length === 0">
        <tr>
          <td colspan="10" class="text-center">
            <p>No maintenance items are expiring within 3 months for {{ company.name }}.</p>
          </td>
        </tr>
      </tbody>
      <tfoot>
        <tr>
          <th class="tableHeader" colspan="10" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
            <div class="pull-left" style="line-height: 34px;">Count: {{expiryItemCount}}</div>
            <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
              <span>Page {{expiryCurrentPage}} of {{expiryPages}}</span>
              <ul class="pagination pagination-sm" style="margin: 0; border: none;">
                <li :class="{disabled: expiryCurrentPage === 1}" style="border: none;">
                  <span @click="expiryCurrentPage = 1" style="border: none;">
                    <i class="glyphicon glyphicon-step-backward"></i>
                  </span>
                </li>
                <li :class="{disabled: expiryCurrentPage === 1}" style="border: none;">
                  <span @click="expiryCurrentPage = Math.max(1, expiryCurrentPage - 1)" style="border: none;">
                    <i class="glyphicon glyphicon-chevron-left"></i>
                  </span>
                </li>
                <li :class="{disabled: expiryCurrentPage === expiryPages}" style="border: none;">
                  <span @click="expiryCurrentPage = Math.min(expiryPages, expiryCurrentPage + 1)" style="border: none;">
                    <i class="glyphicon glyphicon-chevron-right"></i>
                  </span>
                </li>
                <li :class="{disabled: expiryCurrentPage === expiryPages}" style="border: none;">
                  <span @click="expiryCurrentPage = expiryPages" style="border: none;">
                    <i class="glyphicon glyphicon-step-forward"></i>
                  </span>
                </li>
              </ul>
              <span style="white-space: nowrap;">Results per page : </span>
              <select class="pages-form-control" @change="onExpiryPageSizeChange($event)" style="margin-left: 5px;">
                <option value="10" :selected="expiryMaxPerPage === 10">10</option>
                <option value="25" :selected="expiryMaxPerPage === 25">25</option>
                <option value="50" :selected="expiryMaxPerPage === 50">50</option>
                <option value="100" :selected="expiryMaxPerPage === 100">100</option>
              </select>
            </div>
          </th>
        </tr>
      </tfoot>
    </table>
  </div>
</div>


            <!--Add tab-->
            <div id="addMaintenance" :class="['tab-pane', { active: activeMaintenanceTab === 'add' }]">
              <div style="margin-top: 20px;">
                <div class="row">
                  <div class="col-sm-2"></div>
                  <div class="col-sm-8">
                    <form class="form-horizontal" @submit.prevent="submitMaintenance">
                      <div class="form-group">
                        <div class="col-sm-3">
                          <label class="control-label">Company Name</label>
                        </div>
                        <div class="col-sm-6">
                          <input type="text" class="form-control" :value="company.name" disabled>
                        </div>
                      </div>

                    <div class="form-group">
    <div class="col-sm-3">
        <label class="control-label">License Key <span class="text-danger">*</span></label>
    </div>
  <div class="col-sm-6">
    <select v-model="selectedLicense" @change="onLicenseSelected()" class="form-control">
  <option value="">Select License</option>
  <option v-for="license in companyLicenses" :key="license.id" :value="license.id">
    {{ license.licenseKey }}
  </option>
</select>
</div>
</div>




                      <div class="form-group">
                  <div class="col-sm-3">
                      <label class="control-label">Product</label>
                  </div>
                  <div class="col-sm-6">
    <select v-model="selectedProduct" @change="onProductSelected()" class="form-control">
        <option value="">Select Product</option>
        <option value="Accolm BAM">Accolm BAM</option>
        <option value="Accolm SSP">Accolm SSP</option>
    </select>
</div>
              </div>

                       <div class="form-group">
    <div class="col-sm-3">
        <label class="control-label">Product Version</label>
    </div>
    <div class="col-sm-6">
    <select v-model="productVersion" @change="onProductVersionSelected()" class="form-control">
        <option value="">Select Version</option>
        <option value="7">7</option>
        <option value="6">6</option>
        <option value="5">5</option>
        <option value="4">4</option>
        <option value="3">3</option>
        <option value="2">2</option>
        <option value="1">1</option>
    </select>
</div>
</div>

                     <div class="form-group">
                    <div class="col-sm-3">
                        <label class="control-label">License Type</label>
                    </div>
                    <div class="col-sm-6">
   <select v-model="selectedLicenseType" @change="onLicenseTypeSelected()" class="form-control">
        <option value="">Select License Type</option>
        <option value="Production">Production</option>
        <option value="Test">Test</option>
        <option value="Standby">Standby</option>
        <option value="Evaluation">Evaluation</option>
    </select>
</div>
                </div>

                      <div class="form-group">
                        <div class="col-sm-3">
                          <label class="control-label">Start Date</label>
                        </div>
                        <div class="col-sm-6">
                          <input type="date" class="form-control" v-model="maintenanceFormData.startDate" required>
                        </div>
                      </div>

                      <div class="form-group">
                        <div class="col-sm-3">
                          <label class="control-label">End Date</label>
                        </div>
                        <div class="col-sm-6">
                          <input type="date" class="form-control" v-model="maintenanceFormData.endDate" required>
                        </div>
                      </div>

                      

                      <div class="form-group">
                    <div class="col-sm-3">
                        <label class="control-label">Extended Support</label>
                    </div>
                   <div class="col-sm-6">
    <div class="checkbox">
        <label style="padding-left: 0px;">
            Enable Extended Support
            <input type="checkbox" 
                   style="position: initial; margin-left: 0px;"
                   v-model="extendedSupport"> 
        </label>
    </div>
</div>
                </div>

                      <div class="form-group">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-6">
                          <button type="submit" class="request-button form-control">Add Maintenance & Support</button>
                        </div>
                      </div>
                                        </form>
                  </div>
                  <div class="col-sm-2"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        





            

        <!-- License Details Modal -->
        <div class="modal fade" id="licenseDetailsModal" tabindex="-1" role="dialog">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h4 class="modal-title">License Details</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
              </div>
              <div class="modal-body" v-if="singleLicense">
                <form class="form-horizontal">
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">License Name</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" v-model="singleLicense.name" disabled class="form-control">
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">License Code</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" v-model="singleLicense.licenseKey" disabled class="form-control">
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Status</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" :value="getLicenseStatusText(singleLicense)" disabled class="form-control">
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Creation Date</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="date" v-model="singleLicense.creationDate" disabled class="form-control">
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Start Date</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="date" v-model="singleLicense.startDate" disabled class="form-control">
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Expiration Date</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="date" v-model="singleLicense.expiryDate" disabled class="form-control">
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Floating Days</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="number" v-model="singleLicense.floatExp" disabled class="form-control">
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3" style="padding-right: 0px;">
                      <label class="control-label">Hardware Locking</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" :value="singleLicense.hardwareLock ? 'Yes' : 'No'" disabled class="form-control">
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Signing Key</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" v-model="singleLicense.signKey && singleLicense.signKey.name" disabled class="form-control">
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-12">
                      <div class="panel panel-default">
                        <div class="panel-heading">License Properties</div>
                        <div class="panel-body">
                          <table class="table table-hover table-bordered">
                            <thead>
                              <tr>
                                <th>Property</th>
                                <th>Value</th>
                                                              </tr>
                            </thead>
                            <tbody>
                              <tr v-for="prop in singleLicense.properties" :key="prop.id">
                                <td>{{prop.name}}</td>
                                <td>{{prop.value}}</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
              <div class="modal-body" v-else>
                <p>Loading license details...</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Edit License Modal -->
        <div class="modal fade" id="editLicenseModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Edit License</h5>
                <button type="button" class="close" style="margin-top: -25px" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body">
                <form class="form-horizontal">
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">License Name *</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" v-model="editFormData.name" class="form-control" required>
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Start Date *</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="date" v-model="editFormData.startDate" class="form-control" required>
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Expiration Date *</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="date" v-model="editFormData.expiryDate" class="form-control" required>
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Floating Days</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="number" v-model="editFormData.floatExp" class="form-control">
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Hardware Locking</label>
                    </div>
                    <div class="col-sm-9">
                      <select v-model="editFormData.hardwareLock" class="form-control">
                        <option :value="true">True</option>
                        <option :value="false">False</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Signing Key</label>
                    </div>
                    <div class="col-sm-9">
                      <select v-model="editFormData.signKey.id" class="form-control">
                        <option v-for="key in pickKey" :key="key.id" :value="key.id">
                          {{key.name}}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Status</label>
                    </div>
                    <div class="col-sm-9">
                      <select v-model="editFormData.status" class="form-control">
                        <option value="ACTIVE">Active</option>
                        <option value="INACTIVE">Inactive</option>
                        <option value="EXPIRED">Expired</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Company</label>
                    </div>
                    <div class="col-sm-9">
                      <select v-model="editFormData.companyId" class="form-control">
                        <option v-for="company in companies" :key="company.id" :value="company.id">
                          {{company.name}}
                        </option>
                      </select>
                    </div>
                  </div>
                  <!-- License Properties Section -->
                  <div class="form-group">
                    <div class="col-sm-12">
                      <div class="card shadow mb-4">
                        <div class="card-header py-3">
                          <h6 class="m-0 font-weight-bold text-primary" style="font-family: 'Open Sans';color: #ac2925;font-size: 14px">
                            License Properties
                          </h6>
                        </div>
                        <div class="card-body">
                          <table class="table table-hover table-bordered">
                            <thead>
                              <tr>
                                <th>Property</th>
                                <th>Value</th>
                                <th>Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr v-for="(prop, index) in editFormData.properties" :key="index">
                                <td>
                                  <input v-model="prop.name" type="text" class="form-control">
                                </td>
                                <td>
                                  <input v-model="prop.value" type="text" class="form-control">
                                </td>
                                <td>
                                  <button type="button" @click="removeEditProperty(index)" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash-alt"></i>
                                  </button>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <button type="button" @click="addEditProperty" class="btn btn-light">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                              <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                            </svg>
                            Add Property
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="request-button" @click="updateLicense">Save Changes</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Edit Success Modal -->
        <div class="modal fade" id="editLicenseSuccess" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>License updated successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- License Update Success Modal -->
        <div class="modal fade" id="licenseUpdateSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>License updated successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Edit Contact Modal -->
        <div class="modal fade" id="editContactModal" tabindex="-1" role="dialog">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Edit Contact</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body">
                <form class="form-horizontal">
                  <div class="form-group" :class="{'has-error': editValidationErrors.firstName}">
                    <div class="col-sm-3">
                      <label class="control-label">First Name</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" v-model="editContactData.firstName" class="form-control" required>
                      <span class="help-block" v-if="editValidationErrors.firstName">{{ editValidationErrors.firstName }}</span>
                    </div>
                  </div>

                  <div class="form-group" :class="{'has-error': editValidationErrors.lastName}">
                    <div class="col-sm-3">
                      <label class="control-label">Last Name</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" v-model="editContactData.lastName" class="form-control" required>
                      <span class="help-block" v-if="editValidationErrors.lastName">{{ editValidationErrors.lastName }}</span>
                    </div>
                  </div>

                  <div class="form-group" :class="{'has-error': editValidationErrors.email}">
                    <div class="col-sm-3">
                      <label class="control-label">Email</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="email" v-model="editContactData.email" class="form-control" required>
                      <span class="help-block" v-if="editValidationErrors.email">{{ editValidationErrors.email }}</span>
                    </div>
                  </div>

                  <div class="form-group" :class="{'has-error': editValidationErrors.phone}">
                    <div class="col-sm-3">
                      <label class="control-label">Phone</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="tel" v-model="editContactData.phone" class="form-control">
                      <span class="help-block" v-if="editValidationErrors.phone">{{ editValidationErrors.phone }}</span>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Mobile Phone</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="tel" v-model="editContactData.mobilePhone" class="form-control">
                    </div>
                  </div>

                  <div class="form-group" :class="{'has-error': editValidationErrors.contactType}">
                    <div class="col-sm-3">
                      <label class="control-label">Contact Type</label>
                    </div>
                    <div class="col-sm-9">
                      <select v-model="editContactData.contactType" class="form-control" required>
                        <option value="">Select Contact Type</option>
                        <option value="PRIMARY">Primary</option>
                        <option value="SECONDARY">Secondary</option>
                        <option value="TECHNICAL">Technical</option>
                        <option value="BILLING">Billing</option>
                      </select>
                      <span class="help-block" v-if="editValidationErrors.contactType">{{ editValidationErrors.contactType }}</span>
                    </div>
                  </div>
                </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="request-button" @click="updateContact">Save Changes</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Delete License Confirmation Modal -->
        <div class="modal fade" id="deleteLicenseModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                <p>Are you sure you want to delete this license?</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" @click="deleteLicense">Delete</button>
              </div>
            </div>
          </div>
        </div>

        <!-- License Delete Success Modal -->
        <div class="modal fade" id="licenseDeleteSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>License deleted successfully!</p>
                              </div>
            </div>
          </div>
        </div>

        <!-- Edit Maintenance Modal -->
<div class="modal fade" id="editMaintenanceModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit Maintenance & Support</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form class="form-horizontal">
          <div class="form-group" :class="{'has-error': editMaintenanceValidationErrors.companyName}">
            <div class="col-sm-3">
              <label class="control-label">Company Name</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="editMaintenanceData.companyName" class="form-control" disabled>
            </div>
          </div>

          <div class="form-group" :class="{'has-error': editMaintenanceValidationErrors.licenseKey}">
            <div class="col-sm-3">
              <label class="control-label">License Key *</label>
            </div>
            <div class="col-sm-9">
              <input type="text" v-model="editMaintenanceData.licenseKey" class="form-control" required>
              <span class="help-block" v-if="editMaintenanceValidationErrors.licenseKey">{{ editMaintenanceValidationErrors.licenseKey }}</span>
            </div>
          </div>

          <div class="form-group" :class="{'has-error': editMaintenanceValidationErrors.product}">
            <div class="col-sm-3">
              <label class="control-label">Product *</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editMaintenanceData.product" class="form-control" required>
                <option value="">Select Product</option>
                <option value="Accolm BAM">Accolm BAM</option>
                <option value="Accolm SSP">Accolm SSP</option>
              </select>
              <span class="help-block" v-if="editMaintenanceValidationErrors.product">{{ editMaintenanceValidationErrors.product }}</span>
            </div>
          </div>

          <div class="form-group" :class="{'has-error': editMaintenanceValidationErrors.productVersion}">
            <div class="col-sm-3">
              <label class="control-label">Product Version *</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editMaintenanceData.productVersion" class="form-control" required>
                <option value="">Select Version</option>
                <option value="7">7</option>
                <option value="6">6</option>
                <option value="5">5</option>
                <option value="4">4</option>
                <option value="3">3</option>
                <option value="2">2</option>
                <option value="1">1</option>
              </select>
              <span class="help-block" v-if="editMaintenanceValidationErrors.productVersion">{{ editMaintenanceValidationErrors.productVersion }}</span>
            </div>
          </div>

          <div class="form-group" :class="{'has-error': editMaintenanceValidationErrors.licenseType}">
            <div class="col-sm-3">
              <label class="control-label">License Type *</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editMaintenanceData.licenseType" class="form-control" required>
                <option value="">Select License Type</option>
                <option value="Production">Production</option>
                <option value="Test">Test</option>
                <option value="Standby">Standby</option>
                <option value="Evaluation">Evaluation</option>
              </select>
              <span class="help-block" v-if="editMaintenanceValidationErrors.licenseType">{{ editMaintenanceValidationErrors.licenseType }}</span>
            </div>
          </div>

          <div class="form-group" :class="{'has-error': editMaintenanceValidationErrors.startDate}">
            <div class="col-sm-3">
              <label class="control-label">Start Date *</label>
            </div>
            <div class="col-sm-9">
              <input type="date" v-model="editMaintenanceData.startDate" class="form-control" required>
              <span class="help-block" v-if="editMaintenanceValidationErrors.startDate">{{ editMaintenanceValidationErrors.startDate }}</span>
            </div>
          </div>

          <div class="form-group" :class="{'has-error': editMaintenanceValidationErrors.endDate}">
            <div class="col-sm-3">
              <label class="control-label">End Date *</label>
            </div>
            <div class="col-sm-9">
              <input type="date" v-model="editMaintenanceData.endDate" class="form-control" required>
              <span class="help-block" v-if="editMaintenanceValidationErrors.endDate">{{ editMaintenanceValidationErrors.endDate }}</span>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Status</label>
            </div>
            <div class="col-sm-9">
              <select v-model="editMaintenanceData.status" class="form-control">
                <option value="">Select Status</option>
                <option value="ACTIVE">Active</option>
                <option value="EXPIRED">Expired</option>
                <option value="EXPIRING SOON">Expiring Soon</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Extended Support</label>
            </div>
            <div class="col-sm-9">
              <div class="checkbox">
                <label style="padding-left: 0px;">
                  Enable Extended Support
                  <input type="checkbox" 
                         style="position: initial; margin-left: 0px;"
                         v-model="editMaintenanceData.extendedSupport"> 
                </label>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="request-button" @click="updateMaintenanceItem">Save Changes</button>
      </div>
    </div>
  </div>
</div>

<!-- Maintenance Update Success Modal -->
<div class="modal fade" id="maintenanceUpdateSuccessModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>Maintenance & Support updated successfully!</p>
      </div>
    </div>
  </div>
</div>

<!-- Maintenance Update Error Modal -->
<div class="modal fade" id="maintenanceUpdateErrorModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Error</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
        <p>An error occurred while updating maintenance & support. Please try again.</p>
      </div>
    </div>
  </div>
</div>

        <!-- Expiry Warning Modal -->
        <div class="modal fade" id="expiryWarningModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Information</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-info-circle text-info fa-3x mb-3"></i>
                <p>This template is configured to never expire. The expiration date field is disabled.</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
              </div>
            </div>
          </div>
        </div>

<!-- Delete Maintenance Confirmation Modal -->
<div class="modal fade" id="deleteMaintenanceModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Delete Key</h5>
                        <button type="button" class="close" data-dismiss="modal" style="margin-top: -27px;">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete this maintenance record?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" @click="deleteKey(did)">Delete</button>
                    </div>
                </div>
            </div>
        </div>

<!-- Maintenance Delete Success Modal -->
<div class="modal fade" id="maintenanceDeleteSuccessModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>Maintenance & Support item deleted successfully!</p>
      </div>
    </div>
  </div>
</div>

<!-- Maintenance Delete Error Modal -->
<div class="modal fade" id="maintenanceDeleteErrorModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Error</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
        <p>An error occurred while deleting the maintenance & support item. Please try again.</p>
      </div>
    </div>
  </div>
</div>


        <!-- Success Modal for create licenses page -->
        <div class="modal fade" id="createSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>License created successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Modal for create licenses page -->
        <div class="modal fade" id="errorModalcreate" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p>An error occurred while creating the license. Please try again.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Modal for licenses -->
        <div class="modal fade" id="licenseErrorModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p>An error occurred. Please try again.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Update Success Modal -->
        <div class="modal fade" id="contactUpdateSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>Contact updated successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteContactModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                <p>Are you sure you want to delete this contact?</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" @click="deleteContact">Delete</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Delete Success Modal -->
        <div class="modal fade" id="contactDeleteSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>Contact deleted successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Success Modal -->
        <div class="modal fade" id="contactSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>Contact added successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Modal -->
        <div class="modal fade" id="contactErrorModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <span class="glyphicon glyphicon-remove-circle text-danger" style="font-size: 48px;"></span>
                <p>An error occurred. Please try again.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Maintenance Success Modal -->
        <div class="modal fade" id="maintenanceSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>Maintenance & Support added successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Maintenance Error Modal -->
        <div class="modal fade" id="maintenanceErrorModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p>An error occurred while adding maintenance & support. Please try again.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Extended Support Success Modal -->
        <div class="modal fade" id="extendedSupportSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>Extended Support added successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Extended Support Error Modal -->
        <div class="modal fade" id="extendedSupportErrorModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p>An error occurred while adding extended support. Please try again.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
   </div>
  </div>
 </div>
</div>
  `,
});
