package com.accolm.licenseManager.RestInterface;

import java.util.List;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.DELETE;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.accolm.licenseManager.Entities.Company;
import com.accolm.licenseManager.Entities.MaintenanceSupport;
import com.accolm.licenseManager.Services.MaintenanceSupportService;
import com.accolm.licenseManager.Services.MaintenanceSupportServiceImpl;

@Path("maintenance-supports")
public class MaintenanceSupportRI {

	// a maintenace support service layer for separation of concerns.
	private MaintenanceSupportService impl = new MaintenanceSupportServiceImpl();

	// Endpoint to Create a new maintenance suppport, /api/maintenance-suppport
	// and also the add m&s returns error 500
	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response createMaintenanceSupport(MaintenanceSupport request) {
		MaintenanceSupport result = impl.createMaintenanceSupport(request);
		return Response.status(Response.Status.CREATED).entity(result).build();
	}

	// Endpoint to Read all maintenance-supports, /api/maintenance-supports.
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllMaintenanceSupportRequests() {
		List<MaintenanceSupport> result = impl.getAllMaintenanceSupportRequests();
		return Response.status(Response.Status.OK).entity(result).build();
	}

	// contacts endpoint : list maintenance-supports accordint to id
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Path("{id}")
	public Response listMaintenanceSupportRequestById(@PathParam("id") int id) {
		MaintenanceSupport result = impl.getAllMaintenanceSupportRequestById(id);
		return Response.status(Response.Status.OK).entity(result).build();
	}

	// also wheres the count api?

	// Count total licenses
	@GET
	@Path("count")
	@Produces(MediaType.TEXT_PLAIN)
	public Response maintenanceSupportRequests() {
		long count = impl.getMaintenanceSupportRequestsCount();
		return Response.ok(count).build();
	}
	
	
	// Update maintenance support
	@PUT
	@Path("{id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateMaintenanceSupport(@PathParam("id") int id, MaintenanceSupport request) {
		request.setId(id); // make sure the ID from the URL is used
		boolean success = impl.updateMaintenanceSupport(request);
		if (success) {
			return Response.ok("MaintenanceSupport updated successfully.").build();
		} else {
			return Response.status(Response.Status.NOT_FOUND).entity("Update failed. ID may not exist.").build();
		}
	}
	
	// Delete maintenance support
	@DELETE
	@Path("{id}")
	@Produces(MediaType.TEXT_PLAIN)
	public Response deleteMaintenanceSupport(@PathParam("id") int id) {
		boolean success = impl.deleteMaintenanceSupport(id);
		if (success) {
			return Response.ok("MaintenanceSupport deleted successfully.").build();
		} else {
			return Response.status(Response.Status.NOT_FOUND).entity("Delete failed. ID not found.").build();
		}
	}
	

}
