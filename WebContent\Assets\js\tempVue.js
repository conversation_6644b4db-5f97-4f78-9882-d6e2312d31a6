var PaginationStrip = Vue.component("pagination-strip", {
  props: ["currentPage", "pages", "maxPerPage", "itemCount"],
  methods: {
    async onChange(event) {
      const numberToGet = parseInt(event.target.value);
      this.$emit("update:maxPerPage", numberToGet);
      // Reset to page 1 when changing results per page
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onNext() {
      if (this.currentPage < this.pages) {
        this.$emit("update:currentPage", this.currentPage + 1);
        this.$emit("refresh");
      }
    },
    async onPrev() {
      if (this.currentPage > 1) {
        this.$emit("update:currentPage", this.currentPage - 1);
        this.$emit("refresh");
      }
    },
    async onFirst() {
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onLast() {
      this.$emit("update:currentPage", this.pages);
      this.$emit("refresh");
    },
  },
  template: `
    <th class="tableHeader" colspan="9" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
      <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
      <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
        <span>Page {{currentPage}} of {{pages}}</span>
        <ul class="pagination pagination-sm" style="margin: 0; border: none;">
          <li :class="{disabled: currentPage === 1}">
            <span @click="onFirst()"><i class="glyphicon glyphicon-step-backward"></i></span>
          </li>
          <li :class="{disabled: currentPage === 1}">
            <span @click="onPrev()"><i class="glyphicon glyphicon-chevron-left"></i></span>
          </li>
          <li :class="{disabled: currentPage === pages}">
            <span @click="onNext()"><i class="glyphicon glyphicon-chevron-right"></i></span>
          </li>
          <li :class="{disabled: currentPage === pages}">
            <span @click="onLast()"><i class="glyphicon glyphicon-step-forward"></i></span>
          </li>
        </ul>
        <select class="pages-form-control" @change="onChange($event)">
          <option value="10" :selected="maxPerPage === 10">10</option>
          <option value="25" :selected="maxPerPage === 25">25</option>
          <option value="50" :selected="maxPerPage === 50">50</option>
          <option value="100" :selected="maxPerPage === 100">100</option>
        </select>
      </div>
    </th>
  `,
});

var displayTemplates = Vue.component("display-templates", {
  components: {
    "pagination-strip": PaginationStrip,
  },
  data() {
    return {
      showDeleteModal: false,
      showSuccessModal: false,
      showErrorModal: false,
      errorMessage: "",
      allTemplates: [],
      templates: [],
      hasDeleted: false,
      templateToDelete: "",
      search: "",
      did: 0,
      newTemp: {
        id: 0,
        name: "",
        licenseExpiry: " ",
        floatExpiry: 0,
        expiryType: "never",
        templateProperties: [{}],
      },
      rowCount: 1,
      disableBtn: true,
      noShow: false,
      isFetching: true,
      currentPage: 1,
      pages: 0,
      itemCount: 0,
      maxPerPage: 10,
      sortOrder: "asc",
      sortField: "id",
      selectedFile: null,
    };
  },
  methods: {
    submitChanges() {
      this.updateTemplate(this.newTemp);
    },
    addProp() {
      this.newTemp.templateProperties.push({
        name: "",
        value: "",
      });
      this.rowCount++;
    },

    removeProp(index) {
      if (this.newTemp.templateProperties.length > 1) {
        this.newTemp.templateProperties.splice(index, 1);
        this.rowCount--;
      }
    },
    formatExpiryDate(expiry) {
      if (!expiry) return "Never";
      if (expiry === "None" || expiry === " " || expiry === "") return "Never";

      // Handle the new expiry type labels
      if (expiry === "Never") return "Never";
      if (expiry === "Fixed") return "Fixed";
      if (expiry === "Floating days from creation date")
        return "Floating days from creation date";

      // Handle legacy date formats (if any existing data has actual dates)
      if (expiry.includes("-")) {
        const date = new Date(expiry);
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        const year = date.getFullYear();
        return `${month}/${day}/${year}`;
      }

      return expiry;
    },

    async displayData() {
      this.isFetching = true;
      console.log("Fetching templates for page:", this.currentPage); // Debug log

      try {
        const [res, resCount] = await Promise.all([
          axios.get(`${this.apiBaseUrl}/templates/all`),
          axios.get(`${this.apiBaseUrl}/templates/count`),
        ]);

        if (res.status === 200 && resCount.status === 200) {
          this.itemCount = resCount.data;
          this.pages = Math.ceil(resCount.data / this.maxPerPage);

          // Reset to page 1 if current page is beyond available pages
          if (this.currentPage > this.pages && this.pages > 0) {
            this.currentPage = 1;
          }

          console.log("Received templates:", res.data.length); // Debug log
          console.log("Total count:", this.itemCount); // Debug log

          // Check if the server returned all results instead of just the page
          if (res.data.length > this.maxPerPage) {
            console.log(
              "Server returned all results, applying client-side pagination"
            );
            // Store all templates
            this.allTemplates = res.data;
            // Apply client-side pagination
            const startIndex = (this.currentPage - 1) * this.maxPerPage;
            const endIndex = startIndex + this.maxPerPage;
            this.templates = this.allTemplates.slice(startIndex, endIndex);
          } else {
            // Server handled pagination correctly
            this.templates = res.data;
          }

          console.log("Templates loaded for page:", this.templates.length);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        this.isFetching = false;
      }

      return Promise.resolve();
    },

    toggleSort() {
      this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
    },
    async deleteTemp(id) {
      try {
        const response = await axios.delete(
          `${this.apiBaseUrl}/templates/delete/${id}`
        );

        if (response.status === 200) {
          // Hide delete modal first
          this.hideModal("deleteTempModal");

          // Wait for modal to close, then refresh data
          setTimeout(async () => {
            await this.displayData();

            // Show success modal
            this.showModal("successModal");

            // Auto-hide success modal
            setTimeout(() => {
              this.hideModal("successModal");
            }, 3000);
          }, 500);
        }
      } catch (error) {
        console.error("Error deleting template:", error);
        this.hideModal("deleteTempModal");
        setTimeout(() => {
          this.showModal("errorModal");
        }, 300);
      }
    },

    update(id) {
      axios.get(`${this.apiBaseUrl}/templates/get/${id}`).then((res) => {
        this.newTemp = res.data;

        // Determine the expiry type based on the template data
        if (
          !this.newTemp.licenseExpiry ||
          this.newTemp.licenseExpiry === " " ||
          this.newTemp.licenseExpiry === "None" ||
          this.newTemp.licenseExpiry === "Never"
        ) {
          if (this.newTemp.floatExpiry > 0) {
            this.newTemp.expiryType = "floating-firstuse";
          } else {
            this.newTemp.expiryType = "never";
          }
        } else if (
          this.newTemp.licenseExpiry === "" ||
          this.newTemp.licenseExpiry === "Fixed"
        ) {
          // Empty string or "Fixed" indicates fixed date type
          this.newTemp.expiryType = "fixed";
        } else if (
          this.newTemp.licenseExpiry === "Floating days from creation date"
        ) {
          // New floating expiry type
          this.newTemp.expiryType = "floating-firstuse";
        } else {
          // Has a specific date value - could be legacy fixed date
          this.newTemp.expiryType = "fixed";
        }

        if (res.data.templateProperties.length === 0) {
          this.newTemp.templateProperties.push({
            name: "",
            value: "",
          });
        }
      });
    },

    async updateTemplate(newTemp) {
      console.log("=== UPDATE TEMPLATE START ===");

      // Basic validation
      if (!newTemp.name || newTemp.name.trim() === "") {
        this.errorMessage = "Template name is required.";
        this.showModal("customErrorModal");
        return;
      }

      console.log("Checking for duplicate template name:", newTemp.name);

      // Check for duplicate template name
      const isDuplicate = await this.checkDuplicateTemplateName(
        newTemp.name,
        newTemp.id
      );

      console.log("Duplicate check completed. Result:", isDuplicate);

      if (isDuplicate) {
        console.log("🚨 DUPLICATE DETECTED - Triggering modal");

        // Close any form modals that might be open
        $("#createFormModal, #editFormModal").modal("hide");

        // Wait a moment then show duplicate modal
        setTimeout(() => {
          this.showModal("onDuplicateTemplateModal");
        }, 200);

        console.log("=== UPDATE TEMPLATE END (DUPLICATE) ===");
        return;
      }

      console.log("No duplicate found, proceeding with template creation...");

      // Rest of your existing updateTemplate code...
      const templateData = { ...newTemp };

      if (
        templateData.expiryType === "none" ||
        templateData.expiryType === "never"
      ) {
        templateData.licenseExpiry = "Never";
        templateData.floatExpiry = 0;
      } else if (templateData.expiryType === "fixed") {
        templateData.licenseExpiry = "Fixed";
        templateData.floatExpiry = 0;
      } else if (templateData.expiryType === "floating-firstuse") {
        templateData.licenseExpiry = "Floating days from creation date";
      }

      console.log("Submitting template data:", JSON.stringify(templateData));
      var myJSON = JSON.stringify(templateData);

      try {
        const response = await axios.put(
          `${this.apiBaseUrl}/templates/update`,
          myJSON,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        console.log("API response:", response);
        if (
          (response.status === 200 || response.status === 201) &&
          response.data
        ) {
          console.log("Success! Processing success actions...");

          // Reset form fields first
          this.resetFormFields();

          // Refresh the template list
          await this.displayData();

          // Clean up any existing modals
          $(".modal").modal("hide");

          // Switch to the list tab
          setTimeout(() => {
            // Remove active class from all tabs and tab panes
            $(".nav-tabs li").removeClass("active");
            $(".tab-pane").removeClass("active");

            // Add active class to list tab and its content
            $('a[href="#list"]').parent().addClass("active");
            $("#list").addClass("active");

            // Trigger the tab show event
            $('a[href="#list"]').tab("show");

            // Show success modal after tab switch
            setTimeout(() => {
              this.showModal("onSuccessModal");

              // Auto-hide after 3 seconds
              setTimeout(() => {
                this.hideModal("onSuccessModal");
              }, 3000);
            }, 300);
          }, 200);
        }
      } catch (error) {
        console.error("Error creating template:", error);

        // Clean up modals first
        $(".modal").modal("hide");

        // Show error modal
        setTimeout(() => {
          this.errorMessage =
            error.response?.data ||
            "An error occurred while creating the template.";
          this.showModal("customErrorModal");
        }, 200);
      }

      console.log("=== UPDATE TEMPLATE END ===");
    },

    resetFormFields() {
      // Reset to default values
      this.newTemp = {
        id: 0,
        name: "",
        licenseExpiry: " ",
        floatExpiry: 0,
        expiryType: "never",
        templateProperties: [{ name: "", value: "" }],
      };
      this.rowCount = 1;
    },
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file && file.name.endsWith(".ltp")) {
        this.selectedFile = file;
        const fileLabel = document.getElementById("selectedFileName");
        if (fileLabel) {
          fileLabel.textContent = file.name;
        }
      } else {
        // Use the helper method instead of direct Bootstrap modal
        this.showModal("importErrorModal");
        this.resetImportField();
      }
    },

    // Check for duplicate template names
    async checkDuplicateTemplateName(templateName, templateId = 0) {
      try {
        console.log(
          "Checking for duplicate template name:",
          templateName,
          "excluding ID:",
          templateId
        );
        const res = await axios.get(`${this.apiBaseUrl}/templates/all`);
        if (res.status === 200) {
          // Check if a template with the same name already exists (excluding current template if editing)
          const duplicate = res.data.find(
            (template) =>
              template.name.toLowerCase().trim() ===
                templateName.toLowerCase().trim() && template.id !== templateId
          );
          console.log(
            "Duplicate check result:",
            duplicate ? `Found: ${duplicate.name}` : "None found"
          );
          return duplicate !== undefined;
        }
      } catch (error) {
        console.error("Error checking for duplicate template names:", error);
      }
      return false;
    },

    importTemplate() {
      if (!this.selectedFile) return;

      const formData = new FormData();
      formData.append("file", this.selectedFile);

      axios
        .post(`${this.apiBaseUrl}/templates/file/import`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then(async (response) => {
          console.log("Import response:", response);

          if (response.status >= 200 && response.status < 300) {
            // Reset the import field
            this.resetImportField();

            // Show success modal immediately
            this.showModal("importSuccessModal");

            // Reset pagination and clear existing data
            this.currentPage = 1;
            this.templates = [];
            this.allTemplates = [];

            // Wait a moment for the backend to fully process
            setTimeout(async () => {
              try {
                // Force refresh with cache busting
                const timestamp = new Date().getTime();
                const [res, resCount] = await Promise.all([
                  axios.get(`${this.apiBaseUrl}/templates/all`),
                  axios.get(
                    `${this.apiBaseUrl}/templates/count?_t=${timestamp}`
                  ),
                ]);

                if (res.status === 200 && resCount.status === 200) {
                  this.itemCount = resCount.data;
                  this.pages = Math.ceil(resCount.data / this.maxPerPage);

                  if (res.data.length > this.maxPerPage) {
                    this.allTemplates = res.data;
                    const startIndex = (this.currentPage - 1) * this.maxPerPage;
                    const endIndex = startIndex + this.maxPerPage;
                    this.templates = this.allTemplates.slice(
                      startIndex,
                      endIndex
                    );
                  } else {
                    this.templates = res.data;
                  }

                  console.log(
                    "Templates refreshed after import:",
                    this.templates.length
                  );
                }
              } catch (error) {
                console.error("Error refreshing after import:", error);
              }
            }, 1000); // Wait 1 second for backend processing

            // Auto-hide success modal after 3 seconds
            setTimeout(() => {
              this.hideModal("importSuccessModal");
            }, 3000);
          } else {
            this.showModal("importErrorModal");
            this.resetImportField();
          }
        })
        .catch((error) => {
          console.error("Import error:", error);
          this.showModal("importErrorModal");
          this.resetImportField();
        });
    },

    showModal(modalId) {
      console.log(`Showing modal: ${modalId}`);

      // Check if modal exists
      const modalElement = $(`#${modalId}`);
      if (modalElement.length === 0) {
        console.error(`Modal ${modalId} not found`);
        return;
      }

      // Clean up any existing modals
      $(".modal").modal("hide");
      $(".modal-backdrop").remove();
      $("body").removeClass("modal-open").css("padding-right", "");

      // Show the modal after cleanup
      setTimeout(() => {
        modalElement.modal("show");

        // Verify it showed
        setTimeout(() => {
          if (modalElement.hasClass("show") || modalElement.is(":visible")) {
            console.log(`Modal ${modalId} successfully shown`);
          } else {
            console.error(`Modal ${modalId} failed to show`);
          }
        }, 200);
      }, 300);
    },

    hideModal(modalId) {
      const modalElement = $(`#${modalId}`);
      if (modalElement.length) {
        // Properly hide the modal
        modalElement.modal("hide");

        // Handle the hidden event to clean up properly
        modalElement.on("hidden.bs.modal", function () {
          // Clean up after hiding
          $(".modal-backdrop").remove();
          $("body").removeClass("modal-open").css("padding-right", "");

          // Reset aria-hidden to true when modal is actually hidden
          $(this).attr("aria-hidden", "true");

          // Remove the event handler to prevent memory leaks
          $(this).off("hidden.bs.modal");
        });
      }
    },

    resetImportField() {
      this.selectedFile = null;
      const input = document.getElementById("templateFile");
      if (input) {
        input.value = "";
        const label = input.nextElementSibling;
        if (label) {
          label.textContent = "Choose template file...";
        }
      }
    },

    deleteModal(id) {
      this.did = id;
      const template = this.templates.find((temp) => temp.id === id);
      this.templateToDelete = template ? template.name : "Unknown template";

      // Use Vue's nextTick to ensure data is updated before showing modal
      this.$nextTick(() => {
        this.showModal("deleteTempModal");
      });
    },

    reloadPage() {
      window.location.reload(true);
    },
    closeModal() {
      document.getElementById("closeBtn").click();
    },
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },
    sortedTemplates() {
      // Sort by ID by default to ensure new templates appear at the end
      if (this.sortField === "name") {
        // Only apply name sorting if user explicitly clicks on name column
        const sorted = [...this.templates].sort((a, b) => {
          const modifier = this.sortOrder === "asc" ? 1 : -1;
          if (a[this.sortField] < b[this.sortField]) return -1 * modifier;
          if (a[this.sortField] > b[this.sortField]) return 1 * modifier;
          return 0;
        });
        return sorted;
      } else {
        // Default sort by ID (ascending)
        return [...this.templates].sort((a, b) => a.id - b.id);
      }
    },
  },

  watch: {
    maxPerPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        // Reset to page 1 when maxPerPage changes
        this.currentPage = 1;
        this.displayData();
      }
    },
  },

  mounted() {
    this.displayData();

    // Tab change event listener
    $('a[data-toggle="tab"]').on("shown.bs.tab", (e) => {
      const targetTab = $(e.target).attr("href");
      if (targetTab === "#list") {
        this.displayData();
      }
    });

    // Improved modal event handlers with proper accessibility
    $(document).on("show.bs.modal", ".modal", function (e) {
      console.log("Modal showing:", this.id);
      // Remove aria-hidden when showing
      $(this).removeAttr("aria-hidden");
      // Ensure proper z-index
      $(this).css("z-index", 1050);
    });

    $(document).on("shown.bs.modal", ".modal", function (e) {
      console.log("Modal shown:", this.id);
      // Ensure aria-hidden is removed after Bootstrap processes
      $(this).removeAttr("aria-hidden");
      // Ensure backdrop is properly positioned
      $(".modal-backdrop").css("z-index", 1040);
    });

    $(document).on("hide.bs.modal", ".modal", function (e) {
      console.log("Modal hiding:", this.id);
    });

    $(document).on("hidden.bs.modal", ".modal", function (e) {
      console.log("Modal hidden:", this.id);
      // Set aria-hidden to true only when modal is completely hidden
      $(this).attr("aria-hidden", "true");
      // Clean up all modal artifacts
      $(".modal-backdrop").remove();
      $("body").removeClass("modal-open").css("padding-right", "");
    });

    // Force clean up any stuck modals on page load
    $(".modal-backdrop").remove();
    $("body").removeClass("modal-open").css("padding-right", "");

    // Ensure all modals have proper initial aria-hidden state
    $(".modal").attr("aria-hidden", "true");
  },

  template: `
    <div class="main-section" style="font-family: 'Open Sans', sans-serif;">
        <div class="container">
            <div class="row">
                <div class="col-sm-12" style="text-align: center">
                    <h1>Templates</h1>
                </div>
                <div class="col-sm-12">
                    <!-- Tabs Navigation -->
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="active"><a href="#list" role="tab" data-toggle="tab">List Templates</a></li>
                        <li><a href="#create" role="tab" data-toggle="tab">Create Template</a></li>
                        <li><a href="#import" role="tab" data-toggle="tab">Import Template</a></li>
                    </ul>

                    <!-- Tabs Content -->
                    <div class="tab-content">
                        <!-- List Tab -->
                        <div class="tab-pane active" id="list">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <pagination-strip
                                                :current-page.sync="currentPage"
                                                :pages="pages"
                                                :max-per-page.sync="maxPerPage"
                                                :item-count="itemCount"
                                                @refresh="displayData"
                                            ></pagination-strip>
                                        </tr>
                                        <tr>
                                            <th @click="toggleSort" style="cursor: pointer;">
                                                Name 
                                                <i :class="['fas', sortOrder === 'asc' ? 'fa-sort-up' : 'fa-sort-down']"></i>
                                            </th>
                                            <th>License Expiration</th>
                                            <th>Floating Expiration</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody v-show='!isFetching'>
                                        <tr v-for='temp in sortedTemplates' :key="temp.id">
                                            <td style="cursor: pointer;" @click="update(temp.id)" data-toggle="modal" data-target="#viewModal">
                                                <a href="#">{{ temp.name }}</a>
                                            </td>
                                            <td>{{ formatExpiryDate(temp.licenseExpiry) }}</td>
                                            <td>{{ temp.floatExpiry }}</td>
                                            <td>
                                               <div class="btn-group">
    <button @click="update(temp.id)" class="btn btn-light" data-toggle="modal" data-target="#editFormModal">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
        </svg>
    </button>
    <button data-toggle="modal" data-target="#deleteTempModal" @click="deleteModal(temp.id)" class="btn btn-light">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
            <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
        </svg>
    </button>
</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody v-show="isFetching">
                      <tr>
                        <td colspan="8" class="text-center">
                          <p>Loading templates...</p>
                        </td>
                      </tr>
                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <pagination-strip
                                                :current-page.sync="currentPage"
                                                :pages="pages"
                                                :max-per-page.sync="maxPerPage"
                                                :item-count="itemCount"
                                                @refresh="displayData"
                                            ></pagination-strip>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                        <!-- Create Template Tab -->
                      
<div class="tab-pane" id="create">
  <div style="margin-top: 20px;">
    <div class="row">
      <div class="col-sm-2"></div>
      <div class="col-sm-8">
        <form class="form-horizontal">
          <div class="form-group">
            <div class="col-sm-3">
              <label class="control-label">Template Name</label>
            </div>
            <div class="col-sm-6">
              <input type="text" v-model="newTemp.name" class="form-control">
            </div>
          </div>

          <!-- License Expiration Section -->
          <div class="form-group">
  <div class="col-sm-3">
    <label class="control-label">License Expiration</label>
  </div>
  <div class="col-sm-6">
    <select v-model="newTemp.expiryType" class="form-control">
      <option value="never">Never</option>
      <option value="fixed">Fixed</option>
      <option value="floating-firstuse">Floating days from creation date</option>
    </select>
  </div>
</div>

         

          <!-- Show days input if any Floating option is selected -->
          <div class="form-group" v-if="newTemp.expiryType === 'floating-creation' || newTemp.expiryType === 'floating-firstuse'">
            <div class="col-sm-3">
              <label class="control-label">Floating Days</label>
            </div>
            <div class="col-sm-6">
              <input type="number" v-model="newTemp.floatExpiry" class="form-control">
            </div>
          </div>

          <!-- properties -->
          <div class="form-group">
  <div class="col-sm-12">
    <div class="card shadow mb-4">
      <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary" style="font-family: 'Open Sans';color: #ac2925;font-size: 14px">Template Properties</h6>
      </div>
      <div class="card-body">
        <table class="table table-hover table-bordered">
          <thead>
            <tr>
              <th>Property</th>
              <th>Value</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(prop, index) in newTemp.templateProperties" :key="index">
              <td>
                <input v-model="prop.name" type="text" class="form-control">
              </td>
              <td>
                <input v-model="prop.value" type="text" class="form-control">
              </td>
              <td>
                <button type="button" @click="removeProp(index)" class="btn btn-danger btn-sm">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <button type="button" @click="addProp" class="btn btn-light">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
          Add Property
        </button>
      </div>
    </div>
  </div>
</div>


          <div class="form-group">
            <div class="col-sm-3"></div>
            <div class="col-sm-6">

              <button @click.prevent="updateTemplate(newTemp)" class="request-button form-control">Save Template</button> 
            </div>
          </div>
        </form>
      </div>
      <div class="col-sm-2"></div>
    </div>
  </div>
</div>


                        <!-- Import Tab -->
                        <div class="tab-pane" id="import">
                            <div class="row content-container">
                                <div class="col-sm-2"></div>
                                <div class="col-sm-8">
                                    <div class="form-horizontal" style="margin-top: 20px; padding: 20px;">
                                        <div class="form-group">
                                            <div class="col-sm-3">
                                                <label class="control-label">Upload Template File</label>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="input">
                                                    <input type="file" 
                                                        id="templateFile" 
                                                        @change="handleFileSelect" 
                                                        accept=".ltp" 
                                                        required 
                                                        class="form-control">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6">
                                                <button @click.prevent="importTemplate" class="request-button form-control" :disabled="!selectedFile">Import Template</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Modals -->
                    <!-- View Modal -->
                    <div class="modal fade" id="viewModal" tabindex="-1" role="dialog">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">Template Details</h4>
                                    <button type="button" data-dismiss="modal" class="close" style="
    margin-top: -27px;
">×</button>
                                </div>
                                <div class="modal-body">
                                    <form class="form-horizontal">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">Template Name</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" v-model="newTemp.name" disabled>
                                            </div>
                                        </div>
                                        <!-- In the view modal, update the License Expiry field -->
<div class="form-group">
    <label class="col-sm-4 control-label">License Expiry</label>
    <div class="col-sm-8">
        <input type="text" class="form-control" 
               :value="!newTemp.licenseExpiry || newTemp.licenseExpiry === 'None' || newTemp.licenseExpiry === ' ' ? 'Never' : newTemp.licenseExpiry" 
               disabled>
    </div>
</div>

                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">Floating Days</label>
                                            <div class="col-sm-8">
                                                <input type="number" class="form-control" v-model="newTemp.floatExpiry" disabled>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading">Template Properties</div>
                                                    <div class="panel-body">
                                                        <table class="table table-hover table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th>Property</th>
                                                                    <th>Value</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr v-for="prop in newTemp.templateProperties">
                                                                    <td>{{prop.name}}</td>
                                                                    <td>{{prop.value}}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>

                   <!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteTempModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" style="margin-top: -27px;">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <p>Are you sure you want to delete the template "{{ templateToDelete }}"?</p>
                <div class="mt-3">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" @click="deleteTemp(did)">Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>

                      <!-- Error Modal -->
                    <div class="modal fade" id="importErrorModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Error</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
        <p>Invalid file format. Please select a .ltp file.</p>
      </div>
    </div>
  </div>
</div>

<!-- Success Modal for Import -->
<div class="modal fade" id="importSuccessModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>Template imported successfully!</p>
      </div>
    </div>
  </div>
</div>

<!-- Error Modal for Import -->
<div class="modal fade" id="importErrorModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Error</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
        <p>Failed to import template. Please try again.</p>
      </div>
    </div>
  </div>
</div>

<!-- Custom Error Modal -->
<div class="modal fade" id="customErrorModal" tabindex="-1">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Error</h5>
        <button type="button" class="close" data-dismiss="modal" style="margin-top: -27px;">&times;</button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
        <p class="lead mb-0">{{errorMessage}}</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editFormModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Edit Template</h4>
                <button type="button" data-dismiss="modal" class="close" style="margin-top: -27px;">×</button>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">Template Name</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" v-model="newTemp.name">
                        </div>
                    </div>

                    <!-- License Expiration Section -->
                    <div class="form-group">
                        <label class="col-sm-3 control-label">License Expiration</label>
                        <div class="col-sm-9">
                            <select v-model="newTemp.expiryType" class="form-control">
                                <option value="never">Never</option>
                                <option value="fixed">Fixed</option>
                                <option value="floating-firstuse">Floating days from creation date</option>
                            </select>
                        </div>
                    </div>

                    <!-- Show days input if floating option is selected -->
                    <div class="form-group" v-if="newTemp.expiryType === 'floating-creation' || newTemp.expiryType === 'floating-firstuse'">
                        <label class="col-sm-3 control-label">Floating Days</label>
                        <div class="col-sm-9">
                            <input type="number" v-model="newTemp.floatExpiry" class="form-control">
                        </div>
                    </div>

                    <!-- Template Properties -->
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h6 class="panel-title">Template Properties</h6>
                                </div>
                                <div class="panel-body">
                                    <table class="table table-hover table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Property</th>
                                                <th>Value</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(prop, index) in newTemp.templateProperties" :key="index">
                                                <td>
                                                    <input v-model="prop.name" type="text" class="form-control">
                                                </td>
                                                <td>
                                                    <input v-model="prop.value" type="text" class="form-control">
                                                </td>
                                                <td>
                                                    <button type="button" @click="removeProp(index)" class="btn btn-danger btn-sm">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <button type="button" @click="addProp" class="btn btn-light">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                        </svg>
                                        Add Property
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn" @click="submitChanges()" data-dismiss="modal" style="background-color: white; border: 2px solid #dc3545; color: #dc3545; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#dc3545'; this.style.color='white';" onmouseout="this.style.backgroundColor='white'; this.style.color='#dc3545';">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal for created templates -->
<div class="modal fade" id="onSuccessModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Success</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
        <p>Operation completed successfully!</p>
      </div>
    </div>
  </div>
</div>

<!-- Add this modal to your templates.html file, preferably near your other modals -->

<!-- Duplicate Template Warning Modal -->
<div class="modal fade" id="onDuplicateTemplateModal" tabindex="-1" role="dialog" aria-labelledby="duplicateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h4 class="modal-title" id="duplicateModalLabel">
                    <i class="fas fa-exclamation-triangle"></i>
                    Duplicate Template Name
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert">
                    <strong>Warning!</strong> A template with this name already exists. 
                    Please choose a different name for your template.
                </div>
                <p>Template names must be unique. Please modify the name and try again.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i>
                    Close
                </button>
                <button type="button" class="btn btn-primary" data-dismiss="modal" onclick="$('#createFormModal input[name=templateName]').focus()">
                    <i class="fas fa-edit"></i>
                    Edit Name
                </button>
            </div>
        </div>
    </div>
</div>



                    <!-- Success Modal -->
                    <div class="modal fade" id="successModal" tabindex="-1" role="dialog">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Success</h5>
                                    <button type="button" class="close" data-dismiss="modal">
                                        <span>&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body text-center">
                                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                                    <p>Operation completed successfully!</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Modal -->
                    <div class="modal fade" id="errorModal" tabindex="-1" role="dialog">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Error</h5>
                                    <button type="button" class="close" data-dismiss="modal">
                                        <span>&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body text-center">
                                    <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                                    <p>An error occurred. Please try again.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
  `,
});

new Vue({
  el: "#template",
  components: {
    "display-templates": displayTemplates,
  },
  template: `
    <div class="wrapper">
      <display-templates></display-templates>
    </div>
  `,
});
