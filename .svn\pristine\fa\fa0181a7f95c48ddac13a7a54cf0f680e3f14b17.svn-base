package com.accolm.licenseManager.DAO;

import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.TypedQuery;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.jce.provider.BrokenJCEBlockCipher.BrokePBEWithMD5AndDES;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import com.accolm.licenseManager.Entities.Authority;
import com.accolm.licenseManager.Entities.User;
import com.accolm.licenseManager.Utils.LocalEntityManagerFactory;

public class AuthorityDaoImpl implements AuthorityDao {

	Logger logger = LogManager.getLogger(AuthorityDaoImpl.class);
	
	
	

	@Override
	public void insertUserAuthorities(User user) {

		EntityManager em = LocalEntityManagerFactory.createEntityManager();

		Authority userAuthority = null;
		// save
		try {
			logger.info("inserting user authorities...");
			// managed transactions by RESOURCE_LOCAL
			EntityTransaction tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.

			// create user authority
			String userRole = user.getRole().toUpperCase();
			userAuthority = switch (userRole) {

			case "ADMIN" -> new Authority(userRole);
			case "USER" -> new Authority(userRole);

			default -> null;

			};

			userAuthority.setUsername(user.getUsername());
			em.merge(userAuthority);

			tx.commit(); // commit the transaction
			logger.info("user authority saved successfully.");
		} catch (Exception e) {
			logger.error("Error saving user authority ", e);
		} finally {
			em.close(); // close persistence context.
		}

	}

}
